{"root": ["./src/app.tsx", "./src/main.tsx", "./src/router.tsx", "./src/vite-env.d.ts", "./src/components/addprocessblockmodal.tsx", "./src/components/addproductmodal.tsx", "./src/components/checkbox.tsx", "./src/components/confirmationmodalui.tsx", "./src/components/customnode.tsx", "./src/components/datefield.tsx", "./src/components/dynamiccomponent.tsx", "./src/components/dynamictable.tsx", "./src/components/errorboundary.tsx", "./src/components/errormessage.tsx", "./src/components/fieldcomponent.tsx", "./src/components/filtermodal.tsx", "./src/components/footer.tsx", "./src/components/formstatuscomponent.tsx", "./src/components/formstepbar.tsx", "./src/components/header.tsx", "./src/components/modalcomponent.tsx", "./src/components/multiselect.tsx", "./src/components/offlinemsg.tsx", "./src/components/productinfocomponent.tsx", "./src/components/productrouting.tsx", "./src/components/protectedroute.tsx", "./src/components/publicroute.tsx", "./src/components/radiobutton.tsx", "./src/components/selectflowdropdown.tsx", "./src/components/selectinput.tsx", "./src/components/sidebar.tsx", "./src/components/snackbarcomponent.tsx", "./src/components/textarea.tsx", "./src/components/textinput.tsx", "./src/components/usermenue.tsx", "./src/components/workorderfilter.tsx", "./src/components/workorderform.tsx", "./src/components/routing/connectionhandler.tsx", "./src/components/routing/connectionmodal.tsx", "./src/components/routing/controls.tsx", "./src/components/routing/customedge.tsx", "./src/components/routing/customnode.tsx", "./src/components/routing/draggablenode.tsx", "./src/components/routing/edgeoperationspanel.tsx", "./src/components/routing/exportmodal.tsx", "./src/components/routing/flowcanvas.tsx", "./src/components/routing/importmodal.tsx", "./src/components/routing/leftsidebar.tsx", "./src/components/routing/modalcomponents.tsx", "./src/components/routing/nodepalette.tsx", "./src/components/routing/rightsidebar.tsx", "./src/components/routing/routeinfo.tsx", "./src/components/routing/availablenodes.ts", "./src/components/routing/initialschema.ts", "./src/config/data.ts", "./src/config/route.tsx", "./src/context/authcontext.tsx", "./src/context/snackbarcontext.tsx", "./src/hooks/useembedurl.ts", "./src/hooks/useeventqueue.ts", "./src/hooks/useform.ts", "./src/hooks/useloginlogout.ts", "./src/hooks/usenetworkstatus.ts", "./src/hooks/useprocessblocks.ts", "./src/hooks/useproductswithoutrouting.ts", "./src/hooks/usesaverouting.ts", "./src/hooks/usescreensize.ts", "./src/hooks/useuploadaoifiles.tsx", "./src/hooks/useuploadworkorderfiles.tsx", "./src/hooks/useworkorders.ts", "./src/interfaces/form-config.api.interface.ts", "./src/interfaces/form.element.interface.ts", "./src/interfaces/form.field.interface.ts", "./src/interfaces/route.inteface.ts", "./src/pages/dashboard.tsx", "./src/pages/dynamicform.tsx", "./src/pages/eventlist.tsx", "./src/pages/formconfiglist.tsx", "./src/pages/formconfigtable.tsx", "./src/pages/formcreator.tsx", "./src/pages/notfound.tsx", "./src/pages/pastevent.tsx", "./src/pages/processroutelist.tsx", "./src/pages/processroutelist2.tsx", "./src/pages/productroute.tsx", "./src/pages/productroutingpage.tsx", "./src/pages/reportpage.tsx", "./src/pages/routeconfigcreation.tsx", "./src/pages/signin.tsx", "./src/pages/upload.tsx", "./src/pages/workordertable.tsx", "./src/pages/index.ts", "./src/services/routingapiservice.ts", "./src/services/schemagenerator.ts", "./src/utils/authutils.ts", "./src/utils/axiosinstance.ts", "./src/utils/componentregistery.ts", "./src/utils/formactions.ts", "./src/utils/formhandlers.ts", "./src/utils/formhelper.ts", "./src/utils/jsonvalidator.ts", "./src/utils/muitheme.ts", "./src/utils/forms/baseformhandle.ts", "./src/utils/forms/form10handler.ts", "./src/utils/forms/form11handler.ts", "./src/utils/forms/form12handler.ts", "./src/utils/forms/form1handler.ts", "./src/utils/forms/form2handler.ts", "./src/utils/forms/form3handler.ts", "./src/utils/forms/form4handler.ts", "./src/utils/forms/form5handler.ts", "./src/utils/forms/form6handler.ts", "./src/utils/forms/form7handler.ts", "./src/utils/forms/form8handler.ts", "./src/utils/forms/form9handler.ts", "./src/utils/forms/formfactory.ts", "./src/utils/shared/convertroutingtoflow.ts", "./src/utils/shared/fetchandprocessformconfig.ts", "./src/utils/shared/handlleserialinputchange.ts", "./src/utils/shared/validationschema.ts"], "version": "5.6.3"}