{"name": "New Routing Configuration", "code": "new_routing_configuration", "schema": {"routing_schema": {"id": "r1744540220903", "name": "New Routing Configuration", "type": "manufacturing", "version": "1.1", "route": {"start": "pcb_load_feeder", "end": "lead_cutting", "connections": {"pcb_load_feeder": {"towards": {"default": "laser_marking", "conditions": [], "route_type": "main", "edge_type": "straight", "sourceHandle": "output-0", "targetHandle": "input-0", "metadata": {"label": "Connection from pcb_load_feeder to laser_marking", "description": "Default main route with straight style"}}}, "laser_marking": {"towards": {"default": "solder_paste_printing", "conditions": [], "route_type": "main", "edge_type": "bezier", "sourceHandle": "output-0", "targetHandle": "input-0", "metadata": {"label": "Connection from laser_marking to solder_paste_printing", "description": "Default main route with bezier style"}}}, "solder_paste_printing": {"towards": {"default": "spi", "conditions": [], "route_type": "main", "edge_type": "straight", "sourceHandle": "output-0", "targetHandle": "input-0", "metadata": {"label": "Connection from solder_paste_printing to spi", "description": "Default main route with straight style"}}}, "spi": {"towards": {"default": "pick_place", "conditions": [], "route_type": "main", "edge_type": "straight", "sourceHandle": "output-0", "targetHandle": "input-0", "metadata": {"label": "Connection from spi to pick_place", "description": "Default main route with straight style"}}}, "aoi": {"towards": {"default": "pb_depanel", "conditions": [{"left": {"node": "aoi", "path": "status", "type": "property"}, "right": {"type": "value", "value": "pass"}, "target": "pb_depanel", "operator": "equals", "route_type": "main", "edge_type": "bezier", "sourceHandle": "output-2", "targetHandle": "input-0", "metadata": {"label": "Conditional connection from aoi to pb_depanel", "description": "Condition: status equals pass"}}, {"left": {"node": "aoi", "path": "inspection_status", "type": "property"}, "right": {"type": "value", "value": false}, "target": "pb_rework_aoi", "operator": "equals", "route_type": "rework", "edge_type": "bezier", "sourceHandle": "output-0", "targetHandle": "input-0", "metadata": {"label": "Conditional connection from aoi to pb_rework_aoi", "description": "Condition: inspection_status equals false"}}], "route_type": "main", "edge_type": "bezier", "sourceHandle": "output-1", "targetHandle": "input-1", "metadata": {"label": "Connection from aoi to pb_depanel", "description": "Default main route with bezier style"}}}, "pick_place": {"towards": {"default": "aoi", "conditions": [{"left": {"node": "pick_place", "path": "condition_path_placeholder", "type": "property"}, "right": {"type": "value", "value": "condition_value_placeholder"}, "target": "pb_depanel", "operator": "equals", "route_type": "main", "edge_type": "bezier", "sourceHandle": "output-1", "targetHandle": "input-0", "metadata": {"label": "Connection from pick_place to pb_depanel", "description": "Condition: condition_path_placeholder equals condition_value_placeholder"}}], "route_type": "main", "edge_type": "straight", "sourceHandle": "output-0", "targetHandle": "input-0", "metadata": {"label": "Connection from pick_place to aoi", "description": "Default main route with straight style"}}}, "pb_rework_aoi": {"towards": {"default": "aoi", "conditions": [], "route_type": "main", "edge_type": "bezier", "sourceHandle": "output-1", "targetHandle": "input-1", "metadata": {"label": "Connection from pb_rework_aoi to aoi", "description": "Default main route with bezier style"}}}, "pb_depanel": {"towards": {"default": "pb_thc_mounting", "conditions": [], "route_type": "main", "edge_type": "smoothstep", "sourceHandle": "output-1", "targetHandle": "input-0", "metadata": {"label": "Connection from pb_depanel to pb_thc_mounting", "description": "Default main route with smoothstep style"}}}, "pb_thc_mounting": {"towards": {"default": "pb_pre_wave", "conditions": [], "route_type": "main", "edge_type": "smoothstep", "sourceHandle": "output-1", "targetHandle": "input-1", "metadata": {"label": "Connection from pb_thc_mounting to pb_pre_wave", "description": "Default main route with smoothstep style"}}}, "pb_pre_wave": {"towards": {"default": "wave_soldring", "conditions": [], "route_type": "main", "edge_type": "smoothstep", "sourceHandle": "output-1", "targetHandle": "input-1", "metadata": {"label": "Connection from pb_pre_wave to wave_soldring", "description": "Default main route with smoothstep style"}}}, "wave_soldring": {"towards": {"default": "lead_cutting", "conditions": [], "route_type": "main", "edge_type": "smoothstep", "sourceHandle": "output-1", "targetHandle": "input-1", "metadata": {"label": "Connection from wave_soldring to lead_cutting", "description": "Default main route with smoothstep style"}}}, "lead_cutting": {"towards": {"default": "end", "end": true, "conditions": null, "route_type": "main", "edge_type": "bezier", "sourceHandle": "output-0", "targetHandle": "input-0", "metadata": {"label": "Terminal connection from lead_cutting", "description": "Final node in the manufacturing process"}}}}}, "components": {"pcb_load_feeder": {"position": {"x": -310, "y": 80}, "node_type": "load", "event_required": false, "name": "PCB Load Feeder", "handles": {"inputs": ["input-0", "input-1", "input-2", "input-3"], "outputs": ["output-0", "output-1", "output-2", "output-3"]}}, "laser_marking": {"position": {"x": -20, "y": 80}, "node_type": "process", "event_required": false, "name": "Laser <PERSON>", "handles": {"inputs": ["input-0", "input-1", "input-2", "input-3"], "outputs": ["output-0", "output-1", "output-2", "output-3"]}}, "solder_paste_printing": {"position": {"x": 290, "y": 80}, "node_type": "process", "event_required": false, "name": "Solder Paste Printing", "handles": {"inputs": ["input-0", "input-1", "input-2", "input-3"], "outputs": ["output-0", "output-1", "output-2", "output-3"]}}, "spi": {"position": {"x": 600, "y": 80}, "node_type": "inspection", "event_required": false, "name": "SPI Inspection", "handles": {"inputs": ["input-0", "input-1", "input-2", "input-3"], "outputs": ["output-0", "output-1", "output-2", "output-3"]}}, "pick_place": {"position": {"x": 920, "y": 80}, "node_type": "process", "event_required": false, "name": "Pick & Place", "handles": {"inputs": ["input-0", "input-1", "input-2", "input-3"], "outputs": ["output-0", "output-1", "output-2", "output-3"]}}, "aoi": {"position": {"x": 1240, "y": 80}, "node_type": "inspection", "event_required": false, "name": "AOI Inspection", "handles": {"inputs": ["input-0", "input-1", "input-2", "input-3"], "outputs": ["output-0", "output-1", "output-2", "output-3"]}}, "pb_rework_aoi": {"position": {"x": 1240, "y": -180}, "node_type": "rework", "event_required": false, "name": "Rework AOI", "handles": {"inputs": ["input-0", "input-1", "input-2", "input-3"], "outputs": ["output-0", "output-1", "output-2", "output-3"]}}, "pb_depanel": {"position": {"x": 1050, "y": 330}, "node_type": "process", "event_required": false, "name": "Depanelization", "handles": {"inputs": ["input-0", "input-1", "input-2", "input-3"], "outputs": ["output-0", "output-1", "output-2", "output-3"]}}, "pb_thc_mounting": {"position": {"x": 700, "y": 300}, "node_type": "process", "event_required": false, "name": "THC Mounting", "handles": {"inputs": ["input-0", "input-1", "input-2", "input-3"], "outputs": ["output-0", "output-1", "output-2", "output-3"]}}, "pb_pre_wave": {"position": {"x": 320, "y": 300}, "node_type": "process", "event_required": false, "name": "Pre-Wave", "handles": {"inputs": ["input-0", "input-1", "input-2", "input-3"], "outputs": ["output-0", "output-1", "output-2", "output-3"]}}, "wave_soldring": {"position": {"x": 60, "y": 290}, "node_type": "process", "event_required": false, "name": "Wave Soldering", "handles": {"inputs": ["input-0", "input-1", "input-2", "input-3"], "outputs": ["output-0", "output-1", "output-2", "output-3"]}}, "lead_cutting": {"position": {"x": -250, "y": 280}, "node_type": "process", "event_required": false, "name": "Lead Cutting", "handles": {"inputs": ["input-0", "input-1", "input-2", "input-3"], "outputs": ["output-0", "output-1", "output-2", "output-3"]}}}}}}