import React, { useState } from "react";
import {
  Box,
  Modal,
  Button,
  TextField,
  Typography,
  CircularProgress,
  ThemeProvider,
  IconButton,
} from "@mui/material";
import CloseIcon from '@mui/icons-material/Close';
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useSnackbar } from "../context/SnackBarContext";
import { theme } from "../utils/muiTheme";
import { getAccessToken } from "../utils/authUtils";
import { BASE_URL } from '../utils/axiosInstance';
import {
  textFieldStyle,
  multilineTextFieldStyle,
  outlinedButtonStyle,
  containedButtonStyle,
  modalHeaderStyle,
  modalContainerStyle
} from '../utils/commonStyles';

interface AddProcessBlockModalProps {
  open: boolean;
  onClose: () => void;
}

interface AddProcessBlockPayload {
  name: string;
  code: string;
  description?: string; // optional
  area: number;
  node_type?: string;
}

const token = getAccessToken();

const AddProcessBlockModal: React.FC<AddProcessBlockModalProps> = ({
  open,
  onClose,
}) => {
  const [name, setName] = useState("");
  const [code, setCode] = useState("");
  const [description, setDescription] = useState("");
  const [area, setArea] = useState("");
  const { showSnackbar } = useSnackbar();
  const queryClient = useQueryClient();

  // Error states for validation
  const [nameError, setNameError] = useState("");
  const [codeError, setCodeError] = useState("");
  const [areaError, setAreaError] = useState("");

  // Reset form fields and errors
  const resetForm = () => {
    setName("");
    setCode("");
    setDescription("");
    setArea("");
    setNameError("");
    setCodeError("");
    setAreaError("");
  };

  // Create process block API mutation
  const createProcessBlock = useMutation({
    mutationFn: async (payload: AddProcessBlockPayload) => {
      const response = await fetch(
        `${BASE_URL}/mes_trace/workflow/api/process-blocks/`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            ...payload,
            node_type: "machine", // Default node type
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        const errorMessage =
          errorData?.detail ||
          `Error: ${response.status} ${response.statusText}`;
        throw new Error(errorMessage);
      }

      return await response.json();
    },
    onSuccess: () => {
      // Invalidate both queries at once to prevent duplicate fetches
      queryClient.invalidateQueries({
        queryKey: ["process-blocks"],
      });
      resetForm();
      onClose();
      showSnackbar("Workstation created successfully!", "success");
    },
    onError: (error: Error) => {
      showSnackbar("Error creating workstation: " + error.message, "error");
    },
  });

  // Validation for name
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value
    setName(value);
    if (!value.trim()) {
      setNameError("Name is required");
    } else {
      setNameError("");
    }
  };

  // Validation for code
  const handleCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value;
    setCode(value);
    if (!value.trim()) {
      setCodeError("Code is required");
    } else {
      setCodeError("");
    }
  };

  // Validation for area (must be a valid number)
  const handleAreaChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value;

    setArea(value);
    if (!value.trim()) {
      setAreaError("Area is required");
    } else if (isNaN(Number(value))) {
      setAreaError("Area must be a valid number");
    } else {
      setAreaError("");
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Collect validation errors
    let nameErrorMsg = "";
    let codeErrorMsg = "";
    let areaErrorMsg = "";
    let hasValidationErrors = false;

    // Validate name field
    if (!name.trim()) {
      nameErrorMsg = "Name is required";
      hasValidationErrors = true;
    }

    // Validate code field
    if (!code.trim()) {
      codeErrorMsg = "Code is required";
      hasValidationErrors = true;
    }

    // Validate area field
    if (!area.trim()) {
      areaErrorMsg = "Area is required";
      hasValidationErrors = true;
    } else if (isNaN(Number(area))) {
      areaErrorMsg = "Area must be a valid number";
      hasValidationErrors = true;
    }

    // Set all error states at once
    setNameError(nameErrorMsg);
    setCodeError(codeErrorMsg);
    setAreaError(areaErrorMsg);

    // If validation fails, do not proceed
    if (hasValidationErrors) {
      return;
    }

    // Submit to API if validation passes
    createProcessBlock.mutate({
      name,
      code,
      description: description?.trim() ?? "",
      area: parseInt(area, 10),
    });
  };

  // Handle modal close
  const handleClose = () => {
    resetForm();
    onClose();
  };

  return (
    <Modal open={open} onClose={handleClose}>
      <ThemeProvider theme={theme}>
        <Box sx={modalContainerStyle}>
          <Box sx={modalHeaderStyle}>
            <Typography variant="h6">
              Add Workstation
            </Typography>
            <IconButton onClick={handleClose} aria-label="close">
              <CloseIcon />
            </IconButton>
          </Box>

          <Box component="form" onSubmit={handleSubmit} sx={{ p: 2.5 }}>
            <Box sx={{ display: 'flex', gap: 1.5, mb: 1.5 }}>
              <TextField
                label="Name"
                variant="outlined"
                fullWidth
                placeholder="Enter workstation name"
                value={name}
                onChange={handleNameChange}
                error={Boolean(nameError)}
                helperText={nameError}
                sx={textFieldStyle}
                size="small"
              />
              <TextField
                label="Code"
                variant="outlined"
                fullWidth
                placeholder="Enter workstation code"
                value={code}
                onChange={handleCodeChange}
                error={Boolean(codeError)}
                helperText={codeError}
                sx={textFieldStyle}
                size="small"
              />
            </Box>

            <TextField
              label="Area"
              variant="outlined"
              fullWidth
              placeholder="Enter area number"
              value={area}
              onChange={handleAreaChange}
              error={Boolean(areaError)}
              helperText={areaError}
              sx={{ ...textFieldStyle, mb: 1.5 }}
              size="small"
            />

            <TextField
              label="Description"
              variant="outlined"
              fullWidth
              multiline
              rows={3}
              placeholder="Enter workstation description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              sx={{ ...multilineTextFieldStyle, mb: 2.5 }}
              size="small"
            />

            <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
              <Button
                variant="outlined"
                onClick={handleClose}
                sx={outlinedButtonStyle}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="contained"
                disabled={createProcessBlock.isPending}
                sx={containedButtonStyle}
              >
                {createProcessBlock.isPending ? (
                  <CircularProgress size={16} color="inherit" />
                ) : (
                  "Add Workstation"
                )}
              </Button>
            </Box>
          </Box>
        </Box>
      </ThemeProvider>
    </Modal>
  );
};

export default AddProcessBlockModal;
