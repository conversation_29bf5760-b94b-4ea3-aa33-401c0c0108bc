import React from 'react';
import { Dialog, DialogActions, DialogContent, DialogTitle, TextField, Button, MenuItem } from '@mui/material';

interface FilterModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: () => void;
  filter: Record<string, any>;
  onFilterChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

const FilterModal: React.FC<FilterModalProps> = ({ open, onClose, onSubmit, filter, onFilterChange }) => {
  return (
    <Dialog open={open} onClose={onClose}>
      <DialogTitle>Filter Work Orders</DialogTitle>
      <DialogContent>
        <TextField select label="Line" name="line" value={filter.line} onChange={onFilterChange} fullWidth>
          <MenuItem value="SMT">SMT</MenuItem>
          <MenuItem value="Other">Other</MenuItem>
        </TextField>
        <TextField label="Part No" name="part_no" value={filter.part_no} onChange={onFilterChange} fullWidth />
        <TextField label="Customer" name="customer" value={filter.customer} onChange={onFilterChange} fullWidth />
        <TextField label="Order Date" name="order_date" value={filter.order_date} onChange={onFilterChange} fullWidth />
        <TextField label="Order No" name="order_no" value={filter.order_no} onChange={onFilterChange} fullWidth />
        <TextField label="CF" name="cf" value={filter.cf} onChange={onFilterChange} fullWidth />
        <TextField label="Plan" name="plan" value={filter.plan} onChange={onFilterChange} fullWidth />
        <TextField label="Actual" name="actual" value={filter.actual} onChange={onFilterChange} fullWidth />
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="primary">Cancel</Button>
        <Button onClick={onSubmit} color="primary">Apply</Button>
      </DialogActions>
    </Dialog>
  );
};

export default FilterModal;
