import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  CircularProgress,
  Alert,
} from '@mui/material';
import { useSnackbar } from '../context/SnackBarContext';
import { useProductTable } from '../hooks/useProductTable';
import axiosInstance from '../utils/axiosInstance';

interface EditBOMItemModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
  bomItem: any;
  availableParentItems?: any[];
}

interface UpdateBOMItemPayload {
  bom_header: number;
  component: number;
  parent_item: number | null;
  quantity: number;
}

const EditBOMItemModal: React.FC<EditBOMItemModalProps> = ({
  open,
  onClose,
  onSuccess,
  bomItem,
  availableParentItems = [],
}) => {
  const { showSnackbar } = useSnackbar();

  // Form state
  const [formData, setFormData] = useState<UpdateBOMItemPayload>({
    bom_header: 0,
    component: 0,
    parent_item: null,
    quantity: 1,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get products for the dropdown
  const { data: productsResponse, isLoading: isProductsLoading } = useProductTable({
    page: 1,
    page_size: 100,
  });

  // Initialize form data when bomItem changes
  useEffect(() => {
    if (bomItem && open) {
      setFormData({
        bom_header: bomItem.bom_header || 0,
        component: bomItem.component || 0,
        parent_item: bomItem.parent_item || null,
        quantity: parseFloat(bomItem.quantity) || 1,
      });
    }
  }, [bomItem, open]);

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'quantity' ? Number(value) : value
    }));

    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Handle component selection
  const handleComponentChange = (event: SelectChangeEvent<number>) => {
    const value = event.target.value as number;
    setFormData(prev => ({
      ...prev,
      component: value,
    }));

    // Clear error for component field
    if (errors.component) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.component;
        return newErrors;
      });
    }
  };

  // Handle parent item selection
  const handleParentItemChange = (event: SelectChangeEvent<number | string>) => {
    const value = event.target.value;
    setFormData(prev => ({
      ...prev,
      parent_item: value === '' ? null : Number(value),
    }));
  };

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.component || formData.component === 0) {
      newErrors.component = 'Component selection is required';
    }

    if (!formData.quantity || formData.quantity <= 0) {
      newErrors.quantity = 'Quantity must be greater than 0';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm() || !bomItem?.id) {
      return;
    }

    setIsSubmitting(true);

    try {
      await axiosInstance.patch(`/bom/api/items/${bomItem.id}/`, formData);
      showSnackbar('BOM item updated successfully', 'success');
      onSuccess();
      onClose();
    } catch (error: any) {
      console.error('Error updating BOM item:', error);
      showSnackbar(
        error.response?.data?.message || 'Failed to update BOM item',
        'error'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!bomItem) {
    return null;
  }

  return (
    <Dialog
      open={open}
      onClose={isSubmitting ? undefined : onClose}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle sx={{
        bgcolor: 'rgba(0, 0, 0, 0.8)',
        color: 'white',
        fontWeight: 'bold',
        py: 2
      }}>
        Edit BOM Item
      </DialogTitle>

      <form onSubmit={handleSubmit}>
        <DialogContent sx={{ pt: 3 }}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3, mt: 1 }}>
            <FormControl fullWidth required error={!!errors.component}>
              <InputLabel>Component</InputLabel>
              <Select
                value={formData.component || ''}
                onChange={handleComponentChange}
                label="Component"
                disabled={isSubmitting || isProductsLoading}
              >
                {isProductsLoading ? (
                  <MenuItem disabled>
                    <CircularProgress size={20} sx={{ mr: 1 }} />
                    Loading components...
                  </MenuItem>
                ) : (
                  productsResponse?.results?.map((product: any) => (
                    <MenuItem key={product.id} value={product.id}>
                      {product.name} ({product.code})
                    </MenuItem>
                  ))
                )}
              </Select>
              {errors.component && (
                <Box sx={{ color: 'error.main', fontSize: '0.75rem', mt: 0.5, ml: 1.75 }}>
                  {errors.component}
                </Box>
              )}
            </FormControl>

            {availableParentItems.length > 0 && (
              <FormControl fullWidth>
                <InputLabel>Parent Item (Optional)</InputLabel>
                <Select
                  value={formData.parent_item || ''}
                  onChange={handleParentItemChange}
                  label="Parent Item (Optional)"
                  disabled={isSubmitting}
                >
                  <MenuItem value="">
                    <em>No Parent (Top Level)</em>
                  </MenuItem>
                  {availableParentItems
                    .filter((item: any) => item.id !== bomItem.id) // Prevent self-reference
                    .map((item: any) => (
                      <MenuItem key={item.id} value={item.id}>
                        {item.component_detail.name} ({item.component_detail.code})
                      </MenuItem>
                    ))}
                </Select>
              </FormControl>
            )}

            <TextField
              label="Quantity"
              name="quantity"
              type="number"
              value={formData.quantity}
              onChange={handleInputChange}
              fullWidth
              required
              inputProps={{ min: 0.001, step: 0.001 }}
              error={!!errors.quantity}
              helperText={errors.quantity}
              disabled={isSubmitting}
            />
          </Box>
        </DialogContent>

        <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid rgba(0, 0, 0, 0.1)' }}>
          <Button
            onClick={onClose}
            disabled={isSubmitting}
            sx={{
              color: 'text.primary',
              '&:hover': {
                bgcolor: 'rgba(0, 0, 0, 0.04)',
              }
            }}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={isSubmitting}
            sx={{
              bgcolor: 'rgba(0, 0, 0, 0.8)',
              color: 'white',
              '&:hover': {
                bgcolor: 'rgba(0, 0, 0, 0.7)',
              }
            }}
          >
            {isSubmitting ? (
              <>
                <CircularProgress size={20} sx={{ mr: 1, color: 'white' }} />
                Updating...
              </>
            ) : (
              'Update Item'
            )}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default EditBOMItemModal;
