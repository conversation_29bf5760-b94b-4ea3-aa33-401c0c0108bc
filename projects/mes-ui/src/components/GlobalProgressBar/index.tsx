import { useIsFetching } from '@tanstack/react-query';
import { useEffect, useState } from 'react';

const GlobalProgressBar = () => {
  const isFetching = useIsFetching();
  const [visible, setVisible] = useState(false);
  const [width, setWidth] = useState(0);

  useEffect(() => {
    let interval:any;

    if (isFetching > 0) {
      setVisible(true);
      setWidth(10); // Start from 10%

      interval = setInterval(() => {
        setWidth((prev) => {
          if (prev < 90) return prev + Math.random() * 10;
          return prev;
        });
      }, 300);
    } else {
      clearInterval(interval);
      setWidth(100);
      setTimeout(() => {
        setVisible(false);
        setWidth(0);
      }, 400); // wait for progress to reach 100%
    }

    return () => clearInterval(interval);
  }, [isFetching]);

  if (!visible) return null;

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        height: '4px',
        width: `${width}%`,
        backgroundColor: '#3b82f6', // Tailwind blue-500
        transition: 'width 0.3s ease',
        zIndex: 9999,
      }}
    />
  );
};

export default GlobalProgressBar;
