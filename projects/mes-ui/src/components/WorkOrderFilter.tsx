import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IconButton,
  Box,
  Typography,
} from "@mui/material";
import { Close as CloseIcon } from "@mui/icons-material";
import { LocalizationProvider, DatePicker } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { theme } from "../utils/muiTheme";
import useScreen from "../hooks/useScreenSize";

interface WorkOrderFilterProps {
  filterInput: Record<string, any>;
  onChange: (field: string, value: string) => void;
  onClose: () => void;
}

const WorkOrderFilter: React.FC<WorkOrderFilterProps> = ({
  filterInput,
  onChange,
  onClose,
}) => {
  const [date, setDate] = React.useState<Date | null>(
    filterInput.order_date ? new Date(filterInput.order_date) : null
  );

  const handleDateChange = (newDate: Date | null) => {
    setDate(newDate);
    onChange("order_date", newDate ? newDate.toISOString().split("T")[0] : "");
  };

  const { device } = useScreen();
  const isMobile = device === "mobile";

  return (
    <ThemeProvider theme={theme}>
      <Box sx={{ p: 2 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "0px 17px",
            mb: 2,
          }}
        >
          <Typography variant="h6">Filter Work Orders</Typography>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>

        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            gap: 2,
          }}
        >
          <TextField
            label="Line"
            name="line"
            value={filterInput.line || ""}
            onChange={(e) => onChange("line", e.target.value)}
            fullWidth
            size="small"
            sx={{
              '& .MuiOutlinedInput-root': {
                height: '32px',
                borderRadius: '4px',
                '& fieldset': {
                  borderColor: '#e2e8f0',
                },
                '&:hover fieldset': {
                  borderColor: 'rgb(2, 8, 23)',
                },
                '&.Mui-focused fieldset': {
                  borderColor: 'rgb(2, 8, 23)',
                  borderWidth: '1px',
                },
                '& input': {
                  padding: '6px 12px',
                  fontSize: '14px',
                  fontFamily: 'ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
                }
              },
              '& .MuiInputLabel-root': {
                fontSize: '14px',
                fontFamily: 'ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
                color: 'rgb(2, 8, 23)',
                '&.Mui-focused': {
                  color: 'rgb(2, 8, 23)',
                }
              }
            }}
          />
          <TextField
            label="Part No"
            name="part_no"
            value={filterInput.part_no || ""}
            onChange={(e) => onChange("part_no", e.target.value)}
            fullWidth
            size="small"
            sx={{
              '& .MuiOutlinedInput-root': {
                height: '32px',
                borderRadius: '4px',
                '& fieldset': {
                  borderColor: '#e2e8f0',
                },
                '&:hover fieldset': {
                  borderColor: 'rgb(2, 8, 23)',
                },
                '&.Mui-focused fieldset': {
                  borderColor: 'rgb(2, 8, 23)',
                  borderWidth: '1px',
                },
                '& input': {
                  padding: '6px 12px',
                  fontSize: '14px',
                  fontFamily: 'ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
                }
              },
              '& .MuiInputLabel-root': {
                fontSize: '14px',
                fontFamily: 'ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
                color: 'rgb(2, 8, 23)',
                '&.Mui-focused': {
                  color: 'rgb(2, 8, 23)',
                }
              }
            }}
          />
          <TextField
            label="Customer"
            name="customer"
            value={filterInput.customer || ""}
            onChange={(e) => onChange("customer", e.target.value)}
            fullWidth
            size="small"
            sx={{
              '& .MuiOutlinedInput-root': {
                height: '32px',
                borderRadius: '4px',
                '& fieldset': {
                  borderColor: '#e2e8f0',
                },
                '&:hover fieldset': {
                  borderColor: 'rgb(2, 8, 23)',
                },
                '&.Mui-focused fieldset': {
                  borderColor: 'rgb(2, 8, 23)',
                  borderWidth: '1px',
                },
                '& input': {
                  padding: '6px 12px',
                  fontSize: '14px',
                  fontFamily: 'ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
                }
              },
              '& .MuiInputLabel-root': {
                fontSize: '14px',
                fontFamily: 'ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
                color: 'rgb(2, 8, 23)',
                '&.Mui-focused': {
                  color: 'rgb(2, 8, 23)',
                }
              }
            }}
          />
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <DatePicker
              label="Order Date"
              value={date}
              onChange={handleDateChange}
              slotProps={{
                textField: {
                  fullWidth: true,
                  size: "small",
                  sx: {
                    '& .MuiOutlinedInput-root': {
                      height: '32px',
                      borderRadius: '4px',
                      '& fieldset': {
                        borderColor: '#e2e8f0',
                      },
                      '&:hover fieldset': {
                        borderColor: 'rgb(2, 8, 23)',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: 'rgb(2, 8, 23)',
                        borderWidth: '1px',
                      },
                      '& input': {
                        padding: '6px 12px',
                        fontSize: '14px',
                        fontFamily: 'ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
                      }
                    },
                    '& .MuiInputLabel-root': {
                      fontSize: '14px',
                      fontFamily: 'ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
                      color: 'rgb(2, 8, 23)',
                      '&.Mui-focused': {
                        color: 'rgb(2, 8, 23)',
                      }
                    }
                  }
                },
              }}
              format="yyyy-MM-dd"
            />
          </LocalizationProvider>
          <TextField
            label="Order No"
            name="order_no"
            value={filterInput.order_no || ""}
            onChange={(e) => onChange("order_no", e.target.value)}
            fullWidth
            size="small"
            sx={{
              '& .MuiOutlinedInput-root': {
                height: '32px',
                borderRadius: '4px',
                '& fieldset': {
                  borderColor: '#e2e8f0',
                },
                '&:hover fieldset': {
                  borderColor: 'rgb(2, 8, 23)',
                },
                '&.Mui-focused fieldset': {
                  borderColor: 'rgb(2, 8, 23)',
                  borderWidth: '1px',
                },
                '& input': {
                  padding: '6px 12px',
                  fontSize: '14px',
                  fontFamily: 'ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
                }
              },
              '& .MuiInputLabel-root': {
                fontSize: '14px',
                fontFamily: 'ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
                color: 'rgb(2, 8, 23)',
                '&.Mui-focused': {
                  color: 'rgb(2, 8, 23)',
                }
              }
            }}
          />
        </Box>
      </Box>
    </ThemeProvider>
  );
};

export default WorkOrderFilter;
