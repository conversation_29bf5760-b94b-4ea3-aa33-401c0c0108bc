import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  CircularProgress,
} from '@mui/material';
import { useSnackbar } from '../context/SnackBarContext';
import { useDeleteBOMItem } from '../hooks/useBOMTable';

interface DeleteBOMItemModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
  bomItem: any;
}

const DeleteBOMItemModal: React.FC<DeleteBOMItemModalProps> = ({
  open,
  onClose,
  onSuccess,
  bomItem,
}) => {
  const { showSnackbar } = useSnackbar();
  const deleteItemMutation = useDeleteBOMItem();

  // Handle deletion
  const handleDelete = async () => {
    if (!bomItem?.id) return;

    try {
      await deleteItemMutation.mutateAsync(bomItem.id);
      showSnackbar('BOM item deleted successfully', 'success');
      onSuccess();
      onClose();
    } catch (error: any) {
      console.error('Error deleting BOM item:', error);
      showSnackbar(
        error.response?.data?.message || 'Failed to delete BOM item',
        'error'
      );
    }
  };

  if (!bomItem) return null;

  return (
    <Dialog
      open={open}
      onClose={deleteItemMutation.isPending ? undefined : onClose}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle sx={{
        fontWeight: 600,
        py: 3,
        textAlign: 'center',
        borderBottom: '1px solid #f1f5f9'
      }}>
        Delete Item
      </DialogTitle>

      <DialogContent sx={{ pt: 4, pb: 3, textAlign: 'center' }}>
        <Typography variant="h6" sx={{ mb: 2, color: '#374151' }}>
          Are you sure?
        </Typography>
        <Typography variant="body2" color="text.secondary">
          This action cannot be undone.
        </Typography>
      </DialogContent>

      <DialogActions sx={{
        px: 4,
        py: 3,
        gap: 2,
        justifyContent: 'center',
        borderTop: '1px solid #f1f5f9'
      }}>
        <Button
          onClick={onClose}
          disabled={deleteItemMutation.isPending}
          variant="outlined"
          sx={{
            borderColor: '#d1d5db',
            color: '#6b7280',
            px: 4,
            py: 1,
            '&:hover': {
              borderColor: '#9ca3af',
              bgcolor: '#f9fafb',
            }
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleDelete}
          variant="contained"
          disabled={deleteItemMutation.isPending}
          sx={{
            bgcolor: '#ef4444',
            color: 'white',
            px: 4,
            py: 1,
            '&:hover': {
              bgcolor: '#dc2626',
            },
            '&:disabled': {
              bgcolor: 'rgba(239, 68, 68, 0.5)',
            }
          }}
        >
          {deleteItemMutation.isPending ? (
            <>
              <CircularProgress size={18} sx={{ mr: 1, color: 'white' }} />
              Deleting...
            </>
          ) : (
            'Delete'
          )}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DeleteBOMItemModal;
