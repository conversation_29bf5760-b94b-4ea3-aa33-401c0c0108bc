import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  CircularProgress,
} from '@mui/material';
import { useSnackbar } from '../context/SnackBarContext';
import { useDeleteBOMItem } from '../hooks/useBOMTable';

interface DeleteBOMItemModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
  bomItem: any;
}

const DeleteBOMItemModal: React.FC<DeleteBOMItemModalProps> = ({
  open,
  onClose,
  onSuccess,
  bomItem,
}) => {
  const { showSnackbar } = useSnackbar();
  const deleteItemMutation = useDeleteBOMItem();

  // Handle deletion
  const handleDelete = async () => {
    if (!bomItem?.id) return;

    try {
      await deleteItemMutation.mutateAsync(bomItem.id);
      showSnackbar('BOM item deleted successfully', 'success');
      onSuccess();
      onClose();
    } catch (error: any) {
      console.error('Error deleting BOM item:', error);
      showSnackbar(
        error.response?.data?.message || 'Failed to delete BOM item',
        'error'
      );
    }
  };

  if (!bomItem) return null;

  return (
    <Dialog
      open={open}
      onClose={deleteItemMutation.isPending ? undefined : onClose}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle sx={{
        bgcolor: '#d32f2f',
        color: 'white',
        fontWeight: 'bold',
        py: 2,
        display: 'flex',
        alignItems: 'center',
        gap: 1
      }}>
        Confirm Deletion
      </DialogTitle>

      <DialogContent sx={{ pt: 3, pb: 2 }}>
        <Typography variant="body1" sx={{ textAlign: 'center', fontSize: '1.1rem' }}>
          Are you sure you want to delete this item?
        </Typography>
      </DialogContent>

      <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid rgba(0, 0, 0, 0.1)' }}>
        <Button
          onClick={onClose}
          disabled={deleteItemMutation.isPending}
          sx={{
            color: 'text.primary',
            '&:hover': {
              bgcolor: 'rgba(0, 0, 0, 0.04)',
            }
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleDelete}
          variant="contained"
          disabled={deleteItemMutation.isPending}
          sx={{
            bgcolor: '#d32f2f',
            color: 'white',
            '&:hover': {
              bgcolor: '#c62828',
            },
            '&:disabled': {
              bgcolor: 'rgba(211, 47, 47, 0.5)',
            }
          }}
        >
          {deleteItemMutation.isPending ? (
            <>
              <CircularProgress size={20} sx={{ mr: 1, color: 'white' }} />
              Deleting...
            </>
          ) : (
            'Delete'
          )}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DeleteBOMItemModal;
