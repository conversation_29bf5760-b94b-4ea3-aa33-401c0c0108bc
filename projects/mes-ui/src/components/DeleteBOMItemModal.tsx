import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  Button,
  Box,
  Typography,
  CircularProgress,
} from '@mui/material';
import WarningIcon from '@mui/icons-material/Warning';
import { useSnackbar } from '../context/SnackBarContext';
import { useDeleteBOMItem } from '../hooks/useBOMTable';

interface DeleteBOMItemModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
  bomItem: any;
}

const DeleteBOMItemModal: React.FC<DeleteBOMItemModalProps> = ({
  open,
  onClose,
  onSuccess,
  bomItem,
}) => {
  const { showSnackbar } = useSnackbar();
  const deleteItemMutation = useDeleteBOMItem();

  // Handle deletion
  const handleDelete = async () => {
    if (!bomItem?.id) return;

    try {
      await deleteItemMutation.mutateAsync(bomItem.id);
      showSnackbar('BOM item deleted successfully', 'success');
      onSuccess();
      onClose();
    } catch (error: any) {
      console.error('Error deleting BOM item:', error);
      showSnackbar(
        error.response?.data?.message || 'Failed to delete BOM item',
        'error'
      );
    }
  };

  if (!bomItem) return null;

  return (
    <Dialog
      open={open}
      onClose={deleteItemMutation.isPending ? undefined : onClose}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle sx={{
        bgcolor: '#d32f2f',
        color: 'white',
        fontWeight: 'bold',
        py: 2,
        display: 'flex',
        alignItems: 'center',
        gap: 1
      }}>
        <WarningIcon />
        Confirm Deletion
      </DialogTitle>

      <DialogContent sx={{ pt: 3 }}>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
            Are you sure you want to delete this BOM item?
          </Typography>

          {/* Item Details */}
          <Box sx={{ 
            p: 2, 
            bgcolor: '#f5f5f5', 
            borderRadius: 1,
            border: '1px solid #e0e0e0'
          }}>
            <Typography variant="subtitle2" color="text.secondary" gutterBottom>
              Item Details:
            </Typography>
            <Box sx={{ ml: 1 }}>
              <Typography variant="body2">
                <strong>Component:</strong> {bomItem.component_detail?.name || 'Unknown'}
              </Typography>
              <Typography variant="body2">
                <strong>Code:</strong> {bomItem.component_detail?.code || 'Unknown'}
              </Typography>
              <Typography variant="body2">
                <strong>Quantity:</strong> {bomItem.quantity}
              </Typography>
              <Typography variant="body2">
                <strong>Type:</strong> {bomItem.item_type || 'Unknown'}
              </Typography>
            </Box>
          </Box>

          {/* Warning Message */}
          <Box sx={{ 
            p: 2, 
            bgcolor: '#fff3cd', 
            borderRadius: 1,
            border: '1px solid #ffeaa7',
            display: 'flex',
            alignItems: 'flex-start',
            gap: 1
          }}>
            <WarningIcon sx={{ color: '#856404', mt: 0.1 }} fontSize="small" />
            <Box>
              <Typography variant="body2" sx={{ color: '#856404', fontWeight: 'medium' }}>
                Warning: This action cannot be undone
              </Typography>
              <Typography variant="body2" sx={{ color: '#856404', mt: 0.5 }}>
                Deleting this item will permanently remove it from the BOM structure. 
                If this item has children, they will also be deleted.
              </Typography>
            </Box>
          </Box>
        </Box>
      </DialogContent>

      <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid rgba(0, 0, 0, 0.1)' }}>
        <Button
          onClick={onClose}
          disabled={deleteItemMutation.isPending}
          sx={{
            color: 'text.primary',
            '&:hover': {
              bgcolor: 'rgba(0, 0, 0, 0.04)',
            }
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleDelete}
          variant="contained"
          disabled={deleteItemMutation.isPending}
          sx={{
            bgcolor: '#d32f2f',
            color: 'white',
            '&:hover': {
              bgcolor: '#c62828',
            },
            '&:disabled': {
              bgcolor: 'rgba(211, 47, 47, 0.5)',
            }
          }}
        >
          {deleteItemMutation.isPending ? (
            <>
              <CircularProgress size={20} sx={{ mr: 1, color: 'white' }} />
              Deleting...
            </>
          ) : (
            'Delete Item'
          )}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DeleteBOMItemModal;
