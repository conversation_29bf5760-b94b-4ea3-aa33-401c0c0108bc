import React from 'react';
import { Box, Typography, Button, Paper } from '@mui/material';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';

interface ErrorStateProps {
  title?: string;
  message: string;
  actionButton?: {
    label: string;
    onClick: () => void;
  };
}

/**
 * A reusable error state component with optional action button
 */
const ErrorState: React.FC<ErrorStateProps> = ({ 
  title = 'Error', 
  message, 
  actionButton 
}) => {
  return (
    <Paper sx={{ p: 3, textAlign: 'center' }}>
      <ErrorOutlineIcon sx={{ fontSize: 48, color: 'error.main', mb: 2 }} />
      
      <Typography variant="h5" color="error" gutterBottom>
        {title}
      </Typography>
      
      <Typography variant="body1" sx={{ mb: 3 }}>
        {message}
      </Typography>
      
      {actionButton && (
        <Button
          variant="outlined"
          onClick={actionButton.onClick}
          sx={{ mt: 1 }}
        >
          {actionButton.label}
        </Button>
      )}
    </Paper>
  );
};

export default ErrorState;
