import React, { forwardRef } from "react";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { ResponsiveSize } from "../interfaces/form-config.api.interface";
import useScreen from "../hooks/useScreenSize";

interface DateFieldProps {
  label: string;
  name: string;
  value: Date | null;
  onChange: (date: Date | null) => void;
  onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
  error?: string | false | any;
  alignment?: "left" | "right" | "center";
  readonly?: boolean;
  placeholder?: string;
  width?: ResponsiveSize;
  height?: ResponsiveSize;
  marginRight?: ResponsiveSize;
}

const DateField = forwardRef<HTMLInputElement, DateFieldProps>(
  (
    {
      label,
      name,
      value,
      onChange,
      onBlur,
      error,
      width,
      marginRight,
      alignment,
      readonly,
      placeholder,
    },
    ref
  ) => {
    const {
      device,
      width: deviceWidth,
      marginRight: deviceRightMargin,
    } = useScreen(width, marginRight);
    return (
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <DatePicker
          label={label}
          value={value}
          minDate={new Date()}
          onChange={onChange}
          disabled={readonly}
          disableOpenPicker={false}
          slotProps={{
            textField: {
              name,
              size: "medium",
              variant: "outlined",
              placeholder,
              error: !!error,
              inputRef: ref,
              onBlur,
              //   helperText: error || "",
              sx: {
                width: deviceWidth || "100%",
                marginRight: deviceRightMargin || "0px",
                textAlign: alignment || "left",
                marginBottom: device !== "mobile" ? "18px" : "10px",
                backgroundColor: "#fff",
                "& .MuiInputBase-input.Mui-disabled": {
                  opacity: 1, // Keep it fully visible
                  WebkitTextFillColor: "#000 !important", // Force text color to black
                },
              },

              InputProps: {
                disabled: device === "desktop" ? true : false,
                readOnly: true,
              },
            },
          }}
        />
      </LocalizationProvider>
    );
  }
);

export default DateField;
