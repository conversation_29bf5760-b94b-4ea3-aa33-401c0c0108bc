import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  CircularProgress,
  Alert,
} from '@mui/material';
import { useSnackbar } from '../context/SnackBarContext';
import { useProductTable } from '../hooks/useProductTable';
import axiosInstance from '../utils/axiosInstance';

interface CreateBOMItemModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
  bomHeaderId: number;
  parentItemId?: number | null;
  availableParentItems?: any[];
}

interface CreateBOMItemPayload {
  bom_header: number;
  component: number;
  parent_item: number | null;
  quantity: number;
}

const CreateBOMItemModal: React.FC<CreateBOMItemModalProps> = ({
  open,
  onClose,
  onSuccess,
  bomHeaderId,
  parentItemId = null,
  availableParentItems = [],
}) => {
  const { showSnackbar } = useSnackbar();

  // Form state
  const [formData, setFormData] = useState<CreateBOMItemPayload>({
    bom_header: bomHeaderId,
    component: 0,
    parent_item: parentItemId,
    quantity: 1,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get products for the dropdown
  const { data: productsResponse, isLoading: isProductsLoading } = useProductTable({
    page: 1,
    page_size: 100,
  });

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'quantity' ? Number(value) : value
    }));

    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Handle component selection
  const handleComponentChange = (event: SelectChangeEvent<number>) => {
    const value = event.target.value as number;
    setFormData(prev => ({
      ...prev,
      component: value,
    }));

    // Clear error for component field
    if (errors.component) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.component;
        return newErrors;
      });
    }
  };

  // Handle parent item selection
  const handleParentItemChange = (event: SelectChangeEvent<number | string>) => {
    const value = event.target.value;
    setFormData(prev => ({
      ...prev,
      parent_item: value === '' ? null : Number(value),
    }));
  };

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.component || formData.component === 0) {
      newErrors.component = 'Component selection is required';
    }

    if (!formData.quantity || formData.quantity <= 0) {
      newErrors.quantity = 'Quantity must be greater than 0';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      await axiosInstance.post('/bom/api/items/', formData);
      showSnackbar('BOM item created successfully', 'success');
      onSuccess();
      onClose();
      // Reset form
      setFormData({
        bom_header: bomHeaderId,
        component: 0,
        parent_item: parentItemId,
        quantity: 1,
      });
    } catch (error: any) {
      console.error('Error creating BOM item:', error);
      showSnackbar(
        error.response?.data?.message || 'Failed to create BOM item',
        'error'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={isSubmitting ? undefined : onClose}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle sx={{
        bgcolor: 'rgba(0, 0, 0, 0.8)',
        color: 'white',
        fontWeight: 'bold',
        py: 2
      }}>
        Add BOM Item
      </DialogTitle>

      <form onSubmit={handleSubmit}>
        <DialogContent sx={{ pt: 3 }}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3, mt: 1 }}>
            <FormControl fullWidth required error={!!errors.component}>
              <InputLabel>Component</InputLabel>
              <Select
                value={formData.component || ''}
                onChange={handleComponentChange}
                label="Component"
                disabled={isSubmitting || isProductsLoading}
              >
                {isProductsLoading ? (
                  <MenuItem disabled>
                    <CircularProgress size={20} sx={{ mr: 1 }} />
                    Loading components...
                  </MenuItem>
                ) : (
                  productsResponse?.results?.map((product: any) => (
                    <MenuItem key={product.id} value={product.id}>
                      {product.name} ({product.code})
                    </MenuItem>
                  ))
                )}
              </Select>
              {errors.component && (
                <Box sx={{ color: 'error.main', fontSize: '0.75rem', mt: 0.5, ml: 1.75 }}>
                  {errors.component}
                </Box>
              )}
            </FormControl>

            {availableParentItems.length > 0 && (
              <FormControl fullWidth>
                <InputLabel>Parent Item (Optional)</InputLabel>
                <Select
                  value={formData.parent_item || ''}
                  onChange={handleParentItemChange}
                  label="Parent Item (Optional)"
                  disabled={isSubmitting}
                >
                  <MenuItem value="">
                    <em>No Parent (Top Level)</em>
                  </MenuItem>
                  {availableParentItems.map((item: any) => (
                    <MenuItem key={item.id} value={item.id}>
                      {item.component_detail.name} ({item.component_detail.code})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}

            <TextField
              label="Quantity"
              name="quantity"
              type="number"
              value={formData.quantity}
              onChange={handleInputChange}
              fullWidth
              required
              inputProps={{ min: 0.001, step: 0.001 }}
              error={!!errors.quantity}
              helperText={errors.quantity}
              disabled={isSubmitting}
            />
          </Box>
        </DialogContent>

        <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid rgba(0, 0, 0, 0.1)' }}>
          <Button
            onClick={onClose}
            disabled={isSubmitting}
            sx={{
              color: 'text.primary',
              '&:hover': {
                bgcolor: 'rgba(0, 0, 0, 0.04)',
              }
            }}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={isSubmitting}
            sx={{
              bgcolor: 'rgba(0, 0, 0, 0.8)',
              color: 'white',
              '&:hover': {
                bgcolor: 'rgba(0, 0, 0, 0.7)',
              }
            }}
          >
            {isSubmitting ? (
              <>
                <CircularProgress size={20} sx={{ mr: 1, color: 'white' }} />
                Creating...
              </>
            ) : (
              'Add Item'
            )}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default CreateBOMItemModal;
