import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  CircularProgress,
  Alert,
  Typography,
} from '@mui/material';
import { useSnackbar } from '../context/SnackBarContext';
import { useProductTable } from '../hooks/useProductTable';
import { useCreateBOMItem, BOMItemPayload } from '../hooks/useBOMTable';

interface CreateBOMItemModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
  bomHeaderId: number;
  parentItemId?: number | null;
  availableParentItems?: any[];
}



const CreateBOMItemModal: React.FC<CreateBOMItemModalProps> = ({
  open,
  onClose,
  onSuccess,
  bomHeaderId,
  parentItemId = null,
  availableParentItems = [],
}) => {
  const { showSnackbar } = useSnackbar();
  const createItemMutation = useCreateBOMItem();

  // Determine if this is a root level item or child item
  const isRootLevel = parentItemId === null || parentItemId === undefined;
  const isChildItem = !isRootLevel;

  // Form state - simplified to only show relevant fields
  const [formData, setFormData] = useState<BOMItemPayload>({
    bom_header: bomHeaderId,
    component: 0,
    parent_item: parentItemId, // Automatically set based on context
    quantity: 1,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Get products for the dropdown
  const { data: productsResponse, isLoading: isProductsLoading } = useProductTable({
    page: 1,
    page_size: 100,
  });

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'quantity' ? Number(value) : value
    }));

    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Handle component selection
  const handleComponentChange = (event: SelectChangeEvent<number>) => {
    const value = event.target.value as number;
    setFormData(prev => ({
      ...prev,
      component: value,
    }));

    // Clear error for component field
    if (errors.component) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.component;
        return newErrors;
      });
    }
  };

  // Reset form when modal opens and set parent_item based on context
  useEffect(() => {
    if (open) {
      setFormData({
        bom_header: bomHeaderId,
        component: 0,
        parent_item: isRootLevel ? null : parentItemId, // Automatically set based on context
        quantity: 1,
      });
      setErrors({});
    }
  }, [open, bomHeaderId, parentItemId, isRootLevel]);

  // Validate form - simplified validation for both root and child items
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.component || formData.component === 0) {
      newErrors.component = 'Component selection is required';
    }

    if (!formData.quantity || formData.quantity <= 0) {
      newErrors.quantity = 'Quantity must be greater than 0';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      await createItemMutation.mutateAsync(formData);
      showSnackbar('BOM item created successfully', 'success');
      onSuccess();
      onClose();
      // Reset form
      setFormData({
        bom_header: bomHeaderId,
        component: 0,
        parent_item: isRootLevel ? null : parentItemId,
        quantity: 1,
      });
    } catch (error: any) {
      console.error('Error creating BOM item:', error);
      showSnackbar(
        error.response?.data?.message || 'Failed to create BOM item',
        'error'
      );
    }
  };

  return (
    <Dialog
      open={open}
      onClose={createItemMutation.isPending ? undefined : onClose}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle sx={{
        bgcolor: 'rgba(0, 0, 0, 0.8)',
        color: 'white',
        fontWeight: 'bold',
        py: 2
      }}>
        {isRootLevel ? 'Add Top Level Item' : 'Add Child Item'}
      </DialogTitle>

      <form onSubmit={handleSubmit}>
        <DialogContent sx={{ pt: 3 }}>
          {/* Context indicator */}
          {!isRootLevel && (
            <Box sx={{
              mb: 2,
              p: 2,
              bgcolor: '#f5f5f5',
              borderRadius: 1,
              border: '1px solid #e0e0e0'
            }}>
              <Typography variant="body2" color="text.secondary">
                Adding child item to parent component
              </Typography>
            </Box>
          )}

          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3, mt: 1 }}>
            <FormControl fullWidth required error={!!errors.component}>
              <InputLabel>Component</InputLabel>
              <Select
                value={formData.component || ''}
                onChange={handleComponentChange}
                label="Component"
                disabled={createItemMutation.isPending || isProductsLoading}
              >
                {isProductsLoading ? (
                  <MenuItem disabled>
                    <CircularProgress size={20} sx={{ mr: 1 }} />
                    Loading components...
                  </MenuItem>
                ) : (
                  productsResponse?.results?.map((product: any) => (
                    <MenuItem key={product.id} value={product.id}>
                      {product.name} ({product.code})
                    </MenuItem>
                  ))
                )}
              </Select>
              {errors.component && (
                <Box sx={{ color: 'error.main', fontSize: '0.75rem', mt: 0.5, ml: 1.75 }}>
                  {errors.component}
                </Box>
              )}
            </FormControl>

            {/* Parent item is automatically handled based on context - no UI needed */}

            <TextField
              label="Quantity"
              name="quantity"
              type="number"
              value={formData.quantity}
              onChange={handleInputChange}
              fullWidth
              required
              inputProps={{ min: 0.001, step: 0.001 }}
              error={!!errors.quantity}
              helperText={errors.quantity}
              disabled={createItemMutation.isPending}
            />
          </Box>
        </DialogContent>

        <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid rgba(0, 0, 0, 0.1)' }}>
          <Button
            onClick={onClose}
            disabled={createItemMutation.isPending}
            sx={{
              color: 'text.primary',
              '&:hover': {
                bgcolor: 'rgba(0, 0, 0, 0.04)',
              }
            }}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={createItemMutation.isPending}
            sx={{
              bgcolor: 'rgba(0, 0, 0, 0.8)',
              color: 'white',
              '&:hover': {
                bgcolor: 'rgba(0, 0, 0, 0.7)',
              }
            }}
          >
            {createItemMutation.isPending ? (
              <>
                <CircularProgress size={20} sx={{ mr: 1, color: 'white' }} />
                Creating...
              </>
            ) : (
              'Add Item'
            )}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default CreateBOMItemModal;
