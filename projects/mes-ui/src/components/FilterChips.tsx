import React from 'react';
import { Box, Chip, Typography, Button, Paper, useTheme } from '@mui/material';

interface FilterChipsProps {
  filters: Record<string, string>;
  onClearFilter: (key: string) => void;
  onClearAll: () => void;
  displayNames?: Record<string, string>;
}

/**
 * A reusable component for displaying active filters as chips
 */
const FilterChips: React.FC<FilterChipsProps> = ({ 
  filters, 
  onClearFilter, 
  onClearAll,
  displayNames = {} 
}) => {
  const theme = useTheme();
  const filterKeys = Object.keys(filters);
  
  if (filterKeys.length === 0) {
    return null;
  }
  
  return (
    <Paper 
      sx={{ 
        p: 1.5, 
        mb: 2, 
        display: 'flex', 
        flexWrap: 'wrap', 
        gap: 1, 
        alignItems: 'center',
        bgcolor: theme.palette.grey[50],
        borderRadius: 1,
      }}
    >
      <Typography variant="body2" sx={{ fontWeight: 'medium', mr: 1 }}>
        Active Filters:
      </Typography>
      
      {filterKeys.map(key => (
        <Chip 
          key={key}
          label={`${displayNames[key] || key}: ${filters[key]}`}
          onDelete={() => onClearFilter(key)}
          size="small"
          sx={{ 
            bgcolor: theme.palette.mode === 'light' ? 
              theme.palette.grey[100] : 
              theme.palette.grey[700],
            '&:hover': {
              bgcolor: theme.palette.mode === 'light' ? 
                theme.palette.grey[200] : 
                theme.palette.grey[600],
            }
          }}
        />
      ))}
      
      {filterKeys.length > 1 && (
        <Button 
          size="small" 
          onClick={onClearAll}
          sx={{ ml: 'auto', textTransform: 'none' }}
        >
          Clear All
        </Button>
      )}
    </Paper>
  );
};

export default FilterChips;
