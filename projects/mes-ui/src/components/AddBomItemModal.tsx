import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  MenuItem,
  Box,
  Autocomplete,
  CircularProgress,
} from '@mui/material';
import { useSnackbar } from '../context/SnackBarContext';
import axiosInstance from '../utils/axiosInstance';

interface Component {
  id: number;
  code: string;
  name: string;
  description: string;
}

interface BOMItem {
  id: number;
  component: Component;
  position: string;
}

interface AddBomItemModalProps {
  open: boolean;
  onClose: () => void;
  bomHeaderId: number;
  onSuccess: () => void;
}

const AddBomItemModal: React.FC<AddBomItemModalProps> = ({
  open,
  onClose,
  bomHeaderId,
  onSuccess,
}) => {
  const { showSnackbar } = useSnackbar();
  const [loading, setLoading] = useState(false);
  const [components, setComponents] = useState<Component[]>([]);
  const [parentItems, setParentItems] = useState<BOMItem[]>([]);
  const [selectedComponent, setSelectedComponent] = useState<Component | null>(null);
  const [selectedParent, setSelectedParent] = useState<BOMItem | null>(null);
  const [formData, setFormData] = useState({
    quantity: 1,
    position: '',
    item_type: 'assembly',
    notes: '',
  });

  useEffect(() => {
    if (open) {
      fetchComponents();
      fetchParentItems();
    }
  }, [open, bomHeaderId]);

  const fetchComponents = async () => {
    try {
      const response = await axiosInstance.get('/products/api/components/');
      setComponents(response.data);
    } catch (error) {
      showSnackbar('Failed to fetch components', 'error');
    }
  };

  const fetchParentItems = async () => {
    try {
      const response = await axiosInstance.get(`/bom/api/items/?bom_header=${bomHeaderId}`);
      setParentItems(response.data);
    } catch (error) {
      showSnackbar('Failed to fetch parent items', 'error');
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async () => {
    if (!selectedComponent) {
      showSnackbar('Please select a component', 'error');
      return;
    }

    setLoading(true);
    try {
      const response = await axiosInstance.post('/bom/api/items/', {
        bom_header: bomHeaderId,
        component: selectedComponent.id,
        parent_item: selectedParent?.id || null,
        quantity: parseInt(formData.quantity.toString()),
        position: formData.position,
        item_type: formData.item_type,
        notes: formData.notes,
      });

      if (response.status === 201) {
        showSnackbar('BOM item added successfully', 'success');
        onSuccess();
        onClose();
      } else {
        throw new Error('Failed to add BOM item');
      }
    } catch (error) {
      showSnackbar('Failed to add BOM item', 'error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>Add BOM Item</DialogTitle>
      <DialogContent>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, pt: 2 }}>
          <Autocomplete
            options={components}
            getOptionLabel={(option) => `${option.code} - ${option.name}`}
            value={selectedComponent}
            onChange={(_, newValue) => setSelectedComponent(newValue)}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Component"
                required
                helperText="Search by component code or name"
              />
            )}
          />

          <Autocomplete
            options={parentItems}
            getOptionLabel={(option) => `${option.component.code} - ${option.component.name} (${option.position})`}
            value={selectedParent}
            onChange={(_, newValue) => setSelectedParent(newValue)}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Parent Item"
                helperText="Leave empty for top-level items"
              />
            )}
          />

          <TextField
            name="quantity"
            label="Quantity"
            type="number"
            value={formData.quantity}
            onChange={handleChange}
            fullWidth
            required
            inputProps={{ min: 1 }}
          />

          <TextField
            name="position"
            label="Position"
            value={formData.position}
            onChange={handleChange}
            fullWidth
            required
            placeholder="e.g., FRAME, MOTOR, etc."
          />

          <TextField
            name="item_type"
            label="Item Type"
            select
            value={formData.item_type}
            onChange={handleChange}
            fullWidth
            required
          >
            <MenuItem value="assembly">Assembly</MenuItem>
            <MenuItem value="component">Component</MenuItem>
            <MenuItem value="material">Material</MenuItem>
          </TextField>

          <TextField
            name="notes"
            label="Notes"
            value={formData.notes}
            onChange={handleChange}
            fullWidth
            multiline
            rows={3}
            placeholder="Add any additional notes about this BOM item"
          />
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          Cancel
        </Button>
        <Button 
          onClick={handleSubmit} 
          variant="contained" 
          color="primary"
          disabled={loading || !selectedComponent}
        >
          {loading ? <CircularProgress size={24} /> : 'Add Item'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AddBomItemModal; 