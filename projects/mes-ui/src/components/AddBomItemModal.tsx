import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  MenuItem,
  Box,
} from '@mui/material';
import { useSnackbar } from '../context/SnackBarContext';

interface AddBomItemModalProps {
  open: boolean;
  onClose: () => void;
  bomHeaderId: number;
  onSuccess: () => void;
}

const AddBomItemModal: React.FC<AddBomItemModalProps> = ({
  open,
  onClose,
  bomHeaderId,
  onSuccess,
}) => {
  const { showSnackbar } = useSnackbar();
  const [formData, setFormData] = useState({
    component: '',
    parent_item: '',
    quantity: 1,
    position: '',
    item_type: 'assembly',
    notes: '',
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async () => {
    try {
      const response = await fetch('/bom/api/items/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          bom_header: bomHeaderId,
          component: parseInt(formData.component),
          parent_item: formData.parent_item ? parseInt(formData.parent_item) : null,
          quantity: parseInt(formData.quantity.toString()),
          position: formData.position,
          item_type: formData.item_type,
          notes: formData.notes,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to add BOM item');
      }

      showSnackbar('BOM item added successfully', 'success');
      onSuccess();
      onClose();
    } catch (error) {
      showSnackbar('Failed to add BOM item', 'error');
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>Add BOM Item</DialogTitle>
      <DialogContent>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, pt: 2 }}>
          <TextField
            name="component"
            label="Component ID"
            value={formData.component}
            onChange={handleChange}
            fullWidth
            required
          />
          <TextField
            name="parent_item"
            label="Parent Item ID"
            value={formData.parent_item}
            onChange={handleChange}
            fullWidth
            helperText="Leave empty for top-level items"
          />
          <TextField
            name="quantity"
            label="Quantity"
            type="number"
            value={formData.quantity}
            onChange={handleChange}
            fullWidth
            required
          />
          <TextField
            name="position"
            label="Position"
            value={formData.position}
            onChange={handleChange}
            fullWidth
            required
          />
          <TextField
            name="item_type"
            label="Item Type"
            select
            value={formData.item_type}
            onChange={handleChange}
            fullWidth
            required
          >
            <MenuItem value="assembly">Assembly</MenuItem>
            <MenuItem value="component">Component</MenuItem>
            <MenuItem value="material">Material</MenuItem>
          </TextField>
          <TextField
            name="notes"
            label="Notes"
            value={formData.notes}
            onChange={handleChange}
            fullWidth
            multiline
            rows={3}
          />
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={handleSubmit} variant="contained" color="primary">
          Add Item
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AddBomItemModal; 