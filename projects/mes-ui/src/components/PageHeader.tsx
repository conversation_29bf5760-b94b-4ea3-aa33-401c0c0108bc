import React from 'react';
import { Box, Typography, IconButton, Button } from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';

interface PageHeaderProps {
  title: string;
  onBack?: () => void;
  actionButton?: {
    label: string;
    icon?: React.ReactNode;
    onClick: () => void;
  };
}

/**
 * A reusable page header component with optional back button and action button
 */
const PageHeader: React.FC<PageHeaderProps> = ({ title, onBack, actionButton }) => {
  return (
    <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        {onBack && (
          <IconButton
            onClick={onBack}
            sx={{ mr: 1, color: 'black' }}
          >
            <ArrowBackIcon />
          </IconButton>
        )}
        <Typography variant="h4" component="h1" sx={{
          fontWeight: 'bold',
          fontSize: '1.5rem',
          color: '#1a202c',
          borderBottom: '1px solid #e2e8f0',
          paddingBottom: '8px'
        }}>
          {title}
        </Typography>
      </Box>

      {actionButton && (
        <Button
          variant="outlined"
          startIcon={actionButton.icon}
          onClick={actionButton.onClick}
          sx={{
            borderColor: 'black',
            color: 'black',
            '&:hover': {
              borderColor: 'black',
              backgroundColor: 'rgba(0, 0, 0, 0.04)',
            }
          }}
        >
          {actionButton.label}
        </Button>
      )}
    </Box>
  );
};

export default PageHeader;
