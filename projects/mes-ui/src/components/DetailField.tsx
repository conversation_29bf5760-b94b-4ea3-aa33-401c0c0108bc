import React from 'react';
import { Typography, Box, Grid, SxProps, Theme } from '@mui/material';

interface DetailFieldProps {
  label: string;
  value: React.ReactNode;
  labelWidth?: number;
  valueWidth?: number;
  sx?: SxProps<Theme>;
}

/**
 * A reusable component for displaying a label-value pair in a detail view
 */
const DetailField: React.FC<DetailFieldProps> = ({
  label,
  value,
  labelWidth = 4,
  valueWidth = 8,
  sx,
}) => {
  return (
    <Grid container spacing={2} sx={sx}>
      <Grid item xs={labelWidth}>
        <Typography
          variant="body2"
          color="text.secondary"
          sx={{ fontSize: '0.95rem', fontWeight: 'medium' }}
        >
          {label}
        </Typography>
      </Grid>
      <Grid item xs={valueWidth}>
        {typeof value === 'string' || typeof value === 'number' ? (
          <Typography variant="body2" fontWeight="medium">
            {value}
          </Typography>
        ) : (
          value
        )}
      </Grid>
    </Grid>
  );
};

export default DetailField;
