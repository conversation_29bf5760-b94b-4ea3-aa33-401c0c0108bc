import React from "react";
import {
  FormControl,
  TextField,
  Autocomplete,
  FormHelperText,
  Box,
} from "@mui/material";
import { ResponsiveSize } from "../interfaces/form-config.api.interface";
import useScreen from "../hooks/useScreenSize";

interface AutocompleteFieldProps {
  label: string;
  name: string;
  value: string; // Single value
  options: { value: string; label: string }[];
  placeholder?: string;
  onChange: (e: any) => void;
  onBlur: (event: React.FocusEvent<any>) => void;
  error?: any;
  alignment?: "left" | "center" | "right";
  readonly: boolean;
  width?: ResponsiveSize;
  height?: ResponsiveSize;
  marginRight?: ResponsiveSize;
}

const SelectField: React.FC<AutocompleteFieldProps> = ({
  label,
  name,
  value,
  options,
  placeholder,
  onChange,
  onBlur,
  error,
  marginRight,
  alignment,
  width,
  readonly,
}) => {
  const {
    device,
    width: deviceWidth,
    marginRight: deviceRightMargin,
  } = useScreen(width, marginRight);

  const formattedValue =
    options.find((option) => option.value === value) || null;

  return (
    <Box
      sx={{
        width: deviceWidth || "48%",
        marginRight: deviceRightMargin || "0",
        marginBottom: device !== "mobile" ? "18px" : "10px",
      }}
    >
      <Autocomplete
        size="small"
        options={options}
        getOptionLabel={(option) => option.label}
        value={formattedValue}
        onChange={(event, newValue) => {
          onChange({
            target: {
              name,
              value: newValue?.value,
            },
          });
        }}
        disabled={readonly}
        isOptionEqualToValue={(option, val) => option.label === val.value}
        sx={{
          "& .MuiOutlinedInput-root": {
            borderRadius: "4px",
            fontSize: "0.875rem",
            fontFamily: "ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",
            backgroundColor: "#fff",
            color: "rgb(2, 8, 23)",
            height: "40px",
            "& fieldset": {
              borderColor: "#e2e8f0",
            },
            "&:hover fieldset": {
              borderColor: "#cbd5e1",
            },
            "&.Mui-focused fieldset": {
              borderColor: "#94a3b8",
              borderWidth: "1px",
            },
          },
          "& .MuiAutocomplete-endAdornment": {
            right: "8px",
          },
        }}
        renderInput={(params) => (
          <TextField
            {...params}
            label={label}
            placeholder={placeholder || "Select an option"}
            error={!!error}
            onBlur={onBlur}
            InputLabelProps={{
              sx: {
                fontSize: "0.875rem",
                color: "rgb(2, 8, 23)",
                fontFamily: "ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",
                "&.MuiInputLabel-shrink": {
                  transform: "translate(14px, -9px) scale(0.75)",
                },
              },
            }}
          />
        )}
      />
      {error && (
        <FormHelperText error sx={{ ml: 1.5, mt: 0.5, fontSize: "0.75rem" }}>
          {error}
        </FormHelperText>
      )}
    </Box>
  );
};

export default SelectField;
