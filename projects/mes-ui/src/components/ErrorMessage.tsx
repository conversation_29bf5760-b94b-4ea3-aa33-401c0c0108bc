import React from "react";

interface ErrorMessageProps {
  message: string;
  type?: "error" | "warning"; // You can extend this for more types
}

const ErrorMessage: React.FC<ErrorMessageProps> = ({
  message,
  type = "error",
}) => {
  const styles = {
    error: {
      textAlign: "center" as const,
      color: "lightcoral",
      backgroundColor: "#ffe6e6",
      padding: "1rem",
      borderRadius: "5px",
      fontSize: "1.6rem",
      margin: 2,
    },
    warning: {
      textAlign: "center" as const,
      color: "#856404",
      backgroundColor: "#fff3cd",
      padding: "1rem",
      borderRadius: "5px",
      fontSize: "2rem",
      margin: 2,
    },
  };

  return <div style={styles[type]}>{message}</div>;
};

export default ErrorMessage;
