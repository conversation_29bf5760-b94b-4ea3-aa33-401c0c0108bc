import React from "react";
import { Navigate } from "react-router-dom";
import { useAuth } from "../context/AuthContext";

import { PathRouteProps } from "react-router-dom";

interface PublicRouteProps extends PathRouteProps {
  component: React.ComponentType<any>;
  redirectPath?: string; // Path to redirect if the user is authenticated
}

const PublicRoute: React.FC<PublicRouteProps> = ({
  component: Component,
  redirectPath = "",
  ...rest
}) => {
  const { authState } = useAuth();

  const isAuthenticated = !!authState.accessToken;

  if (isAuthenticated && redirectPath) {
    return <Navigate to={redirectPath} replace />;
  }

  return <Component {...rest} />;
};

export default PublicRoute;
