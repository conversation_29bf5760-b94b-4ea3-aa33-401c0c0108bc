import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
  CircularProgress,
  Box,
  Typography,
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import { useDeleteProduct } from '../hooks/useProductTable';

interface DeleteProductDialogProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
  productId: number;
  productName: string;
}

const DeleteProductDialog: React.FC<DeleteProductDialogProps> = ({
  open,
  onClose,
  onSuccess,
  productId,
  productName,
}) => {
  // Delete product mutation
  const deleteProductMutation = useDeleteProduct();
  
  // Handle delete confirmation
  const handleDelete = async () => {
    try {
      await deleteProductMutation.mutateAsync(productId);
      onSuccess();
    } catch (error) {
      console.error('Error deleting product:', error);
    }
  };
  
  return (
    <Dialog
      open={open}
      onClose={deleteProductMutation.isPending ? undefined : onClose}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle sx={{ 
        bgcolor: 'rgba(211, 47, 47, 0.9)', 
        color: 'white',
        fontWeight: 'bold',
        py: 2,
        display: 'flex',
        alignItems: 'center',
        gap: 1
      }}>
        <DeleteIcon />
        <Typography variant="h6" component="span">
          Delete Product
        </Typography>
      </DialogTitle>
      
      <DialogContent sx={{ pt: 3, pb: 2 }}>
        <DialogContentText>
          Are you sure you want to delete the product <strong>{productName}</strong>?
        </DialogContentText>
        
        <Box sx={{ mt: 2 }}>
          <DialogContentText color="error" sx={{ fontWeight: 'medium' }}>
            This action cannot be undone. The product will be permanently removed from the system.
          </DialogContentText>
        </Box>
      </DialogContent>
      
      <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid rgba(0, 0, 0, 0.1)' }}>
        <Button 
          onClick={onClose} 
          disabled={deleteProductMutation.isPending}
          sx={{ 
            color: 'text.primary',
            '&:hover': {
              bgcolor: 'rgba(0, 0, 0, 0.04)',
            }
          }}
        >
          Cancel
        </Button>
        <Button 
          onClick={handleDelete} 
          variant="contained" 
          color="error"
          disabled={deleteProductMutation.isPending}
          startIcon={deleteProductMutation.isPending ? <CircularProgress size={20} color="inherit" /> : <DeleteIcon />}
        >
          {deleteProductMutation.isPending ? 'Deleting...' : 'Delete Product'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DeleteProductDialog;
