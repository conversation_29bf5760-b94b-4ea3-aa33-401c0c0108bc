import React from "react";
import { TextField } from "@mui/material";
import useScreen from "../hooks/useScreenSize";
import { ResponsiveSize } from "../interfaces/form-config.api.interface";

interface TextAreaFieldProps {
  label: string;
  name: string;
  value: string;
  placeholder?: string;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur: (event: React.FocusEvent<HTMLInputElement>) => void;
  error?: string | false | any;
  alignment?: "center" | "right" | "left";
  readonly: boolean;
  width?: ResponsiveSize;
  height?: ResponsiveSize;
  marginRight?: ResponsiveSize;
}

const TextAreaField: React.FC<TextAreaFieldProps> = ({
  label,
  name,
  value,
  placeholder,
  onChange,
  onBlur,
  error,
  width,
  marginRight,
  alignment,
  readonly,
}) => {
  const {
    device,
    width: deviceWidth,
    marginRight: deviceRightMargin,
  } = useScreen(width, marginRight);
  return (
    <TextField
      multiline
      rows={4}
      variant="outlined"
      label={label}
      name={name}
      size="small"
      value={value}
      onChange={onChange}
      onBlur={onBlur}
      placeholder={placeholder}
      error={!!error}
      disabled={readonly}
      style={{
        width: deviceWidth || "48%",
        marginRight: deviceRightMargin || "0",
        textAlign: alignment || "left",
        backgroundColor: "#fff",
        marginBottom: device !== "mobile" ? "18px" : "10px",
      }}
    />
  );
};

export default TextAreaField;
