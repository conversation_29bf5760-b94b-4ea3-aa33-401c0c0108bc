import React from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
  Box,
  IconButton,
  useTheme,
  useMediaQuery,
  Chip,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';

// Define the props interface
export interface ProductListModalProps {
  open: boolean;
  onClose: () => void;
  products: Array<{
    id: number;
    name: string;
    code: string;
  }>;
  routingName?: string;
}

const ProductListModal: React.FC<ProductListModalProps> = ({
  open,
  onClose,
  products,
  routingName,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));



  return (
    <>
      <Dialog
        open={open}
        onClose={onClose}
        maxWidth="md"
        fullWidth
        fullScreen={isMobile}
        slotProps={{
          paper: {
            sx: {
              borderRadius: isMobile ? 0 : '12px',
              maxHeight: '90vh',
            },
          },
        }}
      >
        <DialogTitle
          sx={{
            borderBottom: '1px solid #e2e8f0',
            padding: '20px 24px',
          }}
        >
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Box>
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 600,
                  color: '#1e293b',
                  fontSize: '18px',
                  mb: 0.5,
                }}
              >
                Associated Products
              </Typography>
              {routingName && (
                <Typography
                  variant="body2"
                  sx={{
                    color: '#64748b',
                    fontSize: '14px',
                  }}
                >
                  Routing: {routingName}
                </Typography>
              )}
            </Box>
            <IconButton
              onClick={onClose}
              size="small"
              sx={{
                color: '#64748b',
                '&:hover': {
                  backgroundColor: '#f1f5f9',
                },
              }}
            >
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>

        <DialogContent sx={{ padding: 0 }}>
          {products && products.length > 0 ? (
            <TableContainer component={Paper} elevation={0}>
              <Table>
                <TableHead>
                  <TableRow sx={{ backgroundColor: '#f8fafc' }}>
                    <TableCell
                      sx={{
                        fontWeight: 600,
                        color: '#475569',
                        borderBottom: '1px solid #e2e8f0',
                        width: '25%',
                      }}
                    >
                      Product ID
                    </TableCell>
                    <TableCell
                      sx={{
                        fontWeight: 600,
                        color: '#475569',
                        borderBottom: '1px solid #e2e8f0',
                        width: '40%',
                      }}
                    >
                      Product Name
                    </TableCell>
                    <TableCell
                      sx={{
                        fontWeight: 600,
                        color: '#475569',
                        borderBottom: '1px solid #e2e8f0',
                        width: '35%',
                      }}
                    >
                      Product Code
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {products.map((product) => (
                    <TableRow
                      key={product.id}
                      sx={{
                        '&:hover': {
                          backgroundColor: '#f8fafc',
                        },
                        '&:last-child td': {
                          borderBottom: 0,
                        },
                      }}
                    >
                      <TableCell
                        sx={{
                          color: '#64748b',
                          borderBottom: '1px solid #f1f5f9',
                          padding: '16px',
                        }}
                      >
                        <Typography variant="body2" sx={{ fontSize: '14px', fontWeight: 500 }}>
                          {product.id}
                        </Typography>
                      </TableCell>
                      <TableCell
                        sx={{
                          color: '#334155',
                          borderBottom: '1px solid #f1f5f9',
                          padding: '16px',
                        }}
                      >
                        <Typography variant="body2" sx={{ fontSize: '14px', fontWeight: 500 }}>
                          {product.name}
                        </Typography>
                      </TableCell>
                      <TableCell
                        sx={{
                          color: '#64748b',
                          borderBottom: '1px solid #f1f5f9',
                          padding: '16px',
                        }}
                      >
                        <Chip
                          label={product.code}
                          size="small"
                          sx={{
                            backgroundColor: '#e0f2fe',
                            color: '#0369a1',
                            fontWeight: 500,
                            fontSize: '12px',
                          }}
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <Box
              display="flex"
              justifyContent="center"
              alignItems="center"
              minHeight="200px"
            >
              <Typography color="textSecondary">
                No products associated with this routing
              </Typography>
            </Box>
          )}
        </DialogContent>

        <DialogActions
          sx={{
            borderTop: '1px solid #e2e8f0',
            padding: '16px 24px',
            justifyContent: 'flex-end',
          }}
        >
          <Button
            onClick={onClose}
            variant="outlined"
            sx={{
              textTransform: 'none',
              fontWeight: 500,
              borderColor: '#d1d5db',
              color: '#374151',
              '&:hover': {
                borderColor: '#9ca3af',
                backgroundColor: '#f9fafb',
              },
            }}
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ProductListModal;
