// UserMenu.tsx
import React, { useState } from "react";
import { AccountCircle, ExitToApp } from "@mui/icons-material";
import { Avatar, IconButton, Menu, MenuItem, Typography } from "@mui/material";
import styles from "../styles/UserMenue.module.scss";

interface UserMenuProps {
  user: {
    username: string;
    email: string;
  };
  onLogout: () => void;
}

const UserMenu: React.FC<UserMenuProps> = ({ user, onLogout }) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  return (
    <>
      <IconButton onClick={handleMenuOpen}>
        <Avatar className={styles.avatar}>
          {user.username.charAt(0).toUpperCase()}
        </Avatar>
      </IconButton>
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        keepMounted
        className={styles.menu}
      >
        <MenuItem disabled className={styles.disabledMenuItem}>
          <Typography variant="subtitle1">{user.username}</Typography>
        </MenuItem>
        <MenuItem disabled className={styles.disabledMenuItem}>
          <Typography variant="body2">{user.email}</Typography>
        </MenuItem>
        <MenuItem onClick={onLogout} className={styles.menuItem}>
          <ExitToApp fontSize="small" style={{ marginRight: 8 }} />
          Logout
        </MenuItem>
      </Menu>
    </>
  );
};

export default UserMenu;
