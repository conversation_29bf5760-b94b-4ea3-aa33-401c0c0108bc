import React, { useState, useEffect, useCallback } from "react";
import { <PERSON>, Button } from "@mui/material";
import * as Yup from "yup";
import FieldsComponent from "./FieldComponent";
import { FieldConfig } from "../interfaces/form.field.interface";
import componentRegistry from "../utils/componentRegistery";

interface Config {
  name: string;
  form_fields: FieldConfig[];
  [key: string]: any;
}

interface FormValues {
  [key: string]: string | string[];
}

interface FieldErrors {
  [key: string]: string;
}

interface Component {
  id: number;
  data: FormValues;
}
// // Define the props for the parent component
interface DynamicComponentProps {
  config: Config;
  handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void;
  onSubmit: (
    status?: "pass" | "fail" | "scrap" | "submit",
    field_name?: string,
    field_value?: any
  ) => void;
  nestedFieldValues: any;
}

const DynamicComponent: React.FC<DynamicComponentProps> = ({
  config,
  onSubmit,
  nestedFieldValues,
  handleFormConfigUpdate,
}) => {
  const [components, setComponents] = useState<any[]>([
    { id: Date.now(), data: { ...nestedFieldValues }, config },
  ]);

  const formStatusConfig = config?.form_fields?.find(
    (field) => field.component_name === "FormStatusComponent"
  );
  const [errors, setErrors] = useState<{ [key: number]: FieldErrors }>({});

  const createValidationSchema = (config: Config) => {
    return Yup.object().shape(
      config.form_fields.reduce((acc, field) => {
        if (field.type === "multi-select") {
          acc[field.name] = field.required
            ? Yup.array()
                .of(Yup.string().trim())
                .min(
                  1,
                  `${
                    field.label || field.name
                  } must have at least one selection`
                )
            : Yup.array().of(Yup.string().trim());
        } else if (field.required) {
          acc[field.name] = Yup.string()
            .trim()
            .required(`${field.label || field.name} is required`);
        }
        return acc;
      }, {} as Record<string, Yup.Schema<any>>)
    );
  };

  const validateFields = async (
    data: FormValues,
    validationSchema: any
  ): Promise<FieldErrors | null> => {
    try {
      await validationSchema.validate(data, { abortEarly: false });
      return null; // No errors
    } catch (err: any) {
      const fieldErrors: FieldErrors = {};
      err.inner.forEach((error: Yup.ValidationError) => {
        fieldErrors[error.path as string] = error.message;
      });
      return fieldErrors;
    }
  };

  const updateComponentConfig = (
    id: number,
    configUpdates: Partial<Config>
  ) => {
    setComponents((prevComponents) =>
      prevComponents.map((component) =>
        component.id === id
          ? {
              ...component,
              config: {
                ...component.config,
                ...configUpdates,
              },
            }
          : component
      )
    );
  };

  const handleFieldChange = (
    id: number,
    fieldName: string,
    value: string | string[]
  ) => {
    if (fieldName === "repair_action") {
      const isOther = value === "other";
      // Update the config for the specific component
      updateComponentConfig(id, {
        form_fields: config.form_fields.map((field: any) =>
          field.name === "repair_info"
            ? { ...field, required: isOther, isVisible: isOther }
            : field
        ),
      });
    }

    setComponents((prevComponents) =>
      prevComponents.map((component) =>
        component.id === id
          ? { ...component, data: { ...component.data, [fieldName]: value } }
          : component
      )
    );

    // Clear errors for the changed field
    setErrors((prevErrors) => {
      const updatedErrors = { ...prevErrors };
      if (updatedErrors[id]) {
        delete updatedErrors[id][fieldName];
        if (Object.keys(updatedErrors[id]).length === 0) {
          delete updatedErrors[id];
        }
      }
      return updatedErrors;
    });
  };

  const handleAddMore = async () => {
    const lastComponent = components[components.length - 1];
    const validationSchema = createValidationSchema(lastComponent.config);

    // Validate the last component's fields
    const validationErrors = await validateFields(
      lastComponent.data,
      validationSchema
    );

    if (validationErrors) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        [lastComponent.id]: validationErrors,
      }));
      return;
    }

    // Add a new component
    setComponents((prev) => [
      ...prev,
      { id: Date.now(), data: { ...nestedFieldValues }, config },
    ]);
  };

  const handleRemove = (id: number) => {
    setComponents((prev) => prev.filter((component) => component.id !== id));
    setErrors((prevErrors) => {
      const updatedErrors = { ...prevErrors };
      delete updatedErrors[id];
      return updatedErrors;
    });
  };

  const handleSubmit = async (status: "pass" | "fail" | "scrap" | "submit") => {
    const allErrors = await Promise.all(
      components.map((component) =>
        validateFields(component.data, createValidationSchema(component.config))
      )
    );

    const hasErrors = allErrors.some((error) => error !== null);

    if (hasErrors) {
      const errorObject: { [key: number]: FieldErrors } = {};
      components.forEach((component, index) => {
        if (allErrors[index]) {
          errorObject[component.id] = allErrors[index]!;
        }
      });
      setErrors(errorObject);
      return;
    }

    const faultDetails = components.map((component) => component.data);
    setComponents([{ id: components[0].id, data: {}, config }]);
    setErrors({});
    onSubmit(status, config.name, faultDetails);
  };

  useEffect(() => {
    createValidationSchema(config);
  }, [config, createValidationSchema]);

  useEffect(() => {
    if (nestedFieldValues) {
      setComponents([
        { id: Date.now(), data: { ...nestedFieldValues }, config },
      ]);
    }
  }, [JSON.stringify(nestedFieldValues)]);

  return (
    <>
      <div style={{ width: "100%" }}>
        <p
          style={{
            color: "#000",
            fontSize: "1rem",
            fontWeight: 500,
            marginBottom: 8,
          }}
        >
          {config.label}
        </p>
        {components.map((component) => (
          <FieldsComponent
            key={component.id}
            id={component.id}
            config={component.config}
            data={component.data}
            onChange={handleFieldChange}
            onRemove={() => handleRemove(component.id)}
            errors={errors[component.id] || {}}
            isMoreThanOneComponent={components.length > 1}
          />
        ))}
        <Box sx={{ marginTop: "20px" }}>
          <Button
            sx={{
              backgroundColor: "#3e4146",
              fontSize: "1rem",
              fontWeight: "bold",
              padding: "6px 16px",
            }}
            variant="contained"
            color="primary"
            onClick={handleAddMore}
          >
            Add More
          </Button>
        </Box>
      </div>
      {formStatusConfig &&
        (() => {
          const CustomComponent =
            componentRegistry[formStatusConfig.component_name];
          return CustomComponent ? (
            <CustomComponent
              key={formStatusConfig.name}
              label={formStatusConfig.label}
              name={formStatusConfig.name}
              disabled={false}
              handleSubmit={handleSubmit}
              options={formStatusConfig.options || []}
              config={formStatusConfig}
            />
          ) : null;
        })()}
    </>
  );
};

export default DynamicComponent;
