import React from "react";
import { <PERSON>, But<PERSON>, SelectChangeEvent } from "@mui/material";
import InputField from "./TextInput";
import SelectField from "./SelectInput";
import { FieldConfig } from "../interfaces/form.field.interface";
import TextAreaField from "./TextArea";
import CloseIcon from "@mui/icons-material/Close";
import MultiSelectField from "./MultiSelect";

interface Config {
  form_fields: FieldConfig[];
}

interface FormValues {
  [key: string]: string | string[];
}

interface FieldErrors {
  [key: string]: string;
}

interface FieldsComponentProps {
  id: number;
  config: Config;
  data: FormValues;
  onChange: (id: number, fieldName: string, value: string) => void;
  onRemove?: () => void;
  errors: FieldErrors;
  isMoreThanOneComponent: boolean;
}

const FieldsComponent: React.FC<FieldsComponentProps> = ({
  id,
  config,
  data,
  onChange,
  onRemove,
  errors,
  isMoreThanOneComponent,
}) => {
  const handleFieldChange = (
    e:
      | React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
      | SelectChangeEvent<string>
      | SelectChangeEvent<string[]>,
    field: FieldConfig
  ) => {
    let { value } = e.target as any;

    if (field.type === "multi-select") {
      value = typeof value === "string" ? value.split(",") : value;
    }

    onChange(id, field.name, value);
  };

  return (
    <div
      style={{
        display: "flex",
        flexWrap: "wrap",
        justifyContent: "space-between",
        alignItems: "center",
      }}
    >
      {config.form_fields.map((field) => {
        if (field.type === "component") {
          return;
        }
        if (!field.isVisible) {
          return;
        }
        const value: any = data[field.name] || "";
        switch (field.type) {
          case "text":
          case "number":
            return (
              <InputField
                key={field.name}
                label={field.label}
                name={field.name}
                type={field.type}
                value={value}
                placeholder={field.placeholder}
                onChange={(e) => handleFieldChange(e, field)}
                onBlur={() => {}}
                error={errors[field.name] || ""}
                marginRight={field.marginRight}
                width={field.width}
                readonly={field.readonly}
                alignment={field.alignment}
              />
            );
          case "textarea":
            return (
              <TextAreaField
                key={field.name}
                label={field.label}
                name={field.name}
                value={value}
                placeholder={field.placeholder}
                onChange={(e) => handleFieldChange(e, field)}
                error={errors[field.name] || ""}
                marginRight={field.marginRight}
                width={field.width}
                alignment={field.alignment}
                readonly={field.readonly}
                onBlur={() => {}}
              />
            );
          case "select":
            return (
              <SelectField
                key={field.name}
                label={field.label}
                name={field.name}
                readonly={field.readonly}
                value={value}
                options={field.options || []}
                placeholder={field.placeholder}
                onChange={(e) => handleFieldChange(e, field)}
                error={errors[field.name] || ""}
                marginRight={field.marginRight}
                width={field.width}
                alignment={field.alignment}
                onBlur={() => {}}
              />
            );
          case "multi-select":
            return (
              <MultiSelectField
                key={field.name}
                label={field.label}
                name={field.name}
                value={value || []}
                options={field.options || []}
                onChange={(e) => handleFieldChange(e, field)}
                error={errors[field.name] || ""}
                width={field.width}
                marginRight={field.marginRight}
                readonly={field.readonly}
              />
            );

          default:
            return null;
        }
      })}
      {isMoreThanOneComponent && (
        <Box
          sx={{
            width: "100%",
            display: "flex",
            marginTop: "5px",
            justifyContent: "flex-end",
          }}
        >
          <Button
            sx={{
              backgroundColor: "#d32f2f", // Bright red for visibility
              color: "#fff", // White text for contrast
              fontSize: "0.85rem", // Slightly larger font for readability
              fontWeight: "bold",
              padding: "6px 16px", // Adjust padding for a balanced look
              borderRadius: "8px", // Rounded corners for a modern style
              boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)", // Subtle shadow for depth
              display: "flex",
              alignItems: "center",
              "&:hover": {
                backgroundColor: "#b71c1c", // Darker red on hover
              },
            }}
            variant="contained"
            onClick={onRemove}
            startIcon={<CloseIcon />}
          >
            Remove
          </Button>
        </Box>
      )}
      <div
        style={{
          marginTop: "14px",
          background: "yellow",
          borderBottom: isMoreThanOneComponent ? "1px solid balck" : "", // Border applied conditionally
        }}
      />
    </div>
  );
};

export default FieldsComponent;
