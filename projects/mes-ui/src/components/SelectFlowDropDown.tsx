import React, { useState, useEffect } from "react";
import { Box, CircularProgress, ThemeProvider } from "@mui/material";
import SelectField from "../components/SelectInput";
import { useParts, useRoutingMutation, Part } from "../hooks/useRoutingApi";
import { theme } from "../utils/muiTheme";

interface PartDropdownButtonProps {
  onRoutingDataChange?: (data: any | null, error: Error | null) => void;
  onProductSelect?: (productId: string, productCode: string) => void;
}

const PartDropdownButton: React.FC<PartDropdownButtonProps> = ({
  onRoutingDataChange,
  onProductSelect,
}) => {
  const [selectedPart, setSelectedPart] = useState("");
  const [options, setOptions] = useState<Array<{ value: string; label: string; id?: number }>>([]);

  // Use the useParts hook to fetch parts
  const { data: partsData, isLoading: isPartsLoading } = useParts();

  // Use the useRoutingMutation hook to fetch routing data
  const routingMutation = useRoutingMutation();

  // Update options when parts data changes
  useEffect(() => {
    if (partsData && Array.isArray(partsData)) {
      const newOptions = partsData.map((part: Part) => ({
        value: part.code,
        label: `${part.name}${part.has_routing ? ' (Has Routing)' : ''}`,
        id: part.id
      }));

      setOptions(newOptions);
    }
  }, [partsData]);

  // Handle part selection
  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const productCode = e.target.value;
    setSelectedPart(productCode);

    if (productCode) {
      // Find the selected part to get its ID
      const selectedPart = partsData?.find((part: Part) => part.code === productCode);
      const productId = selectedPart?.id?.toString() || '';

      // Call the onProductSelect callback if provided
      if (onProductSelect && productId) {
        onProductSelect(productId, productCode);
      }

      // Fetch routing data for the selected product
      routingMutation.mutate(productCode, {
        onSuccess: (data) => {
          if (onRoutingDataChange) {
            onRoutingDataChange(data, null);
          }
        },
        onError: (err: Error) => {
          if (onRoutingDataChange) {
            onRoutingDataChange(null, err);
          }
        }
      });
    } else if (onRoutingDataChange) {
      // If nothing selected, clear the routing data
      onRoutingDataChange(null, null);
    }
  };

  // Show loading indicator if data is being fetched
  const isLoadingAny = isPartsLoading || routingMutation.isPending;
  if (isLoadingAny && options.length === 0) {
    return <CircularProgress size={24} color="primary" />;
  }

  // Render options with visual indication of routing status
  const renderOptions = () => {
    return options.map(option => ({
      value: option.value,
      label: option.label,
    }));
  };

  return (
    <Box sx={{ width: "300px" }}>
      <ThemeProvider theme={theme}>
        <SelectField
          label="Select Part"
          name="flowPart"
          value={selectedPart}
          options={renderOptions()}
          placeholder="Select a part..."
          onChange={handleChange}
          onBlur={() => { }}
          error={null}
          readonly={false}
          width={{ desktop: "100%", tablet: "100%", mobile: "100%" }}
        />
      </ThemeProvider>
    </Box>
  );
};

export default PartDropdownButton;
