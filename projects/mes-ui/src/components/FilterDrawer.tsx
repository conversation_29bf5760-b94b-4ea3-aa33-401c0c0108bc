import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  IconButton,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  TextField,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';

interface FilterField {
  id: string;
  label: string;
  value: string;
}

interface FilterDrawerProps {
  open: boolean;
  onClose: () => void;
  title: string;
  fields: FilterField[];
  onFieldChange: (id: string, value: string) => void;
  onApply: () => void;
  onReset: () => void;
}

/**
 * A reusable filter drawer component
 */
const FilterDrawer: React.FC<FilterDrawerProps> = ({
  open,
  onClose,
  title,
  fields,
  onFieldChange,
  onApply,
  onReset,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
    >
      <Box sx={{ width: isMobile ? '100vw' : 400, p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h6" component="h2">
            {title}
          </Typography>
          <IconButton onClick={onClose}>
            <CloseIcon />
          </IconButton>
        </Box>

        <Divider sx={{ mb: 3 }} />

        <Stack spacing={2}>
          {fields.map((field) => (
            <TextField
              key={field.id}
              fullWidth
              label={field.label}
              value={field.value}
              onChange={(e) => onFieldChange(field.id, e.target.value)}
              variant="outlined"
              size="small"
              sx={{
                '& .MuiOutlinedInput-root': {
                  height: '32px',
                  borderRadius: '4px',
                  '& fieldset': {
                    borderColor: '#e2e8f0',
                  },
                  '&:hover fieldset': {
                    borderColor: 'rgb(2, 8, 23)',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: 'rgb(2, 8, 23)',
                    borderWidth: '1px',
                  },
                  '& input': {
                    padding: '6px 12px',
                    fontSize: '14px',
                    fontFamily: 'ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
                  }
                },
                '& .MuiInputLabel-root': {
                  fontSize: '14px',
                  fontFamily: 'ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
                  color: 'rgb(2, 8, 23)',
                  '&.Mui-focused': {
                    color: 'rgb(2, 8, 23)',
                  }
                }
              }}
            />
          ))}
        </Stack>

        <Box sx={{ mt: 4, display: 'flex', justifyContent: 'space-between' }}>
          <Button
            variant="outlined"
            onClick={onReset}
            sx={{
              borderColor: 'rgb(2, 8, 23)',
              color: 'rgb(2, 8, 23)',
              borderRadius: '4px',
              height: '32px',
              textTransform: 'none',
              fontFamily: 'ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
              fontSize: '14px',
              fontWeight: 500,
              '&:hover': {
                borderColor: 'rgb(2, 8, 23)',
                backgroundColor: 'rgba(2, 8, 23, 0.04)',
              }
            }}
          >
            Reset
          </Button>
          <Button
            variant="contained"
            onClick={onApply}
            sx={{
              backgroundColor: 'rgb(2, 8, 23)',
              color: 'white',
              borderRadius: '4px',
              height: '32px',
              textTransform: 'none',
              fontFamily: 'ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
              fontSize: '14px',
              fontWeight: 500,
              boxShadow: 'none',
              '&:hover': {
                backgroundColor: 'rgba(2, 8, 23, 0.9)',
                boxShadow: 'none',
              }
            }}
          >
            Apply Filters
          </Button>
        </Box>
      </Box>
    </Drawer>
  );
};

export default FilterDrawer;
