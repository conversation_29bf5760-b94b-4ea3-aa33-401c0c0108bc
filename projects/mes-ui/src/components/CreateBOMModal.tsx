import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  TextField,
  ThemeProvider,
} from '@mui/material';
import { LocalizationProvider, DatePicker } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { useSnackbar } from '../context/SnackBarContext';
import axiosInstance from '../utils/axiosInstance';
import { theme } from '../utils/muiTheme';
import useScreen from '../hooks/useScreenSize';

interface CreateBOMModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const CreateBOMModal: React.FC<CreateBOMModalProps> = ({
  open,
  onClose,
  onSuccess,
}) => {
  const { showSnackbar } = useSnackbar();
  const { device } = useScreen();
  const isMobile = device === 'mobile';

  const [bomData, setBomData] = useState({
    name: '',
    product_name: '',
    version: '',
    created_at: '',
    components: '',
  });

  const [date, setDate] = useState<Date | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setBomData(prev => ({
      ...prev,
      [name]: value,
    }));
    if (validationErrors[name]) {
      setValidationErrors(prev => ({
        ...prev,
        [name]: '',
      }));
    }
  };

  const handleDateChange = (newDate: Date | null) => {
    setDate(newDate);
    setBomData(prev => ({
      ...prev,
      created_at: newDate ? newDate.toISOString().split('T')[0] : '',
    }));
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};
    if (!bomData.name) errors.name = 'BOM Name is required';
    if (!bomData.product_name) errors.product_name = 'Product Name is required';
    if (!bomData.version) errors.version = 'Version is required';
    if (!bomData.components) errors.components = 'Components are required';
    return errors;
  };

  const handleSubmit = async () => {
    const errors = validateForm();
    if (Object.keys(errors).length > 0) {
      setValidationErrors(errors);
      return;
    }

    try {
      await axiosInstance.post('/bom/api/headers/1', bomData);
      showSnackbar('BOM created successfully', 'success');
      onSuccess();
      onClose();
    } catch (error: any) {
      showSnackbar(error.response?.data?.message || 'Failed to create BOM', 'error');
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>Create New BOM</DialogTitle>
      <DialogContent>
        <ThemeProvider theme={theme}>
          <Box sx={{ mt: 2, display: 'flex', flexDirection: 'column', gap: 2 }}>
            <TextField
              label="BOM Name"
              name="name"
              value={bomData.name}
              onChange={handleChange}
              fullWidth
              error={!!validationErrors.name}
              helperText={validationErrors.name}
              size={isMobile ? 'small' : 'medium'}
            />
            <TextField
              label="Product Name"
              name="product_name"
              value={bomData.product_name}
              onChange={handleChange}
              fullWidth
              error={!!validationErrors.product_name}
              helperText={validationErrors.product_name}
              size={isMobile ? 'small' : 'medium'}
            />
            <TextField
              label="Version"
              name="version"
              value={bomData.version}
              onChange={handleChange}
              fullWidth
              error={!!validationErrors.version}
              helperText={validationErrors.version}
              size={isMobile ? 'small' : 'medium'}
            />
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label="Created At"
                value={date}
                onChange={handleDateChange}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    size: isMobile ? 'small' : 'medium',
                    error: !!validationErrors.created_at,
                    helperText: validationErrors.created_at,
                  },
                }}
                format="yyyy-MM-dd"
              />
            </LocalizationProvider>
            <TextField
              label="Components"
              name="components"
              value={bomData.components}
              onChange={handleChange}
              fullWidth
              multiline
              rows={4}
              error={!!validationErrors.components}
              helperText={validationErrors.components}
              size={isMobile ? 'small' : 'medium'}
            />
          </Box>
        </ThemeProvider>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={handleSubmit} variant="contained" color="primary">
          Create
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CreateBOMModal; 