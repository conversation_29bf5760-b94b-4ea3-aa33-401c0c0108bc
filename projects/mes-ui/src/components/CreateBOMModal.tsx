import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  CircularProgress,
  Alert,
} from '@mui/material';
import { LocalizationProvider, DatePicker } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { useSnackbar } from '../context/SnackBarContext';
import { useAuth } from '../context/AuthContext';
import { useCreateBOM, CreateBOMPayload } from '../hooks/useBOMCreation';
import { useProductTable } from '../hooks/useProductTable';

interface CreateBOMModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const CreateBOMModal: React.FC<CreateBOMModalProps> = ({
  open,
  onClose,
  onSuccess,
}) => {
  const { showSnackbar } = useSnackbar();
  const { authState } = useAuth();

  // Get user ID from auth context (preferred) or fallback to localStorage
  const userId = authState?.user?.id || 1; // Fallback to 1 if no user ID

  // Form state
  const [formData, setFormData] = useState<CreateBOMPayload>({
    name: '',
    code: '',
    description: '',
    product: 0,
    version: '',
    effective_date: '',
    created_by: userId,
  });

  const [effectiveDate, setEffectiveDate] = useState<Date | null>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Get products for the dropdown using the correct API endpoint
  const { data: productsResponse, isLoading: isProductsLoading } = useProductTable({
    page: 1,
    page_size: 100, // Get 100 products as requested
  });

  // Create BOM mutation
  const createBOMMutation = useCreateBOM();

  // Update created_by field when user ID changes
  useEffect(() => {
    setFormData(prev => ({
      ...prev,
      created_by: userId,
    }));
  }, [userId]);

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Handle product selection
  const handleProductChange = (event: SelectChangeEvent<number>) => {
    const value = event.target.value as number;
    setFormData(prev => ({
      ...prev,
      product: value,
    }));

    // Clear error for product field
    if (errors.product) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.product;
        return newErrors;
      });
    }
  };

  // Handle effective date change
  const handleEffectiveDateChange = (newDate: Date | null) => {
    setEffectiveDate(newDate);
    setFormData(prev => ({
      ...prev,
      effective_date: newDate ? newDate.toISOString().split('T')[0] : '',
    }));
  };

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'BOM name is required';
    }

    if (!formData.code.trim()) {
      newErrors.code = 'BOM code is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    if (!formData.product || formData.product === 0) {
      newErrors.product = 'Product selection is required';
    }

    if (!formData.version.trim()) {
      newErrors.version = 'Version is required';
    }

    if (!formData.effective_date) {
      newErrors.effective_date = 'Effective date is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      await createBOMMutation.mutateAsync(formData);
      showSnackbar('BOM created successfully', 'success');
      onSuccess();
      onClose();
      // Reset form
      setFormData({
        name: '',
        code: '',
        description: '',
        product: 0,
        version: '',
        effective_date: '',
        created_by: userId,
      });
      setEffectiveDate(null);
    } catch (error) {
      console.error('Error creating BOM:', error);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={createBOMMutation.isPending ? undefined : onClose}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle sx={{
        bgcolor: 'rgba(0, 0, 0, 0.8)',
        color: 'white',
        fontWeight: 'bold',
        py: 2
      }}>
        Create New BOM
      </DialogTitle>

      <form onSubmit={handleSubmit}>
        <DialogContent sx={{ pt: 3 }}>
          {createBOMMutation.isError && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {createBOMMutation.error instanceof Error
                ? createBOMMutation.error.message
                : 'An error occurred while creating the BOM'}
            </Alert>
          )}

          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3, mt: 1 }}>
            <TextField
              label="BOM Name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              fullWidth
              required
              error={!!errors.name}
              helperText={errors.name}
              disabled={createBOMMutation.isPending}
            />

            <TextField
              label="BOM Code"
              name="code"
              value={formData.code}
              onChange={handleInputChange}
              fullWidth
              required
              error={!!errors.code}
              helperText={errors.code}
              disabled={createBOMMutation.isPending}
            />

            <TextField
              label="Description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              fullWidth
              required
              multiline
              rows={3}
              error={!!errors.description}
              helperText={errors.description}
              disabled={createBOMMutation.isPending}
            />

            <FormControl fullWidth required error={!!errors.product}>
              <InputLabel>Product</InputLabel>
              <Select
                value={formData.product || ''}
                onChange={handleProductChange}
                label="Product"
                disabled={createBOMMutation.isPending || isProductsLoading}
              >
                {isProductsLoading ? (
                  <MenuItem disabled>
                    <CircularProgress size={20} sx={{ mr: 1 }} />
                    Loading products...
                  </MenuItem>
                ) : (
                  productsResponse?.results?.map((product: any) => (
                    <MenuItem key={product.id} value={product.id}>
                      {product.name} ({product.code})
                    </MenuItem>
                  ))
                )}
              </Select>
              {errors.product && (
                <Box sx={{ color: 'error.main', fontSize: '0.75rem', mt: 0.5, ml: 1.75 }}>
                  {errors.product}
                </Box>
              )}
            </FormControl>

            <TextField
              label="Version"
              name="version"
              value={formData.version}
              onChange={handleInputChange}
              fullWidth
              required
              error={!!errors.version}
              helperText={errors.version}
              disabled={createBOMMutation.isPending}
            />

            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label="Effective Date"
                value={effectiveDate}
                onChange={handleEffectiveDateChange}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    required: true,
                    error: !!errors.effective_date,
                    helperText: errors.effective_date,
                    disabled: createBOMMutation.isPending,
                  },
                }}
                format="yyyy-MM-dd"
              />
            </LocalizationProvider>
          </Box>
        </DialogContent>

        <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid rgba(0, 0, 0, 0.1)' }}>
          <Button
            onClick={onClose}
            disabled={createBOMMutation.isPending}
            sx={{
              color: 'text.primary',
              '&:hover': {
                bgcolor: 'rgba(0, 0, 0, 0.04)',
              }
            }}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={createBOMMutation.isPending}
            sx={{
              bgcolor: 'rgba(0, 0, 0, 0.8)',
              color: 'white',
              '&:hover': {
                bgcolor: 'rgba(0, 0, 0, 0.7)',
              }
            }}
          >
            {createBOMMutation.isPending ? (
              <>
                <CircularProgress size={20} sx={{ mr: 1, color: 'white' }} />
                Creating...
              </>
            ) : (
              'Create BOM'
            )}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default CreateBOMModal; 