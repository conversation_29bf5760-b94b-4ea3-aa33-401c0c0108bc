import React, { forwardRef } from "react";
import { TextField } from "@mui/material";
import useScreen from "../hooks/useScreenSize";
import { ResponsiveSize } from "../interfaces/form-config.api.interface";

interface InputFieldProps {
  label: string;
  name: string;
  type?: string;
  value: string;
  placeholder?: string;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
  error?: string | false | any;
  alignment?: "left" | "right" | "center";
  readonly: boolean;
  onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  width?: ResponsiveSize;
  height?: ResponsiveSize;
  marginRight?: ResponsiveSize;
}

const InputField = forwardRef<HTMLInputElement, InputFieldProps>(
  (
    {
      label,
      name,
      type = "text",
      value,
      placeholder,
      onChange,
      onBlur,
      error,
      marginRight,
      alignment,
      width,
      readonly,
      onKeyDown,
    },
    ref
  ) => {
    const {
      device,
      width: deviceWidth,
      marginRight: deviceRightMargin,
    } = useScreen(width, marginRight);

    return (
      <TextField
        name={name}
        size="medium"
        label={label}
        variant="outlined"
        type={type}
        value={value}
        onChange={onChange}
        onBlur={onBlur}
        placeholder={placeholder}
        error={!!error}
        disabled={readonly}
        inputRef={ref}
        onKeyDown={(e: any) => {
          if (onKeyDown) onKeyDown(e);
        }}
        style={{
          marginBottom: device !== "mobile" ? "18px" : "10px",
          width: deviceWidth || "48%",
          marginRight: deviceRightMargin || "0px",
          textAlign: alignment || "left",
          backgroundColor: "#fff",
        }}
      />
    );
  }
);

export default InputField;
