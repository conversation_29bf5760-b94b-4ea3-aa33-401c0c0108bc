import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Paper,
  Typography,
  Box,
  CircularProgress,
  useTheme,
  TextField,
  Button,
} from "@mui/material";
import EditIcon from "@mui/icons-material/Edit";
import SearchIcon from "@mui/icons-material/Search";
import styles from "../styles/EventList.module.scss";

export interface ColumnConfig {
  label: string;
  key: string;
  format?: (value: any, row?: any) => React.ReactNode;
  align?: "left" | "right" | "center";
  width?: string | number;
}

export interface ActionConfig {
  onView?: (row: any) => void;
  onEdit?: (row: any) => void;
  onDelete?: (row: any) => void;
  viewButtonText?: string;
}

interface DynamicTableProps {
  headers: ColumnConfig[];
  data: Record<string, any>[];
  count: number;
  page: number;
  rowsPerPage: number;
  loading?: boolean;
  error?: any;
  onPageChange: (newPage: number) => void;
  onRowsPerPageChange: (size: number) => void;
  rowsPerPageOptions?: number[];
  actions?: ActionConfig;
  onRowClick?: (row: any) => void;
  onSearch?: (searchTerm: string) => void;
  searchPlaceholder?: string;
}

const DynamicTable: React.FC<DynamicTableProps> = ({
  headers,
  data,
  count,
  page,
  rowsPerPage,
  loading,
  error,
  onPageChange,
  onRowsPerPageChange,
  rowsPerPageOptions = [5, 10, 25, 50],
  actions,
  onRowClick,
  onSearch,
  searchPlaceholder = "Search...",
}) => {
  const theme = useTheme();
  const [searchTerm, setSearchTerm] = useState("");

  // Check if we need to add an actions column
  const hasActions = actions && (actions.onView || actions.onEdit || actions.onDelete);

  // Handle search input change
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  // Handle search submit
  const handleSearchSubmit = () => {
    if (onSearch) {
      onSearch(searchTerm);
    }
  };

  // Handle key press in search field
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && onSearch) {
      onSearch(searchTerm);
    }
  };

  return (
    <Box className={styles.eventListContainer}>
      {/* Search field above the table */}
      {onSearch && (
        <Box sx={{
          display: 'flex',
          justifyContent: 'flex-start',
          mb: 2,
          mt: 0,
          width: '100%'
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <Box sx={{ position: 'relative', width: '320px' }}>
              <TextField
                size="medium"
                placeholder={searchPlaceholder}
                value={searchTerm}
                onChange={handleSearchChange}
                onKeyDown={handleKeyDown}
                fullWidth
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: '8px',
                    paddingLeft: '40px', // Make room for the icon
                    height: '44px',
                    backgroundColor: '#fff',
                    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                    transition: 'all 0.2s ease-in-out',
                    '& fieldset': {
                      borderColor: '#e2e8f0',
                      borderWidth: '1px',
                    },
                    '&:hover': {
                      boxShadow: '0 2px 6px rgba(0, 0, 0, 0.12)',
                      '& fieldset': {
                        borderColor: '#cbd5e1',
                      },
                    },
                    '&:focus-within': {
                      boxShadow: '0 0 0 3px rgba(2, 8, 23, 0.1)',
                      '& fieldset': {
                        borderColor: 'rgb(2, 8, 23)',
                        borderWidth: '2px',
                      },
                    },
                    '& input': {
                      padding: '10px 14px',
                      fontSize: '14px',
                      fontFamily: 'ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
                      color: 'rgb(2, 8, 23)',
                      '&::placeholder': {
                        color: '#94a3b8',
                        opacity: 1,
                      },
                    }
                  }
                }}
              />
              <SearchIcon
                sx={{
                  position: 'absolute',
                  left: '12px',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  color: '#94a3b8',
                  fontSize: '18px'
                }}
              />
            </Box>
            <Button
              variant="contained"
              size="medium"
              onClick={handleSearchSubmit}
              sx={{
                backgroundColor: 'rgb(2, 8, 23)',
                color: '#fff',
                textTransform: 'none',
                fontWeight: 500,
                fontSize: '14px',
                padding: '10px 20px',
                height: '44px',
                borderRadius: '8px',
                fontFamily: 'ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                transition: 'all 0.2s ease-in-out',
                '&:hover': {
                  backgroundColor: 'rgba(2, 8, 23, 0.9)',
                  boxShadow: '0 2px 6px rgba(0, 0, 0, 0.15)',
                  transform: 'translateY(-1px)',
                }
              }}
            >
              Search
            </Button>
          </Box>
        </Box>
      )}

      <TableContainer component={Paper} className={styles.tableContainer} sx={{
        borderRadius: '8px',
        boxShadow: 'none',
        overflow: 'hidden',
        border: '1px solid #e2e8f0',
      }}>
        <Table size="small" sx={{ borderCollapse: 'separate', borderSpacing: 0 }}>
          <TableHead>
            <TableRow sx={{
              backgroundColor: '#f8fafc',
              '& th:first-of-type': {
                borderTopLeftRadius: '8px'
              },
              '& th:last-child': {
                borderTopRightRadius: '8px'
              }
            }}>
              {headers.map((header) => (
                <TableCell
                  key={header.key}
                  align={header.align || "left"}
                  style={{ width: header.width }}
                  sx={{
                    fontWeight: 600,
                    fontSize: '0.8rem',
                    color: '#4a5568', // Lighter color to match reference UI
                    borderBottom: '1px solid #e2e8f0',
                    paddingBottom: '10px',
                    paddingTop: '10px',
                    height: '48px', // Match reference UI height
                    paddingLeft: '16px',
                    paddingRight: '16px',
                  }}
                >
                  {header.label}
                </TableCell>
              ))}
              {hasActions && (
                <TableCell
                  align="right"
                  style={{ width: 120 }}
                  sx={{
                    fontWeight: 600,
                    fontSize: '0.8rem',
                    color: '#4a5568', // Lighter color to match reference UI
                    borderBottom: '1px solid #e2e8f0',
                    paddingBottom: '10px',
                    paddingTop: '10px',
                    height: '48px', // Match reference UI height
                    paddingLeft: '16px',
                    paddingRight: '16px',
                  }}
                >
                  Actions
                </TableCell>
              )}
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={hasActions ? headers.length + 1 : headers.length} align="center">
                  <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                    <CircularProgress />
                  </Box>
                </TableCell>
              </TableRow>
            ) : error ? (
              <TableRow>
                <TableCell colSpan={hasActions ? headers.length + 1 : headers.length} align="center">
                  <Typography color="error">
                    Error loading data. Please try again.
                  </Typography>
                </TableCell>
              </TableRow>
            ) : data.length > 0 ? (
              data.map((row, index) => (
                <TableRow
                  key={index}
                  hover
                  onClick={onRowClick ? () => onRowClick(row) : undefined}
                  sx={{
                    cursor: onRowClick ? 'pointer' : 'default',
                    '&:hover': {
                      backgroundColor: '#f1f5f9',
                      boxShadow: 'inset 0 0 0 1px #e2e8f0',
                    },
                    '& .MuiTableCell-root': {
                      borderBottom: index === data.length - 1 ? 'none' : '1px solid #e2e8f0',
                      fontSize: '0.875rem',
                      padding: '10px 16px',
                      height: '48px',
                    },
                    '&:last-child .MuiTableCell-root:first-of-type': {
                      borderBottomLeftRadius: count > rowsPerPage ? '0' : '8px',
                    },
                    '&:last-child .MuiTableCell-root:last-child': {
                      borderBottomRightRadius: count > rowsPerPage ? '0' : '8px',
                    }
                  }}
                >
                  {headers.map((header) => (
                    <TableCell
                      key={header.key}
                      align={header.align || "left"}
                      sx={{
                        maxWidth: '250px',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap'
                      }}
                    >
                      <div style={{
                        maxWidth: '100%',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap',
                        color: 'rgb(2, 8, 23)',
                        fontSize: '16px',
                        fontFamily: 'ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
                        fontWeight: 400,
                        lineHeight: '24px'
                      }}>
                        {header.format
                          ? header.format(row[header.key], row)
                          : row[header.key] !== null && row[header.key] !== undefined
                            ? row[header.key]
                            : "-"}
                      </div>
                    </TableCell>
                  ))}

                  {hasActions && (
                    <TableCell align="right">
                      <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
                        {actions?.onView && (
                          <Button
                            variant="contained"
                            size="small"
                            startIcon={<EditIcon sx={{ fontSize: 16 }} />}
                            onClick={(e: React.MouseEvent) => {
                              e.stopPropagation();
                              actions.onView!(row);
                            }}
                            sx={{
                              backgroundColor: '#0f172a',
                              color: '#fff',
                              textTransform: 'none',
                              fontWeight: 500,
                              fontSize: '13px',
                              padding: '4px 10px',
                              height: '36px',
                              minWidth: '110px',
                              borderRadius: '4px',
                              fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif',
                              boxShadow: 'none',
                              '&:hover': {
                                backgroundColor: '#1e293b',
                              }
                            }}
                          >
                            {actions.viewButtonText || "View"}
                          </Button>
                        )}

                        {actions?.onEdit && (
                          <Button
                            variant="contained"
                            size="small"
                            startIcon={<EditIcon sx={{ fontSize: 16 }} />}
                            onClick={(e: React.MouseEvent) => {
                              e.stopPropagation();
                              actions.onEdit!(row);
                            }}
                            sx={{
                              backgroundColor: '#0f172a',
                              color: '#fff',
                              textTransform: 'none',
                              fontWeight: 500,
                              fontSize: '13px',
                              padding: '4px 10px',
                              height: '36px',
                              minWidth: '110px',
                              borderRadius: '4px',
                              fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif',
                              boxShadow: 'none',
                              '&:hover': {
                                backgroundColor: '#1e293b',
                              }
                            }}
                          >
                            Edit
                          </Button>
                        )}

                        {actions?.onDelete && (
                          <Button
                            variant="contained"
                            size="small"
                            onClick={(e: React.MouseEvent) => {
                              e.stopPropagation();
                              actions.onDelete!(row);
                            }}
                            sx={{
                              backgroundColor: '#dc2626',
                              color: '#fff',
                              textTransform: 'none',
                              fontWeight: 500,
                              fontSize: '13px',
                              padding: '4px 10px',
                              height: '36px',
                              minWidth: '110px',
                              borderRadius: '4px',
                              fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif',
                              boxShadow: 'none',
                              '&:hover': {
                                backgroundColor: '#b91c1c',
                              }
                            }}
                          >
                            Delete
                          </Button>
                        )}
                      </Box>
                    </TableCell>
                  )}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={hasActions ? headers.length + 1 : headers.length} align="center">
                  <Typography sx={{
                    py: 3,
                    color: 'rgb(2, 8, 23)',
                    fontSize: '16px',
                    fontFamily: 'ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
                    fontWeight: 400,
                    lineHeight: '24px'
                  }}>
                    No data available
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Pagination Controls */}
      <Box sx={{
        display: "flex",
        justifyContent: "flex-end",
        mt: 0,
        p: 1.5,
        borderTop: '1px solid #e2e8f0',
        borderBottomLeftRadius: '8px',
        borderBottomRightRadius: '8px',
        backgroundColor: '#f8fafc'
      }}>
        <TablePagination
          component="div"
          count={count}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={(_, newPage) => onPageChange(newPage)}
          onRowsPerPageChange={(e) => onRowsPerPageChange(parseInt(e.target.value, 10))}
          rowsPerPageOptions={rowsPerPageOptions}
          labelRowsPerPage="Rows:"
          sx={{
            '.MuiTablePagination-selectLabel, .MuiTablePagination-displayedRows': {
              margin: 0,
              fontSize: '13px',
              color: '#4a5568',
            },
            '.MuiTablePagination-select': {
              fontSize: '13px',
              marginRight: '8px',
              marginLeft: '4px',
            },
            '.MuiTablePagination-actions': {
              marginLeft: 1,
            },
            '.MuiIconButton-root': {
              color: '#4a5568',
              padding: '4px',
            },
            '.MuiInputBase-root': {
              marginRight: '16px',
            }
          }}
        />
      </Box>
    </Box>
  );
};

export default DynamicTable;
