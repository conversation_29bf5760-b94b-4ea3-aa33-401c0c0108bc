import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Stack,
  useTheme,
  SelectChangeEvent,
  Alert,
} from '@mui/material';
import { useAssignRouting } from '../hooks/useProductTable';
import { useRoutingTable } from '../hooks/useRoutingTable';

interface AssignRoutingFormProps {
  productId: number;
  onSuccess: () => void;
  onCancel: () => void;
}

const AssignRoutingForm: React.FC<AssignRoutingFormProps> = ({
  productId,
  onSuccess,
  onCancel,
}) => {
  const theme = useTheme();
  const [selectedRoutingId, setSelectedRoutingId] = useState<number | ''>('');
  const [error, setError] = useState<string | null>(null);

  // Fetch available routings
  const { data: routingsData, isLoading: isRoutingsLoading } = useRoutingTable({
    page: 1,
    page_size: 100, // Get a large number of routings
  });

  // Assign routing mutation
  const assignRoutingMutation = useAssignRouting();

  // Handle routing selection change
  const handleRoutingChange = (event: SelectChangeEvent<number | ''>) => {
    setSelectedRoutingId(event.target.value as number);
    setError(null);
  };

  // Handle form submission
  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (selectedRoutingId === '') {
      setError('Please select a routing configuration');
      return;
    }

    try {
      console.log('Assigning routing:', { productId, routingId: selectedRoutingId });

      // Execute the mutation and get the result
      const result = await assignRoutingMutation.mutateAsync({
        productId,
        routingId: selectedRoutingId as number,
      });

      console.log('Routing assignment result:', result);

      // Call the onSuccess callback with the updated product data
      if (result.product) {
        onSuccess();
      } else {
        // If we don't have the updated product data, reload the page
        window.location.reload();
      }
    } catch (error: any) {
      console.error('Error assigning routing:', error);
      setError(`Failed to assign routing: ${error.message}`);
    }
  };

  return (
    <Card sx={{
      boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
      borderRadius: 2,
      overflow: 'hidden',
    }}>
      <Box sx={{
        p: 2,
        borderBottom: `1px solid ${theme.palette.divider}`,
        bgcolor: 'rgba(0, 0, 0, 0.8)',
        color: 'white',
        display: 'flex',
        alignItems: 'center',
        gap: 1
      }}>
        <Typography variant="h6" sx={{ fontWeight: 'bold', fontSize: '1.1rem' }}>
          Assign Routing
        </Typography>
      </Box>

      <CardContent sx={{ p: 4 }}>
        <form onSubmit={handleSubmit}>
          <Stack spacing={4}>
            <Typography variant="body1" sx={{ color: 'text.secondary' }}>
              Select a routing to assign to this product. This will determine the manufacturing process flow for the product.
            </Typography>

            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}

            <FormControl fullWidth>
              <InputLabel id="routing-select-label">Routing</InputLabel>
              <Select
                labelId="routing-select-label"
                id="routing-select"
                value={selectedRoutingId}
                label="Routing"
                onChange={handleRoutingChange}
                disabled={isRoutingsLoading || assignRoutingMutation.isPending}
              >
                {isRoutingsLoading ? (
                  <MenuItem value="">
                    <CircularProgress size={20} /> Loading...
                  </MenuItem>
                ) : routingsData?.results && routingsData.results.length > 0 ? (
                  routingsData.results.map((routing) => (
                    <MenuItem key={routing.id} value={routing.id}>
                      {routing.name} ({routing.code})
                    </MenuItem>
                  ))
                ) : (
                  <MenuItem value="">No routings available</MenuItem>
                )}
              </Select>
            </FormControl>

            {selectedRoutingId !== '' && routingsData?.results && (
              <Box sx={{
                border: '1px solid #e0e0e0',
                borderLeft: '4px solid rgb(0, 0, 0)',
                borderRadius: '4px',
                overflow: 'hidden',
                mt: 2
              }}>
                <Box sx={{
                  p: 2,
                  backgroundColor: '#fafafa',
                  borderBottom: '1px solid #f0f0f0'
                }}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 500 }}>
                    Selected Routing Configuration
                  </Typography>
                </Box>

                {routingsData.results
                  .filter(route => route.id === selectedRoutingId)
                  .map(route => (
                    <Box key={route.id} sx={{ p: 2 }}>
                      <Typography variant="body1" sx={{ fontWeight: 500 }}>
                        {route.name}
                      </Typography>
                      <Box sx={{ display: 'flex', gap: 3, mt: 1, alignItems: 'center' }}>
                        <Typography variant="caption" sx={{ fontWeight: 500 }}>
                          Code: {route.code}
                        </Typography>
                      </Box>
                    </Box>
                  ))
                }
              </Box>
            )}

            <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 2 }}>
              <Button
                variant="outlined"
                onClick={onCancel}
                disabled={assignRoutingMutation.isPending}
                sx={{
                  borderColor: 'black',
                  color: 'black',
                  '&:hover': {
                    borderColor: 'black',
                    backgroundColor: 'rgba(0, 0, 0, 0.04)',
                  }
                }}
              >
                Cancel
              </Button>

              <Button
                type="submit"
                variant="contained"
                disabled={selectedRoutingId === '' || assignRoutingMutation.isPending}
                sx={{
                  bgcolor: 'rgba(0, 0, 0, 0.8)',
                  color: 'white',
                  '&:hover': {
                    bgcolor: 'rgba(0, 0, 0, 0.7)',
                  }
                }}
              >
                {assignRoutingMutation.isPending ? (
                  <>
                    <CircularProgress size={20} sx={{ mr: 1, color: 'white' }} />
                    Assigning...
                  </>
                ) : (
                  'Assign Routing'
                )}
              </Button>
            </Box>
          </Stack>
        </form>
      </CardContent>
    </Card>
  );
};

export default AssignRoutingForm;
