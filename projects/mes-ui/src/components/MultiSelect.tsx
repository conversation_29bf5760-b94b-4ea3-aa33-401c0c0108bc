import React from "react";
import { Autocomplete, TextField, Checkbox, FormControl } from "@mui/material";
import CheckBoxOutlineBlankIcon from "@mui/icons-material/CheckBoxOutlineBlank";
import CheckBoxIcon from "@mui/icons-material/CheckBox";
import { ResponsiveSize } from "../interfaces/form-config.api.interface";
import useScreen from "../hooks/useScreenSize";

interface SelectFieldProps {
  label: string;
  name: string;
  value: string | string[]; // Supports single or multiple values
  options: { value: string; label: string }[];
  onChange: (e: any) => void;
  error?: string | false;
  placeholder?: string;
  readonly?: boolean;
  isMulti?: boolean; // Flag for multi-select
  width?: ResponsiveSize;
  height?: ResponsiveSize;
  marginRight?: ResponsiveSize;
}

const SelectField: React.FC<SelectFieldProps> = ({
  label,
  name,
  value,
  options,
  onChange,
  error,
  placeholder,
  width,
  readonly,
  marginRight,
  isMulti = true,
}) => {
  const {
    device,
    width: deviceWidth,
    marginRight: deviceRightMargin,
  } = useScreen(width, marginRight);
  const icon = <CheckBoxOutlineBlankIcon fontSize="small" />;
  const checkedIcon = <CheckBoxIcon fontSize="small" />;

  const handleChange = (event: React.SyntheticEvent, newValue: any) => {
    onChange({
      target: {
        name,
        value: newValue.map((o: any) => o.value),
      },
    });
  };

  return (
    <Autocomplete
      disabled={readonly}
      style={{
        width: deviceWidth ?? "100%",
        marginRight: deviceRightMargin || "0",
        marginBottom: device !== "mobile" ? "18px" : "10px",
      }}
      size="medium"
      multiple
      disableCloseOnSelect
      options={options}
      getOptionLabel={(option) => option.label}
      onChange={handleChange}
      readOnly={readonly}
      renderOption={(props, option, { selected }) => {
        const { key, ...optionProps } = props;
        return (
          <li key={key} {...optionProps}>
            <Checkbox
              icon={icon}
              checkedIcon={checkedIcon}
              style={{ marginRight: 2 }}
              checked={selected}
              sx={{
                "&.Mui-checked": {
                  color: "rgb(41, 44, 49)",
                },
              }}
            />

            {option.label}
          </li>
        );
      }}
      renderInput={(params) => (
        <TextField
          {...params}
          size="medium"
          label={label}
          placeholder={placeholder || "Select options"}
          error={!!error}
          style={{ width: "100%" }}
        />
      )}
    />
  );
};

export default SelectField;
