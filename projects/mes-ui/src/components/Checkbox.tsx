import React from "react";
import {
  FormControl,
  FormControlLabel,
  Checkbox,
  FormHelperText,
} from "@mui/material";
import { ResponsiveSize } from "../interfaces/form-config.api.interface";
import useScreen from "../hooks/useScreenSize";

interface CheckboxFieldProps {
  label: string;
  value: string;
  name: string;
  checked: boolean; // The checkbox checked value, controlled by Formik
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void; // Formik onChange handler
  error?: string | false | any;
  alignment?: "left" | "right" | "center";
  width?: ResponsiveSize;
  height?: ResponsiveSize;
  marginRight?: ResponsiveSize;
}

const CheckboxField: React.FC<CheckboxFieldProps> = ({
  label,
  name,
  checked,
  onChange,
  error,
  width,
  marginRight,
  alignment = "left",
  value,
}) => {
  const { width: deviceWidth, marginRight: deviceRightMargin } = useScreen(
    width,
    marginRight
  );
  return (
    <FormControl
      component="fieldset"
      size="medium"
      error={!!error}
      style={{
        width: deviceWidth || "48%",
        marginRight: deviceRightMargin || "0",
        textAlign: alignment || "left",
      }}
    >
      <FormControlLabel
        control={
          <Checkbox
            checked={checked ? true : false} // Bind Formik's value (true/false)
            onChange={onChange} // Formik's onChange to toggle the checkbox
            name={name}
            value={value}
            sx={{
              "&.Mui-checked": {
                color: "rgb(41, 44, 49)",
              },
              backgroundColor: "white", // Make the background white
              borderRadius: "4px",
              padding: "0px",
              margin: "0px 8px",
            }}
          />
        }
        label={label}
      />
    </FormControl>
  );
};

export default CheckboxField;
