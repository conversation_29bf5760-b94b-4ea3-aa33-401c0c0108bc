import React from "react";
import { Navigate, PathRouteProps } from "react-router-dom";
import { useAuth } from "../context/AuthContext";

interface ProtectedRouteProps extends PathRouteProps {
  requiredRole?: string;
  component: React.ComponentType<any>;
  routeType: "protected" | "public";
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  requiredRole,
  component: Component,
  routeType,
  ...rest
}) => {
  const { authState } = useAuth();

  const isAuthenticated =
    routeType === "protected" ? !!authState.accessToken : true;

  const isAuthorized = requiredRole
    ? authState.user?.role === requiredRole
    : true;

  if (!isAuthenticated) {
    return <Navigate to="/login" />;
  }

  if (requiredRole && !isAuthorized) {
    return <Navigate to="/not-authorized" />;
  }

  return <Component {...rest} />;
};

export default ProtectedRoute;
