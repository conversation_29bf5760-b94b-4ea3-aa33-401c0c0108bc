import React from "react";
import { Menu } from "@mui/icons-material";
import { IconButton, Box } from "@mui/material";
import Style from "../styles/Header.module.scss";
import UserMenu from "./UserMenue";

interface HeaderProps {
  onLogout: () => void;
  user: {
    username: string;
    email: string;
  };
  onSidebarToggle: (event: React.MouseEvent) => void;
  sidebarRef: React.MutableRefObject<HTMLDivElement | null>;
}

const Header: React.FC<HeaderProps> = ({ onLogout, user, onSidebarToggle }) => {
  return (
    <header className={Style.header}>
      <div className={Style.leftSection}>
        <Box
          id="sidebar-toggle"
          onClick={(e) => {
            if (user?.username) {
              e.stopPropagation();
              onSidebarToggle(e);
              return;
            }
          }}
          sx={{
            display: { xs: 'block', sm: 'block', md: 'block', lg: 'none' } // Hide on desktop
          }}
        >
          <IconButton sx={{ color: '#1a202c' }}>
            <Menu />
          </IconButton>
        </Box>
      </div>

      {/* Center section with title - only on mobile/tablet */}
      <div className={Style.centerSection}>
        <h1 className={Style.title}>AssemblyPro</h1>
      </div>

      {user && (
        <div className={Style.userMenu}>
          <UserMenu user={user} onLogout={onLogout} />
        </div>
      )}
    </header>
  );
};

export default Header;
