import React, { useState } from "react";
import { Box, Button } from "@mui/material";
import CheckIcon from "@mui/icons-material/Check";
import CloseIcon from "@mui/icons-material/Close";
import DeleteIcon from "@mui/icons-material/Delete";
import ConfirmationModal from "./ConfirmationModalUi";
import Style from "../styles/passFailButton.module.scss";

interface PassFailButtonProps {
  handleSubmit: (status?: "pass" | "fail" | "scrap" | "submit") => void;
  setFormData: (field: string, value: any) => void;
  handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void;
  disabled: boolean;
  options: { label: string; value: string }[];
  handleComponentActions: (event: any) => void;
  setFailedButtonState: React.Dispatch<React.SetStateAction<string | null>>;
  failedButtonState: string;
}

const PassFailButton: React.FC<PassFailButtonProps> = ({
  handleSubmit,
  disabled,
  options,
  handleComponentActions,
  setFailedButtonState,
  failedButtonState,
}) => {
  // actions state
  const [isModalOpen, setModalOpen] = useState(false);
  const handleModalClose = () => {
    setModalOpen(false);
  };

  const handleClick = (status: "pass" | "fail", e: any) => {
    if (status === "pass") {
      handleSubmit("pass");
    } else if (status === "fail") {
      if (failedButtonState === "fail") {
        const extendedEvent = {
          ...e,
          clickedState: null,
        };

        setFailedButtonState(null);
        handleComponentActions(extendedEvent);
      } else {
        const extendedEvent = {
          ...e,
          clickedState: status,
        };
        setFailedButtonState("fail");
        handleComponentActions(extendedEvent);
      }
    }
  };

  const handleScrapClick = () => {
    handleModalClose();
    handleSubmit("scrap");
  };

  return (
    <>
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          marginTop: 2,
          marginBottom: 2,
          width: "100%",
          gap: 6,
          justifyContent: options.some((option) => option.value === "scrap")
            ? "space-between"
            : "",
        }}
      >
        {options.some((option) => option.value === "submit") && (
          <Button
            className={Style.submitBtn}
            variant="contained"
            onClick={() => handleSubmit("submit")}
            disabled={disabled}
          >
            {disabled ? "Submitting..." : "Submit"}
          </Button>
        )}

        {options.some((option) => option.value === "pass") && (
          <Button
            className={Style.passBtn}
            variant="contained"
            onClick={(e) => handleClick("pass", e)}
            disabled={failedButtonState === "fail" || disabled}
          >
            <CheckIcon />
            {disabled && failedButtonState !== "fail"
              ? "Submitting..."
              : "Pass"}
          </Button>
        )}

        {options.some((option) => option.value === "fail") && (
          <Button
            className={Style.failBtn}
            variant="contained"
            onClick={(e) => handleClick("fail", e)}
            disabled={disabled}
          >
            <CloseIcon />
            {failedButtonState === "fail" ? "Fail Selected" : "Fail"}
          </Button>
        )}
        {options.some((option) => option.value === "scrap") && (
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              cursor: "pointer",
              color: "#000000",
              textDecoration: "underline",
              fontSize: "1rem",
              "&:hover": {
                color: "#1a3ada",
              },
            }}
            onClick={() => setModalOpen(true)}
          >
            <DeleteIcon
              sx={{
                marginRight: 1,
                color: "#851d1d",
              }}
            />
            Scrap
          </Box>
        )}
      </Box>
      <ConfirmationModal
        open={isModalOpen}
        title="Are You sure you want to scrap ?"
        onCancel={handleModalClose}
        onClose={handleScrapClick}
      />
    </>
  );
};

export default PassFailButton;
