import React, { useMemo } from "react";
import styles from "../styles/StepBar.module.scss";

interface ExecutionStep {
  executed: string | null;
  should_execute: string | null;
  time: string;
  validity: Record<string, boolean>;
}

interface ConfigProps {
  route_sequence: ExecutionStep[];
  schema: {
    routing_schema: {
      components: Record<
        string,
        {
          name: string;
        }
      >;
    };
  };
  components: Record<string, { name: string }>; // Component mapping
}

interface StepBarProps {
  config?: ConfigProps;
  isLoading: boolean;
  error: unknown;
}

const SkeletonLoader = () => (
  <div className={styles.skeletonContainer}>
    {Array.from({ length: 5 }).map((_, index) => (
      <div key={index} className={styles.skeletonStep}>
        <div className={styles.skeletonIcon} />
        <div className={styles.skeletonLabel} />
      </div>
    ))}
  </div>
);

const StepBar: React.FC<StepBarProps> = ({ config, isLoading, error }) => {
  if (isLoading) return <SkeletonLoader />;
  if (error) {
    return null;
  }
  if (!config?.route_sequence?.length) return null;

  const { route_sequence, schema } = config;

  let nextExecutableIndex = route_sequence.findIndex(
    (step) => step.executed === null
  );

  const steps = useMemo(
    () =>
      route_sequence.map((step, index) => {
        const comp = schema?.routing_schema?.components ?? {};
        const success =
          step.executed &&
          step.executed === step.should_execute &&
          step.should_execute !== null;
        const label = step.should_execute
          ? comp?.[step.should_execute]?.name
          : step.executed
            ? comp?.[step.executed]?.name
            : "";
        const isPending = step.executed === null;
        let isValid = true;
        if (step.validity?.is_valid !== undefined) {
          isValid = step.validity.is_valid
        }
        return {
          ...step,
          label,
          status: isPending
            ? index === nextExecutableIndex
              ? "next"
              : "pending"
            : !isValid
              ? "failed"
              : success ? "success" : "failed"
        };
      }),
    [route_sequence]
  );

  return (
    <div className={styles.stepContainer}>
      <div className={styles.stepScroll}>
        {steps.map((step, index) => (
          <React.Fragment key={index}>
            <div className={`${styles.step} ${styles[step.status]}`}>
              <span
                className={styles.label}
                style={{ fontSize: "14px", fontWeight: "bold" }}
              >
                {step.label} {step.status === "next" ? "(Next)" : ""}
              </span>
              {step.time && (
                <span className={styles.time} style={{ fontSize: "12px", color: "#402b2b" }}>
                  {new Date(step.time).toLocaleString()}
                </span>
              )}
            </div>
            {index < steps.length - 1 && (
              <div
                className={`${styles.connector} ${step.status !== "success"
                  ? styles.pendingLine
                  : styles.visited
                  }`}
              />
            )}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
};

export default StepBar;
