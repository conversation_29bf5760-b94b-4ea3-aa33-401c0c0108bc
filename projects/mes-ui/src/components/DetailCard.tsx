import React from 'react';
import { Card, CardContent, Typography, Box } from '@mui/material';
import { sectionHeaderStyle } from '../utils/commonStyles';

interface DetailCardProps {
  title: string;
  children: React.ReactNode;
  sx?: any;
}

/**
 * A reusable card component for displaying details with a title
 */
const DetailCard: React.FC<DetailCardProps> = ({ title, children, sx = {} }) => {
  return (
    <Card sx={{ boxShadow: '0 4px 12px rgba(0,0,0,0.1)', borderRadius: 2, mb: 3, overflow: 'hidden', ...sx }}>
      <Box sx={sectionHeaderStyle}>
        <Typography variant="h6">
          {title}
        </Typography>
      </Box>

      <CardContent sx={{ p: 3 }}>
        <Box>
          {children}
        </Box>
      </CardContent>
    </Card>
  );
};

export default DetailCard;
