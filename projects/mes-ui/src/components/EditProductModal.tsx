import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Box,
  Typography,
  CircularProgress,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
} from '@mui/material';
import { useUpdateProduct, CreateProductPayload } from '../hooks/useProductTable';
import { useRoutingTable } from '../hooks/useRoutingTable';

interface EditProductModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess: (updatedProduct: any) => void;
  productId: number;
  initialData: {
    name: string;
    code: string;
    description?: string;
    routing_id?: number | null;
  };
}

const EditProductModal: React.FC<EditProductModalProps> = ({
  open,
  onClose,
  onSuccess,
  productId,
  initialData,
}) => {
  // Form state
  const [formData, setFormData] = useState<Partial<CreateProductPayload>>({
    name: initialData.name,
    code: initialData.code,
    description: initialData.description || '',
    routing_id: initialData.routing_id || undefined,
  });

  // Update form data when initialData changes
  useEffect(() => {
    setFormData({
      name: initialData.name,
      code: initialData.code,
      description: initialData.description || '',
      routing_id: initialData.routing_id || undefined,
    });
  }, [initialData, open]);

  // Error state
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Get routings for the dropdown
  const { data: routingsData, isLoading: isRoutingsLoading } = useRoutingTable({
    page: 1,
    page_size: 100, // Get a large number of routings
  });

  // Update product mutation
  const updateProductMutation = useUpdateProduct();

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Handle routing selection
  const handleRoutingChange = (event: SelectChangeEvent<number | ''>) => {
    const value = event.target.value;
    setFormData(prev => ({
      ...prev,
      routing_id: value === '' ? undefined : value as number,
    }));
  };

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name?.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.code?.trim()) {
      newErrors.code = 'Code is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      // Execute the mutation and get the updated product data
      const updatedProduct = await updateProductMutation.mutateAsync({
        productId,
        productData: formData,
      });

      // Pass the updated product data to the onSuccess callback
      onSuccess(updatedProduct);
      onClose();
    } catch (error) {
      console.error('Error updating product:', error);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={updateProductMutation.isPending ? undefined : onClose}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle sx={{
        bgcolor: 'rgba(0, 0, 0, 0.8)',
        color: 'white',
        fontWeight: 'bold',
        py: 2
      }}>
        Edit Product
      </DialogTitle>

      <form onSubmit={handleSubmit}>
        <DialogContent sx={{ pt: 3 }}>
          {updateProductMutation.isError && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {updateProductMutation.error instanceof Error
                ? updateProductMutation.error.message
                : 'An error occurred while updating the product'}
            </Alert>
          )}

          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3, mt: 1 }}>
            <TextField
              label="Product Name"
              name="name"
              value={formData.name || ''}
              onChange={handleInputChange}
              fullWidth
              required
              error={!!errors.name}
              helperText={errors.name}
              disabled={updateProductMutation.isPending}
            />

            <TextField
              label="Product Code"
              name="code"
              value={formData.code || ''}
              onChange={handleInputChange}
              fullWidth
              required
              error={!!errors.code}
              helperText={errors.code}
              disabled={updateProductMutation.isPending}
            />

            <TextField
              label="Description"
              name="description"
              value={formData.description || ''}
              onChange={handleInputChange}
              fullWidth
              multiline
              rows={3}
              disabled={updateProductMutation.isPending}
            />

            <FormControl fullWidth>
              <InputLabel id="routing-select-label">Routing (Optional)</InputLabel>
              <Select
                labelId="routing-select-label"
                id="routing-select"
                value={formData.routing_id || ''}
                label="Routing (Optional)"
                onChange={handleRoutingChange}
                disabled={isRoutingsLoading || updateProductMutation.isPending}
              >
                <MenuItem value="">
                  <em>None</em>
                </MenuItem>
                {isRoutingsLoading ? (
                  <MenuItem value="" disabled>
                    <CircularProgress size={20} /> Loading...
                  </MenuItem>
                ) : routingsData?.results && routingsData.results.length > 0 ? (
                  routingsData.results.map((routing) => (
                    <MenuItem key={routing.id} value={routing.id}>
                      {routing.name} ({routing.code})
                    </MenuItem>
                  ))
                ) : (
                  <MenuItem value="" disabled>
                    No routings available
                  </MenuItem>
                )}
              </Select>
            </FormControl>
          </Box>
        </DialogContent>

        <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid rgba(0, 0, 0, 0.1)' }}>
          <Button
            onClick={onClose}
            disabled={updateProductMutation.isPending}
            sx={{
              color: 'text.primary',
              '&:hover': {
                bgcolor: 'rgba(0, 0, 0, 0.04)',
              }
            }}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={updateProductMutation.isPending}
            sx={{
              bgcolor: 'rgba(0, 0, 0, 0.8)',
              color: 'white',
              '&:hover': {
                bgcolor: 'rgba(0, 0, 0, 0.7)',
              }
            }}
          >
            {updateProductMutation.isPending ? (
              <>
                <CircularProgress size={20} sx={{ mr: 1, color: 'white' }} />
                Updating...
              </>
            ) : (
              'Update Product'
            )}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default EditProductModal;
