import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Title,
  DialogContent,
  <PERSON>alogA<PERSON>,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  Typography,
  Box,
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import axiosInstance from '../utils/axiosInstance';
import AddBomItemModal from './AddBomItemModal';

interface ProductBOMsModalProps {
  open: boolean;
  onClose: () => void;
  productCode: string;
}

interface Component {
  id: number;
  code: string;
  name: string;
  description: string;
}

interface BOMStructure {
  id: number;
  component_id: number;
  component: Component;
  quantity: string;
  position: string;
  item_type: string;
  notes: string;
  children: BOMStructure[];
}

interface BOM {
  id: number;
  name: string;
  code: string;
  version: string;
  status: string;
  effective_date: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  structure: BOMStructure[];
}

interface ProductBOMResponse {
  product: {
    id: number;
    code: string;
    name: string;
    description: string;
    type_id: string;
    is_active: boolean;
  };
  bom_count: number;
  boms: BOM[];
}

interface FlattenedComponent {
  id: number;
  component_id: number;
  component: Component;
  quantity: string;
  position: string;
  item_type: string;
  notes: string;
  level: number;
  parent: string;
}

const ProductBOMsModal: React.FC<ProductBOMsModalProps> = ({
  open,
  onClose,
  productCode,
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<ProductBOMResponse | null>(null);
  const [addItemModalOpen, setAddItemModalOpen] = useState(false);
  const [selectedBomId, setSelectedBomId] = useState<number | null>(null);

  const fetchBOMs = async () => {
    if (!productCode) return;
    
    setLoading(true);
    setError(null);
    try {
      const response = await axiosInstance.get(`/bom/api/headers/product_boms?product_code=${productCode}`);
      setData(response.data);
    } catch (err) {
      setError('Failed to fetch BOMs');
      console.error('Error fetching BOMs:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (open) {
      fetchBOMs();
    }
  }, [productCode, open]);

  const flattenBOMStructure = (
    structure: BOMStructure[],
    level: number = 0,
    parent: string = '',
    result: FlattenedComponent[] = []
  ): FlattenedComponent[] => {
    structure.forEach((item) => {
      result.push({
        ...item,
        level,
        parent,
      });
      if (item.children.length > 0) {
        flattenBOMStructure(item.children, level + 1, item.component.name, result);
      }
    });
    return result;
  };

  const handleAddItem = (bomId: number) => {
    setSelectedBomId(bomId);
    setAddItemModalOpen(true);
  };

  const handleAddItemSuccess = () => {
    // Refresh BOM data
    if (productCode) {
      fetchBOMs();
    }
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={onClose}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: '8px',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
          },
        }}
      >
        <DialogTitle sx={{ 
          borderBottom: '1px solid #e2e8f0',
          pb: 2,
          fontSize: '18px',
          fontWeight: 600,
        }}>
          Product BOMs - {data?.product.name}
        </DialogTitle>
        <DialogContent sx={{ mt: 2 }}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress size={24} />
            </Box>
          ) : error ? (
            <Typography color="error" sx={{ p: 2 }}>{error}</Typography>
          ) : !data?.boms.length ? (
            <Typography sx={{ p: 2 }}>No BOMs found for this product.</Typography>
          ) : (
            data.boms.map((bom) => (
              <Box key={bom.id} sx={{ mb: 3 }}>
                <Box sx={{ 
                  mb: 1, 
                  p: 2, 
                  backgroundColor: '#f8fafc',
                  borderRadius: '4px',
                  border: '1px solid #e2e8f0',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}>
                  <Box>
                    <Typography variant="subtitle1" sx={{ fontWeight: 500, mb: 1 }}>
                      {bom.name}
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 2 }}>
                      <Typography variant="body2" color="text.secondary">
                        Version: {bom.version}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Status: {bom.status}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Effective: {new Date(bom.effective_date).toLocaleDateString()}
                      </Typography>
                    </Box>
                  </Box>
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={() => handleAddItem(bom.id)}
                    sx={{
                      backgroundColor: 'rgb(0, 0, 0)',
                      color: 'rgb(255, 255, 255)',
                      '&:hover': {
                        backgroundColor: 'rgb(0, 0, 0)',
                      },
                    }}
                  >
                    Add Item
                  </Button>
                </Box>
                <TableContainer component={Paper} sx={{ boxShadow: 'none', border: '1px solid #e2e8f0' }}>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Component Name</TableCell>
                        <TableCell>Level</TableCell>
                        <TableCell>Parent</TableCell>
                        <TableCell>Code</TableCell>
                        <TableCell>Quantity</TableCell>
                        <TableCell>Position</TableCell>
                        <TableCell>Type</TableCell>
                        <TableCell>Notes</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {flattenBOMStructure(bom.structure).map((item) => (
                        <TableRow key={item.id}>
                          <TableCell>{item.component.name}</TableCell>
                          <TableCell>{item.level}</TableCell>
                          <TableCell>{item.parent || '-'}</TableCell>
                          <TableCell>{item.component.code}</TableCell>
                          <TableCell>{item.quantity}</TableCell>
                          <TableCell>{item.position}</TableCell>
                          <TableCell>{item.item_type}</TableCell>
                          <TableCell>{item.notes || '-'}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
            ))
          )}
        </DialogContent>
        <DialogActions sx={{ borderTop: '1px solid #e2e8f0', p: 2 }}>
          <Button
            onClick={onClose}
            sx={{
              textTransform: 'none',
              color: '#64748b',
              '&:hover': {
                backgroundColor: '#f1f5f9',
              },
            }}
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {selectedBomId && (
        <AddBomItemModal
          open={addItemModalOpen}
          onClose={() => {
            setAddItemModalOpen(false);
            setSelectedBomId(null);
          }}
          bomHeaderId={selectedBomId}
          onSuccess={handleAddItemSuccess}
        />
      )}
    </>
  );
};

export default ProductBOMsModal; 