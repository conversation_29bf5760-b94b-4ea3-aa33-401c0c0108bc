import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>T<PERSON>le,
  DialogContent,
  <PERSON>alogA<PERSON>,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  Typography,
  Box,
  IconButton,
  ButtonGroup,
  TextField,
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import AddChildIcon from '@mui/icons-material/PlaylistAdd';
import axiosInstance from '../utils/axiosInstance';
import AddBomItemModal from './AddBomItemModal';
import { useSnackbar } from '../context/SnackBarContext';

interface ProductBOMsModalProps {
  open: boolean;
  onClose: () => void;
  productCode: string;
  status?: string;
}

interface Component {
  id: number;
  code: string;
  name: string;
  description: string;
}

interface BOMStructure {
  id: number;
  component_id: number;
  component: Component;
  quantity: string;
  position: string;
  item_type: string;
  notes: string;
  children: BOMStructure[];
}

interface BOM {
  id: number;
  name: string;
  code: string;
  version: string;
  status: string;
  effective_date: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  structure: BOMStructure[];
}

interface ProductBOMResponse {
  product: {
    id: number;
    code: string;
    name: string;
    description: string;
    type_id: string;
    is_active: boolean;
  };
  bom_count: number;
  boms: BOM[];
}

interface FlattenedComponent {
  id: number;
  component_id: number;
  component: Component;
  quantity: string;
  position: string;
  item_type: string;
  notes: string;
  level: number;
  parent: string;
}

interface EditQuantityModalProps {
  open: boolean;
  onClose: () => void;
  currentQuantity: string;
  itemId: number;
  onSuccess: () => void;
}

const EditQuantityModal: React.FC<EditQuantityModalProps> = ({
  open,
  onClose,
  currentQuantity,
  itemId,
  onSuccess,
}) => {
  const [quantity, setQuantity] = useState(currentQuantity);
  const [loading, setLoading] = useState(false);
  const { showSnackbar } = useSnackbar();

  const handleSubmit = async () => {
    setLoading(true);
    try {
      await axiosInstance.patch(`/bom/api/items/${itemId}/`, {
        quantity: parseFloat(quantity),
      });
      showSnackbar('Quantity updated successfully', 'success');
      onSuccess();
      onClose();
    } catch (error) {
      showSnackbar('Failed to update quantity', 'error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="xs" fullWidth>
      <DialogTitle>Edit Quantity</DialogTitle>
      <DialogContent>
        <Box sx={{ pt: 2 }}>
          <TextField
            fullWidth
            label="Quantity"
            type="number"
            value={quantity}
            onChange={(e) => setQuantity(e.target.value)}
            inputProps={{ min: 0, step: 0.01 }}
          />
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} disabled={loading}>Cancel</Button>
        <Button 
          onClick={handleSubmit} 
          variant="contained" 
          disabled={loading}
        >
          {loading ? <CircularProgress size={24} /> : 'Update'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

const ProductBOMsModal: React.FC<ProductBOMsModalProps> = ({
  open,
  onClose,
  productCode,
  status = 'active',
}) => {
  const { showSnackbar } = useSnackbar();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<ProductBOMResponse | null>(null);
  const [addItemModalOpen, setAddItemModalOpen] = useState(false);
  const [selectedBomId, setSelectedBomId] = useState<number | null>(null);
  const [expandedItems, setExpandedItems] = useState<Set<number>>(new Set());
  const [editQuantityModalOpen, setEditQuantityModalOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<{ id: number; quantity: string } | null>(null);
  const [editingItemId, setEditingItemId] = useState<number | null>(null);

  const fetchBOMs = async () => {
    if (!productCode) return;
    
    setLoading(true);
    setError(null);
    try {
      const response = await axiosInstance.get(`/bom/api/headers/product_boms?product_code=${productCode}&status=${status}`);
      
      setData(response.data);
    } catch (err) {
      setError('Failed to fetch BOMs');
      console.error('Error fetching BOMs:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (open) {
      fetchBOMs();
    }
  }, [productCode, open]);

  const toggleItem = (itemId: number) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(itemId)) {
        newSet.delete(itemId);
      } else {
        newSet.add(itemId);
      }
      return newSet;
    });
  };

  const handleEditQuantity = (item: BOMStructure) => {
    setSelectedItem({ id: item.id, quantity: item.quantity });
    setEditQuantityModalOpen(true);
  };

  const handleAddChild = (item: BOMStructure) => {
    setSelectedBomId(item.id);
    setAddItemModalOpen(true);
  };

  const handleDelete = async (itemId: number) => {
    if (!window.confirm('Are you sure you want to delete this item?')) return;
    
    try {
      await axiosInstance.delete(`/bom/api/items/${itemId}/`);
      showSnackbar('Item deleted successfully', 'success');
      fetchBOMs();
    } catch (error) {
      showSnackbar('Failed to delete item', 'error');
    }
  };

  const renderBOMStructure = (structure: BOMStructure[], level: number = 0) => {
    return structure.map((item) => (
      <React.Fragment key={item.id}>
        <TableRow>
          <TableCell>
            <Box sx={{ pl: level * 4, display: 'flex', alignItems: 'center', gap: 1 }} onClick={() => toggleItem(item.id)}>
              {item.children.length > 0 && (
                <IconButton
                  size="small"
                  sx={{
                    padding: '2px',
                    '&:hover': {
                      backgroundColor: 'rgba(0, 0, 0, 0.04)',
                    },
                  }}
                >
                  <ArrowForwardIosIcon 
                    sx={{ 
                      fontSize: '0.875rem',
                      color: 'text.secondary',
                      transform: expandedItems.has(item.id) ? 'rotate(90deg)' : 'rotate(0deg)',
                      transition: 'transform 0.2s',
                    }} 
                  />
                </IconButton>
              )}
              {item.component.name}
            </Box>
          </TableCell>
          <TableCell>{item.component.code}</TableCell>
          <TableCell>{item.quantity}</TableCell>
          <TableCell>{item.item_type}</TableCell>
          <TableCell>{item.notes || '-'}</TableCell>
          <TableCell>
            <ButtonGroup size="small" variant="outlined">
              <IconButton 
                size="small" 
                onClick={() => handleEditQuantity(item)}
                title="Edit Quantity"
              >
                <EditIcon fontSize="small" />
              </IconButton>
              <IconButton 
                size="small" 
                onClick={() => handleAddChild(item)}
                title="Add Child"
              >
                <AddChildIcon fontSize="small" />
              </IconButton>
              <IconButton 
                size="small" 
                onClick={() => handleDelete(item.id)}
                title="Delete"
                color="error"
              >
                <DeleteIcon fontSize="small" />
              </IconButton>
            </ButtonGroup>
          </TableCell>
        </TableRow>
        {item.children.length > 0 && expandedItems.has(item.id) && renderBOMStructure(item.children, level + 1)}
      </React.Fragment>
    ));
  };

  const handleAddItem = (bomId: number) => {
    setSelectedBomId(bomId);
    setAddItemModalOpen(true);
  };

  const handleAddItemSuccess = () => {
    // Refresh BOM data
    if (productCode) {
      fetchBOMs();
    }
  };

  return (
    <>
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress size={24} />
        </Box>
      ) : error ? (
        <Typography color="error" sx={{ p: 2 }}>{error}</Typography>
      ) : !data?.boms.length ? (
        <Typography sx={{ p: 2 }}>No BOMs found for this product.</Typography>
      ) : (
        data.boms.map((bom) => (
          <Box key={bom.id} sx={{ mb: 3 }}>
            <Box sx={{ 
              mb: 1, 
              p: 2, 
              backgroundColor: '#f8fafc',
              borderRadius: '4px',
              border: '1px solid #e2e8f0',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <Box>
                <Typography variant="subtitle1" sx={{ fontWeight: 500, mb: 1 }}>
                  {bom.name}
                </Typography>
                <Box sx={{ display: 'flex', gap: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Version: {bom.version}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Status: {bom.status}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Effective: {new Date(bom.effective_date).toLocaleDateString()}
                  </Typography>
                </Box>
              </Box>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => handleAddItem(bom.id)}
                sx={{
                  backgroundColor: 'rgb(0, 0, 0)',
                  color: 'rgb(255, 255, 255)',
                  '&:hover': {
                    backgroundColor: 'rgb(0, 0, 0)',
                  },
                }}
              >
                Add Item
              </Button>
            </Box>
            <TableContainer component={Paper} sx={{ boxShadow: 'none', border: '1px solid #e2e8f0' }}>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Component Name</TableCell>
                    <TableCell>Code</TableCell>
                    <TableCell>Quantity</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell>Notes</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {renderBOMStructure(bom.structure)}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        ))
      )}

      {selectedBomId && (
        <AddBomItemModal
          open={addItemModalOpen}
          onClose={() => {
            setAddItemModalOpen(false);
            setSelectedBomId(null);
          }}
          bomHeaderId={selectedBomId}
          onSuccess={handleAddItemSuccess}
        />
      )}

      {selectedItem && (
        <EditQuantityModal
          open={editQuantityModalOpen}
          onClose={() => {
            setEditQuantityModalOpen(false);
            setSelectedItem(null);
          }}
          currentQuantity={selectedItem.quantity}
          itemId={selectedItem.id}
          onSuccess={fetchBOMs}
        />
      )}
    </>
  );
};

export default ProductBOMsModal; 