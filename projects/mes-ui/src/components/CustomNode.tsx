import { memo } from "react";
import { <PERSON><PERSON>, Position, NodeProps } from "reactflow";
import "../styles/CustomNode.module.scss";

const CustomNode: React.FC<NodeProps> = ({ data }) => {
  return (
    <div className={`custom-node ${data.type}`}>
      {/* Incoming Handles */}
      <Handle type="target" position={Position.Top} id="top" />
      <Handle type="target" position={Position.Left} id="left" />

      {/* Node Content */}
      <div className="node-content">
        <strong>{data.label}</strong>
        <p className="node-type">{data.type}</p>
      </div>

      {/* Outgoing Handles */}
      <Handle type="source" position={Position.Right} id="right" />
      <Handle type="source" position={Position.Bottom} id="bottom" />
    </div>
  );
};

export default memo(CustomNode);
