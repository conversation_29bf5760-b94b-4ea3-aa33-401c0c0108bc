import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Box,
  Typography,
  IconButton,
  Divider,
} from '@mui/material';
import FilterListIcon from '@mui/icons-material/FilterList';
import CloseIcon from '@mui/icons-material/Close';

interface FilterState {
  [key: string]: any;
}

interface FilterMenuProps {
  filterState: FilterState;
  onApplyFilters: (filters: FilterState) => void;
  onResetFilters: () => void;
}

const GlobalFilters: React.FC<FilterMenuProps> = ({
  filterState,
  onApplyFilters,
  onResetFilters,
}) => {
  const [open, setOpen] = useState(false);
  const [tempFilters, setTempFilters] = useState<FilterState>(filterState);

  const hasActiveFilters = Object.values(filterState).some(
    (value) => value !== null && value !== undefined && value !== ''
  );

  const handleOpen = () => setO<PERSON>(true);
  const handleClose = () => {
    setOpen(false);
    setTempFilters(filterState);
  };

  const handleApply = () => {
    onApplyFilters(tempFilters);
    setOpen(false);
  };

  const handleReset = () => {
    onResetFilters();
    setTempFilters({});
    setOpen(false);
  };

  return (
    <>
      <Badge
        variant="dot"
        invisible={!hasActiveFilters}
        color="primary"
        sx={{
          marginLeft:'auto',
          '& .MuiBadge-badge': {
            right: 4,
            top: 4,
          },
        }}
      >
        <Button
          variant="outlined"
          onClick={handleOpen}
          startIcon={<FilterListIcon />}
        >
          Filters
        </Button>
      </Badge>

      <Drawer
        anchor="right"
        open={open}
        onClose={handleClose}
        PaperProps={{
          sx: {
            width: { xs: '100%', sm: 400 },
          },
        }}
      >
        <Box display="flex" justifyContent="space-between" alignItems="center" p={2}>
          <Typography variant="h6">Filters</Typography>
          <IconButton onClick={handleClose}>
            <CloseIcon />
          </IconButton>
        </Box>
        <Divider />
        <Box p={2} sx={{ flexGrow: 1, overflowY: 'auto' }}>
          {/* Add your filter form components here */}
          {/* Example: */}
          {/* <TextField
            label="Search"
            value={tempFilters.search || ''}
            onChange={(e) => setTempFilters({ ...tempFilters, search: e.target.value })}
            fullWidth
            margin="normal"
          /> */}
        </Box>
        <Divider />
        <Box p={2} display="flex" justifyContent="space-between">
          <Button onClick={handleReset} color="error">
            Reset
          </Button>
          <Box>
            <Button onClick={handleClose} sx={{ mr: 1 }}>
              Cancel
            </Button>
            <Button onClick={handleApply} variant="contained" color="primary">
              Apply
            </Button>
          </Box>
        </Box>
      </Drawer>
    </>
  );
};

export default GlobalFilters;
