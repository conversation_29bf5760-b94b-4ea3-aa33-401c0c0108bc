import React, { useMemo } from 'react';
import { Box, Typography,  } from '@mui/material';
import ReactECharts from 'echarts-for-react';
import Card from './Card';


type HeatmapProps = {
  title?: string;
  data: (string | number)[][];
  axisLabels?: { x?: string; y?: string };
  isGrouped?: boolean;
};

const HeatmapComponent: React.FC<HeatmapProps> = ({ title, data, axisLabels ,isGrouped}) => {
  const { xLabels, yLabels, values } = useMemo(() => {
    if (!data || data.length < 2) return { xLabels: [], yLabels: [], values: [] };

    const rows = data.slice(1); // first row is header

    const xSet = new Set<string>();
    const ySet = new Set<string>();

    rows.forEach((row) => {
      xSet.add(String(row[0]));
      ySet.add(String(row[1]));
    });

    const xArr = Array.from(xSet);
    const yArr = Array.from(ySet);

    const heatmapData = rows.map((row) => [
      xArr.indexOf(String(row[0])),
      yArr.indexOf(String(row[1])),
      Number(row[2]) || 0,
    ]);

    return {
      xLabels: xArr,
      yLabels: yArr,
      values: heatmapData,
    };
  }, [data]);

  const maxValue = values.length > 0 ? Math.max(...values.map((d) => d[2])) : 0;

  const option = {
    title: !isGrouped && {
        text: title,
        left: "center",
      },
    tooltip: {
      position: 'top',
      formatter: function (params: any) {
        return `${params.value[1]}<br/>${params.value[0]}: ${params.value[2]}`;
      },
    },
    grid: {
      height: '70%',
      top: '15%',
      left: '15%',
      right: '15%',
    },
    xAxis: {
      type: 'category',
      name: axisLabels?.x || '',
      nameLocation: 'middle',
      nameGap: 30,
      data: xLabels,
      splitArea: { show: true },
      axisLabel: { rotate: 45, interval: 0 },
    },
    yAxis: {
      type: 'category',
      name: axisLabels?.y || '',
      nameLocation: 'middle',
      nameGap: 40,
      data: yLabels,
      splitArea: { show: true },
    },
    visualMap: {
      min: 0,
      max: maxValue,
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: 0,
      inRange: {
        color: ['#e0f7fa', '#006064'], // beautiful gradient blue-teal
      },
      textStyle: { color: '#555' },
    },
    series: [
      {
        name: title || 'Heatmap',
        type: 'heatmap',
        data: values,
        label: { show: true, color: '#222' },
        emphasis: {
          itemStyle: { shadowBlur: 10, shadowColor: 'rgba(0,0,0,0.5)' },
        },
        animation: false,
      },
    ],
  };

  return (
      <Box width="100%" >
        <ReactECharts option={option} style={{ height: 300, width: '100%' }} />
      </Box>
  );
};

export default HeatmapComponent;
