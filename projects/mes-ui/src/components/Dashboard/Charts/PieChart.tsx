import React from 'react';
import ReactECharts from 'echarts-for-react';
import Card from './Card';

type PieChartProps = {
    title?: string;
    data: (string | number)[][];
    colorScheme?: string[];
};

const defaultGradients = [
    {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 1,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#a5d7ff' },
        { offset: 1, color: '#1e88e5' },
      ],
    },
    {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 1,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#9dffa1' },
        { offset: 1, color: '#43a047' },
      ],
    },
    {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 1,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#ffcb7c' },
        { offset: 1, color: '#fb8c00' },
      ],
    },
    {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 1,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#ec83ff' },
        { offset: 1, color: '#8e24aa' },
      ],
    },
    {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 1,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#ff8a88' },
        { offset: 1, color: '#e53935' },
      ],
    },
    {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 1,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#95f3ff' },
        { offset: 1, color: '#00acc1' },
      ],
    },
    {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 1,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#ffad8f' },
        { offset: 1, color: '#6d4c41' },
      ],
    },
    {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 1,
      y2: 1,
      colorStops: [
        { offset: 0, color: '#ffe48c' },
        { offset: 1, color: '#ffb300' },
      ],
    },
  ];
  

const PieChart: React.FC<PieChartProps> = ({ title, data }) => {
    const pieChartData = data.slice(1).map((item, index) => ({
        name: item[0] as string,
        value: item[1] as number,
        itemStyle: {
            color: defaultGradients[index % defaultGradients.length],
        },
    }));

    const option = {
        title: {
            text: title || '',
            left: 'center',
            top: 10,
            textStyle: {
                fontSize: 16,
                fontWeight: 600,
            },
        },
        tooltip: {
            trigger: 'item',
            formatter: '{b}: {c}',
        },
        series: [
            {
                name: data[0][1] as string,
                type: 'pie',
                radius: ['40%', '70%'],
                avoidLabelOverlap: false,
                label: {
                    show: true,
                    formatter: '{b}: {c}',
                    position: 'outside',
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: '16',
                        fontWeight: 'bold',
                    },
                },
                labelLine: {
                    show: true,
                },
                data: pieChartData,
            },
        ],
    };

    return (
        <Card>
            <ReactECharts option={option} style={{ height: 300, width: '100%' }} />
        </Card>
    );
};

export default PieChart;
