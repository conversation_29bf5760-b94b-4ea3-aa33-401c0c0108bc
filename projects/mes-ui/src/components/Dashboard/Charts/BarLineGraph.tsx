import React from "react";
import ReactECharts from "echarts-for-react";
import Card from "./Card";

const defaultGradients = [
  {
    type: "linear",
    x: 0,
    y: 0,
    x2: 1,
    y2: 1,
    colorStops: [
      { offset: 0, color: "#a5d7ff" },
      { offset: 1, color: "#1e88e5" },
    ],
  },
  {
    type: "linear",
    x: 0,
    y: 0,
    x2: 1,
    y2: 1,
    colorStops: [
      { offset: 0, color: "#9dffa1" },
      { offset: 1, color: "#43a047" },
    ],
  },
  {
    type: "linear",
    x: 0,
    y: 0,
    x2: 1,
    y2: 1,
    colorStops: [
      { offset: 0, color: "#ffcb7c" },
      { offset: 1, color: "#fb8c00" },
    ],
  },
  {
    type: "linear",
    x: 0,
    y: 0,
    x2: 1,
    y2: 1,
    colorStops: [
      { offset: 0, color: "#ec83ff" },
      { offset: 1, color: "#8e24aa" },
    ],
  },
  {
    type: "linear",
    x: 0,
    y: 0,
    x2: 1,
    y2: 1,
    colorStops: [
      { offset: 0, color: "#ff8a88" },
      { offset: 1, color: "#e53935" },
    ],
  },
  {
    type: "linear",
    x: 0,
    y: 0,
    x2: 1,
    y2: 1,
    colorStops: [
      { offset: 0, color: "#95f3ff" },
      { offset: 1, color: "#00acc1" },
    ],
  },
  {
    type: "linear",
    x: 0,
    y: 0,
    x2: 1,
    y2: 1,
    colorStops: [
      { offset: 0, color: "#ffad8f" },
      { offset: 1, color: "#6d4c41" },
    ],
  },
  {
    type: "linear",
    x: 0,
    y: 0,
    x2: 1,
    y2: 1,
    colorStops: [
      { offset: 0, color: "#ffe48c" },
      { offset: 1, color: "#ffb300" },
    ],
  },
];

type BarLineProps = {
  title: string;
  data: any[];
  xAxis?:string
  yAxis?:string
  barKeys:string[]
  lineKeys:string[]
  showToolbox?:boolean;
  showZoom?:boolean;
  isGrouped?:boolean;
};

const BarLineGraph = ({ title = "Bar Chart", data = [], xAxis , yAxis , showToolbox , showZoom , barKeys , lineKeys,isGrouped }: BarLineProps) => {
    const [headers] = data;
    const xKey = headers[0]; 
  const yKeys = headers.slice(1); 

  const barSeries = barKeys.map((yKey) => ({
    type: "bar",
    name: yKey,
    encode: { x: xKey, y: yKey },
    label: {
      show: true,
      position: "top",
      formatter: function (params) {
        return params.value[yKey];
      },
    },
    emphasis: {
      label: { show: true },
    },
    itemStyle: {
      opacity: 1,
    },
  }));

  const lineSeries = lineKeys.map((yKey) => ({
    type: "line",
    smooth: true,
    name: yKey,
    encode: { x: xKey, y: yKey },
    label: {
      show: true,
      position: "top",
      formatter: function (params) {
        return params.value[yKey];
      },
    },
    emphasis: {
      label: { show: true },
    },
    itemStyle: {
      opacity: 1,
    },
  }));
  
  const option = {
    title: !isGrouped && {
        text: title,
        left: "center",
      },
      toolbox: showToolbox && {
        feature: {
          dataView: { show: true, readOnly: false },
          magicType: { show: true, type: ['line', 'bar'] },
          restore: { show: true },
          saveAsImage: { show: true }
        }
      },
      tooltip: {
        trigger: "axis",
        axisPointer: { type: "shadow" },
      },
      legend: {
        top: 30,
      },
      dataset: {
        source: data,
      },
      dataZoom: showZoom && [
        {
          type: 'slider',
          show: true,
          xAxisIndex: [0],
          start: 0,
          end: 100
        }
      ],
      xAxis: {
        type: "category",
        name: xAxis,
      },
      yAxis: {
        type: "value",
        name: yAxis,
      },
      series: [...barSeries, ...lineSeries],
      color: defaultGradients,
  };

  return (
      <ReactECharts option={option} style={{ height: 300, width: "100%" }} />
  );
};

export default BarLineGraph;
