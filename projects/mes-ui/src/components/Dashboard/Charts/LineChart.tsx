import React from 'react';
import ReactECharts from 'echarts-for-react';
import Card from './Card';


type LineChartProps = {
    title?: string;
    data: any[][]; // 2D array as per ECharts dataset.source format
    showToolbox?: boolean;
    showZoom?:boolean;
    isGrouped?: boolean;
  };

const LineChart: React.FC<LineChartProps> = ({
  title,
  data,
  showToolbox ,
  showZoom,
  isGrouped,
}) => {
    const option = {
        title:!isGrouped && {
          text: title || '',
          left: 'center',
          top: 10,
          textStyle: { fontSize: 16, fontWeight: 600 },
        },
        tooltip: { trigger: 'axis' },
        toolbox: showToolbox && {
          feature: {
            dataView: { show: true, readOnly: false },
            magicType: { show: true, type: ['line', 'bar'] },
            restore: { show: true },
            saveAsImage: { show: true }
          }
        },
        legend: {
          top: 35,
          data: data.length > 0 ? data[0].slice(1) : [], // headers except first col (time)
        },
        color: [
          '#42a5f5',
          '#66bb6a',
          '#ffa726',
          '#ab47bc',
          '#ef5350',
          '#26c6da',
          '#8d6e63',
          '#ff7043',
        ],
        grid: {
          left: '3%',
          right: '4%',
          bottom: '10%',
          top: '20%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
        },
        yAxis: {
          type: 'value',
          splitLine: { lineStyle: { color: '#eee' } },
        },
        dataZoom: showZoom && [
          {
            type: 'slider',
            show: true,
            xAxisIndex: [0],
            start: 0,
            end: 100
          }
        ],
        dataset: {
          source: data,
        },
        series: data[0].slice(1).map((name: string, idx: number) => {
            // choose base colors for each series, cycle through some colors
            const colors = [
              'rgba(66, 165, 245, 1)',   // blue
              'rgba(102, 187, 106, 1)',  // green
              'rgba(255, 167, 38, 1)',   // orange
              'rgba(171, 71, 188, 1)',   // purple
              'rgba(239, 83, 80, 1)',    // red
              'rgba(38, 198, 218, 1)',   // cyan
              'rgba(141, 110, 99, 1)',   // brown
              'rgba(255, 112, 67, 1)',   // deep orange
            ];
          
            const baseColor = colors[idx % colors.length];
          
            return {
              type: 'line',
              smooth: true,
              name,
              encode: { x: data[0][0], y: name },
            
              lineStyle: name === 'Goal' ? { type: 'dashed', color: baseColor } : { color: baseColor },
              itemStyle: { color: baseColor },  // points color
            };
          }),
      };
  return (
      <ReactECharts option={option} style={{ height: 300, width: '100%' }} />
  );
};

export default LineChart;
