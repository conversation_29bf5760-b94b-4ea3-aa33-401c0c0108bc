import React from "react";
import { Box, Typography } from "@mui/material";
import { styled } from "@mui/material/styles";
import Card from "./Card";

type WidthVariant = 'normal' | 'wide' | 'extra-wide';

const getStrokeWidth = (variant: WidthVariant): number => {
  switch (variant) {
    case 'wide':
      return 18;
    case 'extra-wide':
      return 25;
    default:
      return 12;
  }
};

const CircularContainer = styled(Box)({  
  position: "relative",
  display: "inline-flex",
  alignItems: "center",
  justifyContent: "center",
});

const BackgroundCircle = styled("circle")<{ strokeWidth: number }>(({ strokeWidth }) => ({
  fill: "none",
  stroke: "#f0f0f0",
  strokeWidth,
}));

const ForegroundCircle = styled("circle")<{ strokeWidth: number }>(({ strokeWidth }) => ({
  fill: "none",
  strokeWidth,
  strokeLinecap: "round",
  transition: "stroke-dashoffset 0.3s ease",
}));

// Gradient selector based on value
const getGradientColors = (value: number) => {
  if (value <= 30 && value < 60) {
    return ["#f44336", "#f8bbd0"]; // crimson → pastel pink
  } else if (value <= 60 && value < 80) {
    return ["#ff9800", "#ffe0b2"]; // orange → peach
  } else if (value <= 80) {
    return ["#4caf50", "#c8e6c9"]; // emerald → pastel green
  } else {
    return ["#2196f3", "#bbdefb"]; // blue → soft blue
  }
};

type RadialProgressCardProps = {
  title: string;
  data: number; // 0-100
  size?: number;
  width?: WidthVariant;
};

const RadialProgressCard: React.FC<RadialProgressCardProps> = ({
  title,
  data,
  size = 180,
  width = 'normal',
}) => {
  const strokeWidth = getStrokeWidth(width);
  const radius = size / 2 - strokeWidth / 2;
  const circumference = 2 * Math.PI * radius;
  const progress = Math.min(Math.max(data, 0), 100);
  const offset = circumference - (progress / 100) * circumference;

  const [startColor, endColor] = getGradientColors(progress);
  const gradientId = `gradient-${title.replace(/\s+/g, "")}`;  

  return (
    <Card>
      <CircularContainer sx={{ width: size, height: size }}>
        <svg width={size} height={size} viewBox={`0 0 ${size} ${size}`}>
          <defs>
            <linearGradient id={gradientId} x1="1" y1="0" x2="0" y2="1">
              <stop offset="0%" stopColor={startColor} />
              <stop offset="100%" stopColor={endColor} />
            </linearGradient>
          </defs>

          <BackgroundCircle strokeWidth={strokeWidth} r={radius} cx={size / 2} cy={size / 2} />
          <ForegroundCircle
            strokeWidth={strokeWidth}
            r={radius}
            cx={size / 2}
            cy={size / 2}
            strokeDasharray={circumference}
            strokeDashoffset={offset}
            stroke={`url(#${gradientId})`}
            style={{ transform: "rotate(-90deg)", transformOrigin: "50% 50%" }}
          />
        </svg>

        <Box
          sx={{
            position: "absolute",
            top: 0,
            left: 0,
            width: size,
            height: size,
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            userSelect: "none",
            gap: 1,
          }}
        >
          <Typography
            variant="h4"
            sx={{
              fontWeight: 700,
              color: "#1c1c1e",
              fontFamily:
                "'SF Pro Text', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif",
              lineHeight: 1,
            }}
          >
            {progress.toFixed(0)}%
          </Typography>
          <Typography
            variant="subtitle1"
            sx={{
              fontWeight: 500,
              color: "#6e6e73",
              fontFamily:
                "'SF Pro Text', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif",
              letterSpacing: 0.5,
              fontSize: 16,
            }}
          >
            {title}
          </Typography>
        </Box>
      </CircularContainer>
    </Card>
  );
};

export default RadialProgressCard;
