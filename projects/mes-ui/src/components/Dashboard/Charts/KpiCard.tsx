import React from "react";
import { Box, Typography, IconButton } from "@mui/material";
import { styled } from "@mui/material/styles";
import { ArrowDownward, ArrowUpward } from "@mui/icons-material";

// Styled card container
const StatCard = styled(Box)<{ borderColor: string; bgColor: string }>(
  ({ theme, borderColor, bgColor }) => ({
    border: `1px solid ${borderColor}`,
    background: bgColor,
    borderRadius: "12px",
    padding: theme.spacing(2),
    flex: 1,
    // minWidth: 250,
    // maxWidth: 320,
    position: "relative",
  })
);

// Stat info group
const StatChange = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  marginTop: theme.spacing(1),
}));

type CardColor = "green" | "red" | "blue" | "orange" | "gray" | "purple";

type CardProps = {
  title: string;
  data: number;
  change: number;
  color: CardColor;
  icon?: React.ReactNode;
};

const getColors = (color: CardColor) => {
  switch (color) {
    case "green":
      return {
        borderColor: "#d1f5e5",
        bgColor: "linear-gradient(135deg, #f4fef9 0%, #e1f9ee 100%)",
        textColor: "#1a7f37",
      };
    case "red":
      return {
        borderColor: "#ffd1d1",
        bgColor: "linear-gradient(135deg, #fff5f5 0%, #ffeaea 100%)",
        textColor: "#d32f2f",
      };
    case "blue":
      return {
        borderColor: "#d0e3ff",
        bgColor: "linear-gradient(135deg, #f6f9ff 0%, #ebf3ff 100%)",
        textColor: "#1e40af",
      };
    case "orange":
      return {
        borderColor: "#ffe5b4",
        bgColor: "linear-gradient(135deg, #fff7e6 0%, #fff2cc 100%)",
        textColor: "#f57c00",
      };
    case "gray":
      return {
        borderColor: "#e0e0e0",
        bgColor: "linear-gradient(135deg, #f9f9f9 0%, #f0f0f0 100%)",
        textColor: "#616161",
      };
    case "purple":
      return {
        borderColor: "#e0d7ff",
        bgColor: "linear-gradient(135deg, #f7f4ff 0%, #ede6ff 100%)",
        textColor: "#6a1b9a",
      };
    default:
      return {
        borderColor: "#d0e3ff",
        bgColor: "linear-gradient(135deg, #f6f9ff 0%, #ebf3ff 100%)",
        textColor: "#1e40af",
      };
  }
};

const KPICard: React.FC<CardProps> = ({
  title,
  data,
  change,
  color,
  icon,
}) => {
  const { borderColor, bgColor, textColor } = getColors(color);
  const isPositive = change >= 0;

  return (
    <StatCard borderColor={borderColor} bgColor={bgColor}>
      <Box display="flex" justifyContent="space-between" alignItems="start">
        <Typography variant="subtitle2" color="textSecondary">
          {title}
        </Typography>
        {icon && (
          <IconButton size="small" sx={{ color: textColor }}>
            {icon}
          </IconButton>
        )}
      </Box>

      <Typography variant="h4" sx={{ fontWeight: 700 }}>
        {data?.toLocaleString()}
      </Typography>

      <StatChange>
        {isPositive ? (
          <ArrowUpward fontSize="small" sx={{ color: textColor }} />
        ) : (
          <ArrowDownward fontSize="small" sx={{ color: textColor }} />
        )}

        <Typography
          variant="body2"
          sx={{ color: textColor, ml: 0.5, fontWeight: 500 }}
        >
          {isPositive ? "+" : "-"}
          {Math.abs(change)}%
        </Typography>

        <Typography variant="body2" color="textSecondary" sx={{ ml: 0.5 }}>
          vs. previous
        </Typography>
      </StatChange>
    </StatCard>
  );
};

export default KPICard;
