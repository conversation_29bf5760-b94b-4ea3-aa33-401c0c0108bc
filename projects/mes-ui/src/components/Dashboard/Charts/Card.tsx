import { styled } from '@mui/material/styles';
import { Box } from '@mui/material';

const Card = styled(Box)<{grouped?:boolean}>(({ theme,grouped }) => ({
    border: '1px solid #e3e3e3',
    borderRadius: 15,
    position:'relative',
    padding: grouped?theme.spacing(2.5): '20px',
  //   width: 180,
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    
    boxShadow: '0 2px 5px rgba(0,0,0,0.05)',
    backgroundColor: '#fff',
  }));

  export default Card;