import React, { useEffect, useMemo, useState } from "react";
import styles from "../../styles/Dashboard.module.scss"; // Adjust the path as needed
import ReactECharts from "echarts-for-react";
import { useChartData } from "../../hooks/useChartData";
import {
  IconButton,
  Button,
  Menu,
  MenuItem,
  TextField,
  Checkbox,
  FormControlLabel,
  Box,
  Typography,
  ToggleButtonGroup,
  ToggleButton,
  InputAdornment,
  Tooltip,
  CircularProgress,
  styled,
  ButtonProps,
  Badge,
  Select,
} from "@mui/material";
import {
  CalendarToday,
  CheckBox,
  Clear,
  FilterList,
  FilterAltOutlined,
  Info,
  InfoOutlined,
  Percent,
  Tag,
  Title,
  Close,
  ToggleOffOutlined,
} from "@mui/icons-material";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { format } from "date-fns";

interface ModeToggleButtonProps extends ButtonProps {
  isActive: boolean;
}

const ModeToggleButton = styled(Button, {
  shouldForwardProp: (prop) => prop !== "isActive",
})<ModeToggleButtonProps>(({ theme, isActive }: any) => ({
  borderRadius: "16px",
  textTransform: "none",
  fontWeight: 500,
  fontSize: "10px",
  padding: "2px 5px", // Adjust padding as neede
  minWidth: "auto",
  borderColor: isActive ? theme.palette.primary.main : theme.palette.grey[400],
  backgroundColor: isActive
    ? theme.palette.primary[50]
    : theme.palette.grey[100],
  color: isActive ? theme.palette.primary.main : theme.palette.grey[600],
  "&:hover": {
    backgroundColor: isActive
      ? theme.palette.primary[100]
      : theme.palette.grey[200],
    borderColor: isActive
      ? theme.palette.primary.main
      : theme.palette.grey[500],
  },
}));
ModeToggleButton.defaultProps = {
  variant: "outlined",
};

const MenuItemIcon = ({ type }: { type: string }) => {
  if (type === "date") return <CalendarToday color="primary" />;
  if (type === "int") return <Tag color="primary" />;
  if (type === "str") return <Title color="primary" />;
  if (type === "bool") return <ToggleOffOutlined color="primary" />;
  return <></>;
};

const FilterMenu = ({
  allowedFilters,
  appliedFilters,
  applyFilter,
}: {
  allowedFilters: Record<string, any>;
  appliedFilters: Record<string, any>; // Add this pro
  applyFilter: (filters: Record<string, any>) => void;
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [filters, setFilters] = useState<Record<string, any>>({});
  const [inputMode, setInputMode] = useState<
    Record<string, "exact" | "range" | "pattern">
  >({});
  const [patternType, setPatternType] = useState<
    Record<string, "suffix" | "prefix" | "like">
  >({});
  useEffect(() => {
    const morphedFilters = Object.entries(appliedFilters).reduce(
      (acc: any, [key, val]) => {
        acc[key] = { value: val };
        return acc;
      },
      {}
    );
    setFilters(morphedFilters);
  }, [appliedFilters]);
  const open = Boolean(anchorEl);

  const handleMainClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleCloseAll = () => {
    setAnchorEl(null);
  };
  const handleChange = (field: string, key: string, value: any) => {
    setFilters((prev) => {
      let newValue = value;
      // Only apply patternType logic for string fields and 'value' key
      if (key === "value" && typeof value === "string" && patternType[field]) {
        if (patternType[field] === "prefix") {
          newValue = value + "%";
        } else if (patternType[field] === "suffix") {
          newValue = "%" + value;
        } else if (patternType[field] === "like") {
          newValue = "%" + value + "%";
        }
      }
      return {
        ...prev,
        [field]: {
          ...prev[field],
          [key]: newValue,
          // Optionally, store the raw input for display if needed
          _raw: value,
        },
      };
    });
  };

  const countAppliedFilters = () => {
    return Object.keys(appliedFilters).filter((key) => {
      const value = filters[key]?.value;
      if (value === undefined || value === "") return false;
      if (Array.isArray(value)) return value.some((val) => val !== "");
      return true;
    }).length;
  };

  const handleClearField = (field: string) => {
    setFilters((prev) => ({
      ...prev,
      [field]: {
        ...prev[field],
        value: "",
      },
    }));
  };

  const renderInput = (field: string, type: string) => {
    const filterValue = filters[field] || {};
    const mode = inputMode[field] || "exact";
    switch (type) {
      case "int": {
        const inputType = "number";

        return (
          <>
            <Box display="flex" alignItems="center" mb={1} gap={1} sx={{margin:0}}>
              <Box
                sx={{ display: "flex", alignItems: "center", gap: "10px",padding:0 }}
                key={field}
              >
                <MenuItemIcon type={type} />
                <Typography variant="body2">{field}</Typography>
              </Box>
              <ModeToggleButton
                isActive={inputMode[field] === "range"}
                onClick={() => {
                  setInputMode((prev) => ({
                    ...prev,
                    [field]: mode === "range" ? "exact" : "range",
                  }));
                }}
              >
                Range
              </ModeToggleButton>
              <IconButton
                sx={{ marginLeft: "auto" }}
                onClick={() => handleClearField(field)}
                color={
                  filterValue?.value &&
                  (!!filterValue?.value[0] || !!filterValue?.value[1])
                    ? "error"
                    : "inherit"
                }
              >
                <Close></Close>
              </IconButton>
            </Box>
            {mode === "exact" ? (
              <TextField
                label="Exact Match"
                type={inputType}
                fullWidth
                size="small"
                margin="dense"
                value={filterValue.value?.[0] || ""}
                onChange={(e) => handleChange(field, "value", [e.target.value])}
              />
            ) : (
              <Box display="flex" gap={1}>
                <TextField
                  label="Range Start"
                  type={inputType}
                  fullWidth
                  size="small"
                  margin="dense"
                  value={filterValue.value?.[0] || ""}
                  onChange={(e) =>
                    handleChange(field, "value", [
                      e.target.value,
                      filterValue.value?.[1] || "",
                    ])
                  }
                />
                <TextField
                  label="Range End"
                  type={inputType}
                  fullWidth
                  size="small"
                  margin="dense"
                  value={filterValue.value?.[1] || ""}
                  onChange={(e) =>
                    handleChange(field, "value", [
                      filterValue.value?.[0] || "",
                      e.target.value,
                    ])
                  }
                />
              </Box>
            )}
          </>
        );
      }

      case "date": {
        return (
          <>
                  <Box display="flex" alignItems="center" mb={1} gap={1} sx={{margin:0}}>
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        gap: "10px",
                      }}
                      key={field}
                    >
                      <MenuItemIcon type={type} />
                      <Typography variant="body2">{field}</Typography>
                    </Box>
                    <ModeToggleButton
                      isActive={inputMode[field] === "range"}
                      onClick={() => {
                        setInputMode((prev) => ({
                          ...prev,
                          [field]: mode === "range" ? "exact" : "range",
                        }));
                      }}
                    >
                      Range
                    </ModeToggleButton>
                    <IconButton
                      sx={{ marginLeft: "auto" }}
                      onClick={() => handleClearField(field)}
                      color={
                        filterValue?.value &&
                        (!!filterValue?.value[0] || !!filterValue?.value[1])
                          ? "error"
                          : "inherit"
                      }
                    >
                      <Close></Close>
                    </IconButton>
                  </Box>


            {mode === "exact" ? (
              <DatePicker
                label="Exact Match"
                value={filterValue.value ? new Date(filterValue.value) : null}
                onChange={(newValue) =>
                  handleChange(field, "value", [
                    format(newValue as Date, "yyyy-MM-dd") || "",
                  ])
                }
                slotProps={{ textField: { fullWidth: true, margin: "dense",size:'small' } }}
              />
            ) : (
              <Box display="flex" gap={1}>
                <DatePicker
                  label="Start Date"
                  value={
                    filterValue.value?.[0]
                      ? new Date(filterValue.value[0])
                      : null
                  }
                  onChange={(newValue) =>
                    handleChange(field, "value", [
                      format(newValue as Date, "yyyy-MM-dd") || "",
                      filterValue.value?.[1] || "",
                    ])
                  }
                  slotProps={{
                    textField: { fullWidth: true, margin: "dense", size:'small'  },
                  }}
                />
                <DatePicker
                  label="End Date"
                  value={
                    filterValue.value?.[1]
                      ? new Date(filterValue.value[1])
                      : null
                  }
                  onChange={(newValue) =>
                    handleChange(field, "value", [
                      filterValue.value?.[0] || "",
                      format(newValue as Date, "yyyy-MM-dd") || "",
                    ])
                  }
                  slotProps={{
                    textField: { fullWidth: true, margin: "dense",size:'small'  },
                  }}
                />
              </Box>
            )}
          </>
        );
      }

      case "str": {
        return (
          <>    
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        gap: "10px",
                        padding:0
                      }}
                      key={field}
                    >
                      <MenuItemIcon type={type} />
                      <Typography variant="body2">{field}</Typography>
                    <ModeToggleButton
                      isActive={inputMode[field] === "pattern"}
                      onClick={() => {
                        setInputMode((prev) => ({
                          ...prev,
                          [field]: mode === "pattern" ? "exact" : "pattern",
                        }));
                      }}
                    >
                      Pattern
                    </ModeToggleButton>
                    <IconButton
                      sx={{ marginLeft: "auto" }}
                      onClick={() => handleClearField(field)}
                      color={
                        filterValue?.value &&
                        (!!filterValue?.value[0] || !!filterValue?.value[1])
                          ? "error"
                          : "inherit"
                      }
                    >
                      <Close></Close>
                    </IconButton>
                    </Box>
                
                

            {mode === "exact" ? (
              <TextField
                label="Exact Match"
                fullWidth
                margin="dense"
                size="small"
                value={filterValue.value || ""}
                onChange={(e) => handleChange(field, "value", e.target.value)}
              />
            ) : (
              <TextField
                label="Pattern Match"
                fullWidth
                size="small"
                margin="dense"
                value={filterValue._raw || filterValue.value || ""}
                onChange={(e) => handleChange(field, "value", e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Select
                        size="small"
                        value={patternType[field] || "prefix"}
                        onChange={(e) =>
                          setPatternType((prev:any
                          ) => ({
                            ...prev,
                            [field]: e.target.value,
                          }))
                        }
                        displayEmpty
                        variant="outlined" // or "outlined" based on your input style
                        sx={{
                          fontSize: '0.75rem',
                          minWidth: 100,
                          backgroundColor: '#f5f5f5',
                          borderRadius: 1,
                          height: '32px',
                          '.MuiOutlinedInput-notchedOutline': {
                            border: 'none',
                          },
                        }}
                        MenuProps={{
                          PaperProps: {
                            sx: {
                              fontSize: '0.75rem',
                            },
                          },
                        }}
                      >
                        <MenuItem value="prefix" sx={{fontSize:'0.75rem'}}>STARTS WITH</MenuItem>
                        <MenuItem value="suffix" sx={{fontSize:'0.75rem'}}>ENDS WITH</MenuItem>
                        <MenuItem value="like" sx={{fontSize:'0.75rem'}}>CONTAINS</MenuItem>
                      </Select>
                    </InputAdornment>
                  )
                }}
              />
            )}
          </>
        );
      }

      case "bool":
        return (
              <Box sx={{ display: "flex",padding:0 }}>
                <Box
                  sx={{ display: "flex", alignItems: "center", gap: "10px" }}
                  key={field}
                >
                  <MenuItemIcon type={type} />
                  <Typography variant="body2">{field}</Typography>
                </Box>
                <Checkbox
                  checked={filterValue.value === true}
                  onChange={(e) =>
                    handleChange(field, "value", e.target.checked)
                  }
                />
                <IconButton
                sx={{ marginLeft: "auto" }}
                onClick={() => handleClearField(field)}
                color={
                  filterValue?.value &&
                  (!!filterValue?.value)
                    ? "error"
                    : "inherit"
                }
              >
                <Close></Close>
              </IconButton>
              </Box>
            
        );
      default:
        return null;
    }
  };

  const appliedFiltersCount = useMemo(() => {
    return Object.keys(appliedFilters).filter((key) => {
      const value = appliedFilters[key];
      return value !== undefined && value !== "";
    }).length;
  }, [appliedFilters]);

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <IconButton onClick={handleMainClick}>
        {appliedFiltersCount > 0 ? (
          <Badge variant="dot" color="primary">
            <FilterList />
          </Badge>
        ) : (
          <FilterList />
        )}
      </IconButton>

      {/* First-level menu: list of filterable fields */}
      <Menu
        PaperProps={{
          sx: {
            borderRadius: "10px",
            boxShadow: "0px 0px 10px rgba(0, 0, 0, 0.1)",
            width: "350px",
            height: "auto",
            maxHeight: "400px",
            overflowY: "auto",
            "& .MuiMenu-paper": {
              borderRadius: "10px",
              boxShadow: "0px 0px 10px rgba(99, 99, 99, 0.1)",
            },
          },
        }}
        anchorEl={anchorEl}
        open={open}
        onClose={handleCloseAll}
      >
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            gap: "10px",
            borderBottom: "0.5px solid lightgray",
            position: "sticky",
            top: 0,
            background: "white",
            zIndex: 100,
            padding: "10px",
          }}
        >
          <Box>
            <FilterAltOutlined sx={{ marginBottom: "4px" }} color="primary" />
          </Box>
          <Typography variant="h6" gutterBottom>
            Filter Options
          </Typography>

          <Button
            sx={{ marginLeft: "auto" }}
            size="small"
            color="inherit"
            onClick={() => {
              setFilters({});
              setInputMode({});
              setPatternType({});
            }}
          >
            Clear All
          </Button>
        </Box>
        {Object.entries(allowedFilters).map(([field, type]) => (
          <Box sx={{ paddingRight: "15px",paddingLeft:'15px',paddingBottom:'5px' }}>
            <Box>{renderInput(field, type)}</Box>
          </Box>
        ))}

        <Button
          fullWidth
          size="large"
          onClick={() => {
            applyFilter(filters);
            handleCloseAll();
          }}
        >
          Apply
        </Button>
        <Box
          sx={{
            position: "sticky",
            bottom: 0,
            padding: "10px",
            backgroundColor: "rgb(222, 222, 222)",
            zIndex: 100,
          }}
          textAlign={"center"}
        >
          <Typography variant="body2">
            {countAppliedFilters()} of {Object.keys(allowedFilters).length}{" "}
            filters active
          </Typography>
        </Box>
      </Menu>
    </LocalizationProvider>
  );
};

export default FilterMenu;
