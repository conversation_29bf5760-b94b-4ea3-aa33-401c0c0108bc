import React, { useState } from 'react';
import {
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  IconButton,
  Tooltip,
  Box,
  Divider,
} from '@mui/material';
import {
  Home,
  Settings,
  Info,
  ChevronLeft,
  ChevronRight,
} from '@mui/icons-material';

const drawerWidth = 240;
const collapsedWidth = 60;

const menuItems = [
  { label: 'Home', icon: <Home /> },
  { label: 'Settings', icon: <Settings /> },
  { label: 'About', icon: <Info /> },
];

export default function CollapsibleSidebar() {
  const [open, setOpen] = useState(true);

  return (
    <Drawer
    //   variant="permanent"
      sx={{
        width: open ? drawerWidth : collapsedWidth,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: open ? drawerWidth : collapsedWidth,
          boxSizing: 'border-box',
          overflowX: 'hidden',
          transition: 'width 0.3s',
        },
      }}
    >
      <Box display="flex" justifyContent={open ? 'flex-end' : 'center'} p={1}>
        <IconButton onClick={() => setOpen(!open)}>
          {open ? <ChevronLeft /> : <ChevronRight />}
        </IconButton>
      </Box>
      <Divider />
      <List>
        {menuItems.map((item, index) => (
          <Tooltip
            key={item.label}
            title={open ? '' : item.label}
            placement="right"
            arrow
          >
            <ListItem component="div">
              <ListItemIcon>{item.icon}</ListItemIcon>
              {open && <ListItemText primary={item.label} />}
            </ListItem>
          </Tooltip>
        ))}
      </List>
    </Drawer>
  );
}
