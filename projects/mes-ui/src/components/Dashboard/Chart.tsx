import React, { useMemo, useState } from "react";
import KPICard from "./Charts/KpiCard";
import LineChart from "./Charts/LineChart";
import BarGraph from "./Charts/BarGraph";
import BarLineGraph from "./Charts/BarLineGraph";
import RadialProgress from "./Charts/RadialProgress";
import Heatmap from "./Charts/Heatmap";
import { useChartData, useChartGroupData } from "../../hooks/useChartData";
import Pie<PERSON><PERSON> from "./Charts/PieChart";
import Card from "./Charts/Card";
import { Box, Select, MenuItem, Typography, FormControl, CircularProgress } from "@mui/material";
import FilterMenu from "./FilterMenu";


interface propType {
  id: number;
}

const chartMap = {
  kpi: KPICard,
  pie: PieChart,
  line: LineChart,
  bar: BarGraph,
  "bar-line": BarLineGraph,
  "radial-progress": RadialProgress,
  heatmap: Heatmap,
};

const Chart = ({ id }: propType) => {
  const [chartId, setChartId] = useState(id);
  const [filters, setFilters] = useState({});
  const {
    data: chartData,
    isLoading: isLoadingChart,
    error: errorChart,
  } = useChartData(chartId, filters);

  const { data: chartGroupData } = useChartGroupData(
    chartData?.chart_instance?.chart_group
  );

  const ChartComponent = useMemo(() => {
    return (
      chartMap[
        chartData?.chart_instance?.config?.type as keyof typeof chartMap
      ] || null
    );
  }, [chartData?.chart_instance?.config?.type, chartId]);

  if (!ChartComponent) return null;

  if (chartData?.chart_instance?.config?.type === "kpi")
    return (
      <KPICard
        title={chartData?.chart_instance?.config?.title}
        data={chartData?.chart_instance?.config?.data}
        {...chartData?.chart_instance?.config}
      />
    );

  if (isLoadingChart)
    return (
      <Card grouped>
       <Box sx={{height:300,display:'flex',justifyContent:'center',alignItems:'center'}}>
          <CircularProgress />
       </Box>
      </Card>
    );

  return (
    <Card grouped={!!chartGroupData}>
      {chartGroupData ? (
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            paddingBottom: "10px",
            borderBottom: "1px solid #e0e0e0",
            width: "100%",
          }}
        >
          <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
            {chartGroupData?.name}
          </Typography>
          <FormControl size="small" sx={{ minWidth: 200 }}>
            <Select
              value={chartId}
              onChange={(e) => {
                setChartId(e.target.value as number);
              }}
              variant="outlined"
              // sx={{
              //     '& .MuiSelect-select': {
              //         padding: '8px 14px',
              //     }
              // }}
            >
              {chartGroupData?.charts?.map((chart: any) => (
                <MenuItem key={chart.id} value={chart.id}>
                  {chart.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
      {    Object.keys(chartData?.chart_instance?.filter_config).length > 0 && (
            <FilterMenu
              allowedFilters={chartData?.chart_instance?.filter_config}
              appliedFilters={filters}
              applyFilter={(f) =>
                setFilters(
                  Object.keys(f).reduce(
                    (acc, key) => ({ ...acc, [key]: f[key].value }),
                    {}
                  )
                )
              }
            />
        )}
        </Box>
      ) : (
        Object.keys(chartData?.chart_instance?.filter_config).length > 0 && (
          <div style={{position:'absolute',top:10,right:10,zIndex:100}}>
            <FilterMenu
              allowedFilters={chartData?.chart_instance?.filter_config}
              appliedFilters={filters}
              applyFilter={(f) =>
                setFilters(
                  Object.keys(f).reduce(
                    (acc, key) =>
                      f[key]?.value 
                        ? { ...acc, [key]: f[key].value }
                        : acc,
                    {}
                  )
                )
              }
            />
          </div>
        )
      )}
      <ChartComponent
        title={chartData?.chart_instance?.config?.title}
        data={chartData?.chart_data?.dataset?.source || chartData?.chart_instance?.config?.dataset}
        isGrouped={!!chartData?.chart_instance?.chart_group}
        {...chartData?.chart_instance?.config}
      />
    </Card>
  );
};

export default Chart;
