// components/ConfirmationModal.tsx
import React from "react";
import { Box, Button, Modal, Typography } from "@mui/material";

interface ConfirmationModalProps {
  open: boolean;
  title: string;
  onCancel: () => void;
  onClose: () => void;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  open,
  title,
  onCancel,
  onClose,
}) => {
  return (
    <Modal
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick" && reason !== "escapeKeyDown") {
          onClose();
        }
      }}
    >
      <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          width: 400,
          bgcolor: "background.paper",
          borderRadius: "10px",
          boxShadow: 24,
          p: 4,
        }}
      >
        <Typography variant="h6" gutterBottom>
          {title}
        </Typography>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-around",
            mt: 3,
          }}
        >
          <Button variant="contained" color="success" onClick={onClose}>
            Yes
          </Button>
          <Button variant="outlined" color="error" onClick={onCancel}>
            No
          </Button>
        </Box>
      </Box>
    </Modal>
  );
};

export default ConfirmationModal;
