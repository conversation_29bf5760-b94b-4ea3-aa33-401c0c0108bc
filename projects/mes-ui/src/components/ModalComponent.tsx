import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogActions,
  Button,
  CircularProgress,
} from '@mui/material';

interface ModalComponentProps {
  open: boolean;
  onClose: () => void;
  onSubmit: () => void;
  title: string;
  loading?: boolean;
  children: React.ReactNode;
}

const ModalComponent: React.FC<ModalComponentProps> = ({
  open,
  onClose,
  onSubmit,
  title,
  loading = false,
  children,
}) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          padding: 2,
        },
      }}
    >
      {/* <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        {title}
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle> */}
      <DialogContent sx={{ mt: 2 }}>
        {children}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}  sx={{ color: "#000000" }} disabled={loading}>
          Cancel
        </Button>
        <Button
          onClick={onSubmit}
          sx={{ color: "#000000" }}
          disabled={loading}
          startIcon={loading ? <CircularProgress size={20} /> : null}
        >
          Submit
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ModalComponent;
