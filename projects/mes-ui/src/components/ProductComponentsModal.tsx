import React from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
  Box,
  CircularProgress,
  IconButton,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { useProductById } from '../hooks/useProductTable';

// Define the props interface
export interface ProductComponentsModalProps {
  open: boolean;
  onClose: () => void;
  productId?: number | null;
}

// Define the component interface based on API response structure
interface ComponentItem {
  component: {
    id: number;
    code: string;
    name: string;
    description: string;
  };
}

const ProductComponentsModal: React.FC<ProductComponentsModalProps> = ({
  open,
  onClose,
  productId,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Fetch product data to get components
  const {
    data: product,
    isLoading,
    error
  } = useProductById(productId);

  // Extract components from product data
  const components: ComponentItem[] = product?.components || [];

  // Error state
  if (error) {
    return (
      <Dialog
        open={open}
        onClose={onClose}
        maxWidth="md"
        fullWidth
        fullScreen={isMobile}
      >
        <DialogTitle>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">Product Components</Typography>
            <IconButton onClick={onClose} size="small">
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
            <Typography color="error">
              Error loading product components. Please try again.
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose} variant="outlined">
            Close
          </Button>
        </DialogActions>
      </Dialog>
    );
  }

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      fullScreen={isMobile}
      slotProps={{
        paper: {
          sx: {
            borderRadius: isMobile ? 0 : '12px',
            maxHeight: '90vh',
          },
        },
      }}
    >
      <DialogTitle
        sx={{
          borderBottom: '1px solid #e2e8f0',
          padding: '20px 24px',
        }}
      >
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Box>
            <Typography
              variant="h6"
              sx={{
                fontWeight: 600,
                color: '#1e293b',
                fontSize: '18px',
                mb: 0.5,
              }}
            >
              Product Components
            </Typography>
            {product && (
              <Typography
                variant="body2"
                sx={{
                  color: '#64748b',
                  fontSize: '14px',
                }}
              >
                Product: {product.name} ({product.code})
              </Typography>
            )}
          </Box>
          <IconButton
            onClick={onClose}
            size="small"
            sx={{
              color: '#64748b',
              '&:hover': {
                backgroundColor: '#f1f5f9',
              },
            }}
          >
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ padding: 0 }}>
        {isLoading ? (
          <Box
            display="flex"
            justifyContent="center"
            alignItems="center"
            minHeight="300px"
          >
            <CircularProgress size={40} />
          </Box>
        ) : components && components.length > 0 ? (
          <TableContainer component={Paper} elevation={0}>
            <Table>
              <TableHead>
                <TableRow sx={{ backgroundColor: '#f8fafc' }}>
                  <TableCell
                    sx={{
                      fontWeight: 600,
                      color: '#475569',
                      borderBottom: '1px solid #e2e8f0',
                      width: '15%',
                    }}
                  >
                    Component ID
                  </TableCell>
                  <TableCell
                    sx={{
                      fontWeight: 600,
                      color: '#475569',
                      borderBottom: '1px solid #e2e8f0',
                      width: '25%',
                    }}
                  >
                    Component Code
                  </TableCell>
                  <TableCell
                    sx={{
                      fontWeight: 600,
                      color: '#475569',
                      borderBottom: '1px solid #e2e8f0',
                      width: '30%',
                    }}
                  >
                    Component Name
                  </TableCell>
                  <TableCell
                    sx={{
                      fontWeight: 600,
                      color: '#475569',
                      borderBottom: '1px solid #e2e8f0',
                      width: '30%',
                    }}
                  >
                    Component Description
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {components.map((item) => (
                  <TableRow
                    key={item.component.id}
                    sx={{
                      '&:hover': {
                        backgroundColor: '#f8fafc',
                      },
                      '&:last-child td': {
                        borderBottom: 0,
                      },
                    }}
                  >
                    <TableCell
                      sx={{
                        color: '#64748b',
                        borderBottom: '1px solid #f1f5f9',
                        padding: '16px',
                      }}
                    >
                      <Typography variant="body2" sx={{ fontSize: '14px', fontWeight: 500 }}>
                        {item.component.id}
                      </Typography>
                    </TableCell>
                    <TableCell
                      sx={{
                        color: '#334155',
                        borderBottom: '1px solid #f1f5f9',
                        padding: '16px',
                      }}
                    >
                      <Typography variant="body2" sx={{ fontSize: '14px', fontWeight: 500 }}>
                        {item.component.code}
                      </Typography>
                    </TableCell>
                    <TableCell
                      sx={{
                        color: '#334155',
                        borderBottom: '1px solid #f1f5f9',
                        padding: '16px',
                      }}
                    >
                      <Typography variant="body2" sx={{ fontSize: '14px', fontWeight: 500 }}>
                        {item.component.name}
                      </Typography>
                    </TableCell>
                    <TableCell
                      sx={{
                        color: '#64748b',
                        borderBottom: '1px solid #f1f5f9',
                        padding: '16px',
                      }}
                    >
                      <Typography variant="body2" sx={{ fontSize: '14px' }}>
                        {item.component.description || '-'}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        ) : (
          <Box
            display="flex"
            justifyContent="center"
            alignItems="center"
            minHeight="200px"
          >
            <Typography color="textSecondary">
              No components found for this product
            </Typography>
          </Box>
        )}
      </DialogContent>

      <DialogActions
        sx={{
          borderTop: '1px solid #e2e8f0',
          padding: '16px 24px',
          justifyContent: 'flex-end',
        }}
      >
        <Button
          onClick={onClose}
          variant="outlined"
          sx={{
            textTransform: 'none',
            fontWeight: 500,
            borderColor: '#d1d5db',
            color: '#374151',
            '&:hover': {
              borderColor: '#9ca3af',
              backgroundColor: '#f9fafb',
            },
          }}
        >
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ProductComponentsModal;
