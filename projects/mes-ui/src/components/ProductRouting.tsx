import React from "react";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import ArrowDownwardIcon from "@mui/icons-material/ArrowDownward";
import styles from "../styles/ProductRouting.module.scss";
import useScreen from "../hooks/useScreenSize";

interface ExecutionStep {
  executed: string | null;
  should_execute: string | null;
  time: string;
  validity: Record<string, boolean>;
}

interface ConfigProps {
  route_sequence: ExecutionStep[];
  schema: {
    routing_schema: {
      components: Record<
        string,
        {
          name: string;
        }
      >;
    };
  };
  components: Record<string, { name: string }>; // Component mapping
}

interface ProductRouteProps {
  config?: ConfigProps;
  isLoading: boolean;
  error: unknown;
}

const ProductRoute: React.FC<ProductRouteProps> = ({
  config,
  isLoading,
  error,
}) => {
  const { device } = useScreen();
  if (isLoading) return <div>Loading...</div>;
  if (error || !config?.route_sequence?.length) return null;

  const { route_sequence, schema } = config;
  const comp = schema?.routing_schema?.components ?? {};
  const stepsPerRow = device === "mobile" ? 2 : device === "tablet" ? 3 : 4;
  const totalSteps = route_sequence.length;

  // ✅ Group steps into rows
  const groupedSteps: ExecutionStep[][] = [];
  for (let i = 0; i < totalSteps; i += stepsPerRow) {
    groupedSteps.push(route_sequence.slice(i, i + stepsPerRow));
  }

  groupedSteps.forEach((row, index) => {
    if (index % 2 === 1) {
      row.reverse();
    }
  });

  const next_executable = route_sequence.findIndex(
    (step) => !step.executed && step.should_execute
  );

  const last_node = route_sequence[route_sequence.length - 1];
  return (
    <div className={styles.routeWrapper}>
      <div className={styles.routeContainer}>
        {groupedSteps.map((row, rowIndex) => {
          return (
            <div
              key={rowIndex}
              className={styles.rowContainer}
              style={{
                justifyContent:
                  rowIndex % 2 === 1 && row.length < stepsPerRow
                    ? "flex-end"
                    : "flex-start",
              }}
            >
              {row.map((step, pos) => {
                const globalIndex = rowIndex * stepsPerRow + pos;
                const hasNextExecutable = next_executable !== -1;
                const success =
                  step.executed &&
                  step.executed === step.should_execute &&
                  step.should_execute !== null;
                const isLastInRow = pos === row.length - 1;
                const isFirstInRow = pos === 0;
                const isOddRow = rowIndex % 2 === 1;
                const label = step.should_execute
                  ? comp[step.should_execute]?.name
                  : step.executed
                    ? comp[step.executed]?.name
                    : "";
                const isLastNode = step.executed
                  ? step.executed === last_node.executed
                  : step.should_execute === last_node.should_execute;
                const actualIndex = isOddRow
                  ? rowIndex * stepsPerRow + (row.length - 1 - pos) // Adjust for reversed order
                  : rowIndex * stepsPerRow + pos;

                const isNextExecutable =
                  hasNextExecutable && actualIndex === next_executable;

                const isPending = step.executed === null;

                let isValid = true;
                if (step.validity?.is_valid !== undefined) {
                  isValid = step.validity.is_valid
                }

                return (
                  <div key={globalIndex} className={styles.routeStep}>
                    {/* Step Box */}
                    <div
                      className={`${styles.stepBox} ${isNextExecutable
                        ? styles.next_executable
                        : !isValid ? styles.failed : success ?
                          styles.success
                          : isPending
                            ? styles.pending
                            : styles.failed
                        }`}
                    >
                      <span className={styles.label} style={{ fontSize: "14px", fontWeight: "bold" }}>{label}</span>
                      {step.time && (
                        <span
                          className={styles.time}
                          style={{ fontSize: "12px", color: "#402b2b" }}
                        >
                          {new Date(step.time).toLocaleString()}
                        </span>
                      )}
                    </div>

                    {!isLastNode && (
                      <>
                        {(isLastInRow && !isOddRow) ||
                          (isFirstInRow && isOddRow) ? (
                          <ArrowDownwardIcon
                            className={`${styles.verticalArrow} ${isOddRow
                              ? styles.verticalArrowRight
                              : styles.verticalArrowLeft
                              }`}
                          />
                        ) : (
                          <ArrowForwardIcon
                            className={`${styles.horizontalArrow} ${isOddRow ? styles.leftArrow : styles.rightArrow
                              }`}
                          />
                        )}
                      </>
                    )}

                    {!isLastNode && (
                      <>
                        {/* ✅ Vertical Line (↓) at last node of odd row & first node of even row */}
                        {(isLastInRow && !isOddRow) ||
                          (isFirstInRow && isOddRow) ? (
                          <div
                            className={`${styles.verticalLine} ${success ? styles.successLine : ""
                              } ${isOddRow
                                ? styles.verticalLineRight
                                : styles.verticalLineLeft
                              }`}
                          />
                        ) : (
                          /* ✅ Horizontal Line (→ or ←) */
                          <div
                            className={`${styles.horizontalLine} ${success ? styles.successLine : ""
                              } ${isOddRow ? styles.leftLine : styles.rightLine}`}
                          />
                        )}
                      </>
                    )}
                  </div>
                );
              })}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default ProductRoute;
