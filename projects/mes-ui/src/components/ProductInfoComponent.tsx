import React from "react";
import { Box, Typography } from "@mui/material";
import { ResponsiveSize } from "../interfaces/form-config.api.interface";
import useScreen from "../hooks/useScreenSize";

interface ProductInfoComponentProps {
  label: string;
  name: string;
  value: string;
  width?: ResponsiveSize;
  height?: ResponsiveSize;
  marginRight?: ResponsiveSize;
}

const ProductInfoComponent: React.FC<ProductInfoComponentProps> = ({
  label,
  value,
  marginRight,
  width,
}) => {
  const { width: deviceWidth, marginRight: deviceRightMargin } = useScreen(
    width,
    marginRight
  );
  return (
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        width: deviceWidth ?? "32%",
        padding: deviceWidth !== "mobile" ? "10px 0px" : "5px 0px",
        marginRight: deviceRightMargin || "0px",
      }}
    >
      <Typography component="div">
        <Typography
          variant="subtitle1"
          sx={{ fontWeight: "500", color: "#000000" }}
        >
          {label}
        </Typography>
        <Typography variant="body2">{value}</Typography>
      </Typography>
    </Box>
  );
};

export default ProductInfoComponent;
