/* Canvas area fixes to ensure proper visibility of all elements */

// Define z-index values to avoid import issues
$z-base: 1;
$z-canvas: 5;
$z-sidebars: 10;
$z-controls: 15;
$z-react-flow-controls: 20;
$z-nodes: 30;
$z-selected-nodes: 35;
$z-edges: 40;
$z-selected-edges: 45;
$z-edge-controls: 50;
$z-edge-buttons: 9999; // Higher z-index for edge buttons
$z-tooltips: 60;
$z-modals: 100;

// Canvas container
.canvas-container {
  position: relative;
  flex: 1;
  height: 100%;
  transition: all 0.3s ease;
  // Ensure canvas is above the base but below sidebars
  z-index: $z-canvas;
  overflow: visible !important;

  // Parent container override to ensure edges extend beyond boundaries
  .react-flow-wrapper {
    overflow: visible !important;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;

    .react-flow {
      overflow: visible !important;

      &__renderer {
        overflow: visible !important;
      }

      &__pane {
        overflow: visible !important;
        pointer-events: auto !important;
      }

      &__viewport {
        overflow: visible !important;
      }

      &__container {
        overflow: visible !important;
      }
    }
  }
}

// React Flow viewport to extend beyond boundaries
.react-flow__viewport {
  overflow: visible !important;
  pointer-events: auto !important;

  // Make all edge paths stand out with proper z-index
  path.react-flow__edge-path {
    stroke-width: 2px;
    z-index: $z-edges;

    &:hover {
      stroke-width: 3px;
    }
  }

  // Enhance edge interaction areas
  .react-flow__edge-interaction {
    cursor: pointer;
    stroke-width: 10px !important;
    opacity: 0;
    stroke: transparent;
    fill: none;
  }
}

// Add proper styling to connection lines while dragging
.react-flow__connection-path {
  stroke-width: 2px !important;
  stroke: #2196f3 !important;
  z-index: $z-edges;
}

// Ensure connection line extends beyond panel boundaries
.react-flow__connection {
  z-index: $z-edges;
  pointer-events: none;

  .react-flow__connection-path {
    pointer-events: none;
  }
}

// Make sure selected elements appear on top
.react-flow__node.selected {
  z-index: $z-selected-nodes !important;
}

.react-flow__edge.selected {
  z-index: $z-selected-edges !important;

  path {
    stroke-width: 3px;
  }
}