/* Control panel styles */
/* Use variables directly */
$primary-color: #2196f3;
$secondary-color: #ff9800;
$rework-color: #e91e63;
$light-bg: #f8f9fa;
$dark-bg: #2d3748;
$light-text: #f8f9fa;
$dark-text: #2d3748;
$sidebar-width: 240px;
$collapsed-width: 45px;
$border-radius: 4px;
$shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
$shadow-dark: 0 2px 10px rgba(0, 0, 0, 0.3);
$transition: all 0.3s ease;

.custom-controls-panel {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: $border-radius;
  padding: 8px;
  box-shadow: $shadow-light;
  z-index: 1000;

  .control-buttons {
    display: flex;
    flex-direction: row;
    gap: 8px;

    button {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: white;
      border: 1px solid #ddd;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 18px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      transition: all 0.2s ease;

      &:hover {
        transform: scale(1.1);
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
      }

      &:active {
        transform: scale(0.95);
      }

      span {
        display: flex;
        align-items: center;
        justify-content: center;
      }

      &.fit-view-button {
        background-color: $primary-color;
        color: white;
        border-color: #1565c0; /* Replaced darken($primary-color, 10%) */

        &:hover {
          background-color: #1976c0; /* Replaced darken($primary-color, 5%) */
        }
      }
    }
  }

  .dark-mode & {
    background-color: rgba($dark-bg, 0.8);
    box-shadow: $shadow-dark;

    .control-buttons button {
      background-color: #3a4556;
      border-color: #4a5568;
      color: white;

      &.fit-view-button {
        background-color: $primary-color;
        border-color: #0d47a1; /* Replaced darken($primary-color, 15%) */
      }
    }
  }
}