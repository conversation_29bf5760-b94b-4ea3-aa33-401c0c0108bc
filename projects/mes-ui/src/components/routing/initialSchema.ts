// src/initialSchema.js
const initialSchema = {
  "routing_schema": {
    "id": "r1",
    "name": "PCB Routing",
    "type": "no-idea-for-now",
    "product_id": "2",
    "components": {
      "pcb_load_feeder": {
        "position": {
          "x": -727,
          "y": -237
        },
        "node_type": "machine",
        "event_required": false
      },
      "laser_marking": {
        "position": {
          "x": -384,
          "y": -240
        },
        "node_type": "machine",
        "event_required": false
      },
      "solder_paste_printing": {
        "position": {
          "x": -106,
          "y": -240
        },
        "node_type": "machine",
        "event_required": false
      },
      "spi": {
        "position": {
          "x": 170,
          "y": -238
        },
        "node_type": "machine",
        "event_required": false
      },
      "pick_place": {
        "position": {
          "x": 411,
          "y": -243
        },
        "node_type": "machine",
        "event_required": false
      },
      "reflow": {
        "position": {
          "x": 686,
          "y": -245
        },
        "node_type": "machine",
        "event_required": false
      },
      "aoi": {
        "position": {
          "x": 957,
          "y": -243
        },
        "node_type": "machine",
        "event_required": false
      },
      "pb_pcb_unload": {
        "position": {
          "x": 1239,
          "y": -248
        },
        "node_type": "machine",
        "event_required": false
      },
      "pb_depanel": {
        "position": {
          "x": 1537,
          "y": -247
        },
        "node_type": "machine",
        "event_required": false
      },
      "pb_rework_aoi": {
        "position": {
          "x": 1026,
          "y": -375
        },
        "node_type": "machine",
        "event_required": false
      },
      "pb_thc_mounting": {
        "position": {
          "x": -628,
          "y": -52
        },
        "node_type": "machine",
        "event_required": false
      },
      "pb_pre_wave": {
        "position": {
          "x": -295,
          "y": -57
        },
        "node_type": "machine",
        "event_required": true
      },
      "wave_soldring": {
        "position": {
          "x": 32,
          "y": -59
        },
        "node_type": "machine",
        "event_required": false
      },
      "lead_cutting": {
        "position": {
          "x": 357,
          "y": -68
        },
        "node_type": "machine",
        "event_required": false
      },
      "touch_up": {
        "position": {
          "x": 681,
          "y": -73
        },
        "node_type": "machine",
        "event_required": false
      },
      "pb_post_wave": {
        "position": {
          "x": 954,
          "y": -71
        },
        "node_type": "machine",
        "event_required": true
      },
      "sap_confirmation": {
        "position": {
          "x": 1235,
          "y": -74
        },
        "node_type": "machine",
        "event_required": false
      },
      "fpt": {
        "position": {
          "x": 1537,
          "y": -72
        },
        "node_type": "machine",
        "event_required": false
      },
      "pb_testing": {
        "position": {
          "x": -200,
          "y": 165
        },
        "node_type": "machine",
        "event_required": true
      },
      "conformal_coating": {
        "position": {
          "x": 139,
          "y": 169
        },
        "node_type": "machine",
        "event_required": false
      },
      "conformal_inspection": {
        "position": {
          "x": 455,
          "y": 170
        },
        "node_type": "machine",
        "event_required": false
      },
      "pdi": {
        "position": {
          "x": 778,
          "y": 168
        },
        "node_type": "machine",
        "event_required": false
      },
      "sap_confirmation_ii": {
        "position": {
          "x": 1083,
          "y": 165
        },
        "node_type": "machine",
        "event_required": false
      },
      "move_to_store": {
        "position": {
          "x": 1379,
          "y": 162
        },
        "node_type": "machine",
        "event_required": false
      },
      "pb_rework_1": {
        "position": {
          "x": -522,
          "y": 72
        },
        "node_type": "rework_station",
        "event_required": true
      },
      "pb_rework_analysis": {
        "position": {
          "x": -572,
          "y": 282
        },
        "node_type": "rework_station",
        "event_required": true
      }
    },
    "route": {
      "start": "pcb_load_feeder",
      "end": "move_to_store",
      "connections": {
        "pcb_load_feeder": {
          "towards": {
            "default": "laser_marking",
            "conditions": [],
            "route_type": "main"
          }
        },
        "laser_marking": {
          "towards": {
            "default": "solder_paste_printing",
            "conditions": [],
            "route_type": "main"
          }
        },
        "solder_paste_printing": {
          "towards": {
            "default": "spi",
            "conditions": [],
            "route_type": "main"
          }
        },
        "spi": {
          "towards": {
            "default": "pick_place",
            "conditions": [],
            "route_type": "main"
          }
        },
        "pick_place": {
          "towards": {
            "default": "reflow",
            "conditions": [],
            "route_type": "main"
          }
        },
        "reflow": {
          "towards": {
            "default": "aoi",
            "conditions": [],
            "route_type": "main"
          }
        },
        "aoi": {
          "towards": {
            "default": "pb_pcb_unload",
            "conditions": [
              {
                "target": "pb_rework_aoi",
                "route_type": "main",
                "operator": "equals",
                "left": {
                  "node": "aoi",
                  "path": "inspection_status",
                  "type": "property"
                },
                "right": {
                  "type": "value",
                  "value": "false"
                }
              }
            ],
            "route_type": "main"
          }
        },
        "pb_pcb_unload": {
          "towards": {
            "default": "pb_depanel",
            "conditions": [],
            "route_type": "main"
          }
        },
        "pb_rework_aoi": {
          "towards": {
            "default": "aoi",
            "conditions": [],
            "route_type": "rework"
          }
        },
        "pb_depanel": {
          "towards": {
            "default": "pb_thc_mounting",
            "conditions": [],
            "route_type": "main"
          }
        },
        "pb_thc_mounting": {
          "towards": {
            "default": "pb_pre_wave",
            "conditions": [],
            "route_type": "main"
          }
        },
        "pb_pre_wave": {
          "towards": {
            "default": "wave_soldring",
            "conditions": [],
            "route_type": "main"
          }
        },
        "wave_soldring": {
          "towards": {
            "default": "lead_cutting",
            "conditions": [],
            "route_type": "main"
          }
        },
        "lead_cutting": {
          "towards": {
            "default": "touch_up",
            "conditions": [],
            "route_type": "main"
          }
        },
        "touch_up": {
          "towards": {
            "default": "pb_post_wave",
            "conditions": [],
            "route_type": "main"
          }
        },
        "pb_post_wave": {
          "towards": {
            "default": "sap_confirmation",
            "conditions": [],
            "route_type": "main"
          }
        },
        "sap_confirmation": {
          "towards": {
            "default": "fpt",
            "conditions": [],
            "route_type": "main"
          }
        },
        "fpt": {
          "towards": {
            "default": "pb_testing",
            "conditions": [],
            "route_type": "main"
          }
        },
        "pb_testing": {
          "towards": {
            "default": "conformal_coating",
            "conditions": [
              {
                "target": "pb_rework_1",
                "route_type": "main",
                "operator": "equals",
                "left": {
                  "node": "pb_testing",
                  "path": "inspection_status",
                  "type": "property"
                },
                "right": {
                  "type": "value",
                  "value": "false"
                }
              }
            ],
            "route_type": "main"
          }
        },
        "conformal_coating": {
          "towards": {
            "default": "conformal_inspection",
            "conditions": [],
            "route_type": "main"
          }
        },
        "conformal_inspection": {
          "towards": {
            "default": "pdi",
            "conditions": [],
            "route_type": "main"
          }
        },
        "pdi": {
          "towards": {
            "default": "sap_confirmation_ii",
            "conditions": [],
            "route_type": "main"
          }
        },
        "sap_confirmation_ii": {
          "towards": {
            "default": "move_to_store",
            "conditions": [],
            "route_type": "main"
          }
        },
        "pb_rework_analysis": {
          "towards": {
            "default": "pb_testing",
            "conditions": [],
            "route_type": "rework"
          }
        },
        "pb_rework_1": {
          "towards": {
            "default": "pb_testing",
            "conditions": [
              {
                "target": "pb_rework_analysis",
                "route_type": "rework",
                "operator": "equals",
                "left": {
                  "node": "pb_rework_1",
                  "path": "inspection_status",
                  "type": "property"
                },
                "right": {
                  "type": "value",
                  "value": "false"
                }
              }
            ],
            "route_type": "rework"
          }
        },
        "move_to_store": {
          "towards": {
            "default": "end",
            "end": true,
            "conditions": null,
            "route_type": "main"
          }
        }
      }
    }
  }
};

export default initialSchema;
