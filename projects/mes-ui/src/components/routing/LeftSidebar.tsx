import React, { useState } from 'react';
import NodePalette from './NodePalette';
import { NodeInfo } from '../../services/routingApiService';
import { InputAdornment, TextField } from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import './LeftSidebar.scss';

interface LeftSidebarProps {
  collapsed: boolean;
  setCollapsed: (collapsed: boolean) => void;
  processBlocks: NodeInfo[];
  onAddWorkstation?: () => void;
}

const LeftSidebar: React.FC<LeftSidebarProps> = ({
  collapsed,
  setCollapsed,
  processBlocks,
  onAddWorkstation
}) => {
  const [searchQuery, setSearchQuery] = useState('');

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  // Filter process blocks based on search query
  const filteredBlocks = searchQuery.trim() === ''
    ? processBlocks
    : processBlocks.filter(block =>
      block.label.toLowerCase().includes(searchQuery.toLowerCase())
    );

  return (
    <div className={`sidebar left-sidebar ${collapsed ? 'collapsed' : ''}`}>
      <button
        className="sidebar-toggle left-toggle"
        onClick={() => setCollapsed(!collapsed)}
        aria-label={collapsed ? "Expand palette" : "Collapse palette"}
      >
        {collapsed ? '→' : '←'}
      </button>

      {!collapsed && (
        <>
          <div className="workstation-palette-header">
            <div className="palette-title">Workstation Palette</div>
          </div>

          <div className="search-container">
            <TextField
              placeholder="Search workstations..."
              variant="outlined"
              size="medium"
              fullWidth
              value={searchQuery}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon fontSize="small" sx={{ color: '#94a3b8' }} />
                  </InputAdornment>
                ),
                sx: {
                  height: '44px',
                  fontSize: '14px',
                  borderRadius: '8px',
                  backgroundColor: '#ffffff',
                  boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                  transition: 'all 0.2s ease-in-out',
                  '& .MuiOutlinedInput-input': {
                    padding: '10px 8px',
                    fontSize: '14px',
                    fontFamily: 'ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
                    color: 'rgb(2, 8, 23)',
                    '&::placeholder': {
                      color: '#94a3b8',
                      opacity: 1,
                    },
                  },
                  '& .MuiInputAdornment-root': {
                    marginRight: '4px',
                    '& .MuiSvgIcon-root': {
                      fontSize: '18px',
                    }
                  },
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: '#e2e8f0',
                    borderWidth: '1px',
                  },
                  '&:hover': {
                    boxShadow: '0 2px 6px rgba(0, 0, 0, 0.12)',
                    '& .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#cbd5e1',
                    },
                  },
                  '&.Mui-focused': {
                    boxShadow: '0 0 0 3px rgba(2, 8, 23, 0.1)',
                    '& .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'rgb(2, 8, 23)',
                      borderWidth: '2px',
                    },
                  }
                }
              }}
            />
          </div>

          <div className="process-count">
            Process ({filteredBlocks.length})
          </div>

          <NodePalette availableNodes={filteredBlocks} />
        </>
      )}
    </div>
  );
};

export default LeftSidebar;
