.workstation-palette-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0;
  padding: 16px 16px 10px;
  border-bottom: 1px solid #e2e8f0;

  .palette-title {
    font-size: 18px;
    font-weight: 500;
    color: rgb(2, 8, 23);
    font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    letter-spacing: 0.01em;
  }

  .dark-mode & {
    .palette-title {
      color: #f0f0f0;
    }
  }
}

.search-container {
  padding: 10px 16px;
}

.process-count {
  font-size: 12px;
  font-weight: 500;
  color: #64748b;
  padding: 10px 16px 8px;
  margin-bottom: 0;
  border-top: 1px solid #e2e8f0;
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  letter-spacing: 0.01em;
}

/* Override NodePalette header to avoid duplication */
.workstation-palette-header+.node-palette-container .node-palette-header {
  display: none;
}

/* Override NodePalette styling */
.node-palette-container {
  padding: 4px 16px 16px;
  margin-top: 0;

  .node-category {
    margin-bottom: 16px;

    h3 {
      display: none;
    }
  }

  .node-items-grid {
    padding: 4px 0;
  }

  .node-item {
    height: 32px;
    margin-bottom: 6px;
    border-radius: 4px;
    padding: 6px 12px;
    background-color: transparent;
    border: 1px solid transparent;
    position: relative;
    cursor: grab;
    transition: all 0.2s ease;

    &::before {
      content: '+';
      position: absolute;
      left: 12px;
      top: 50%;
      transform: translateY(-50%);
      color: #64748b;
      font-size: 14px;
      font-weight: 400;
      line-height: 1;
    }

    .node-icon {
      display: none;
    }

    .node-label {
      font-weight: 400;
      padding-left: 14px;
      display: inline-block;
      transform: translateY(-1px);
      letter-spacing: 0.01em;
      color: rgb(2, 8, 23);
      font-size: 16px;
      font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    }

    &:hover {
      transform: none;
      background-color: #f8fafc;
      border: 1px solid #cbd5e1;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      transition: all 0.2s ease-in-out;
    }

    &:active {
      cursor: grabbing;
      transform: scale(0.98);
      background-color: #f1f5f9;
    }
  }
}

.empty-search-message {
  text-align: center;
  padding: 12px;
  color: #64748b;
  font-size: 12px;
  font-style: italic;
  background-color: #f8fafc;
  border-radius: 4px;
  margin: 8px 0;
  border: 1px dashed #e2e8f0;
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  letter-spacing: 0.01em;
}