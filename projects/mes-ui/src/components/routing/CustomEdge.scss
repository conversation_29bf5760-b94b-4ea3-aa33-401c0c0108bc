// CustomEdge styling
/* Use variables directly */
$primary-color: #2196f3;
$secondary-color: #ff9800;
$rework-color: #e91e63;
$light-bg: #f8f9fa;
$dark-bg: #2d3748;
$light-text: #f8f9fa;
$dark-text: #2d3748;
$sidebar-width: 240px;
$collapsed-width: 45px;
$border-radius: 4px;
$shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
$shadow-dark: 0 2px 10px rgba(0, 0, 0, 0.3);
$transition: all 0.3s ease;

/* Edge specific variables */
$hover-width: 5px;
$selected-width: 4px;
$default-width: 3px;
$delete-red: #f44336;
$delete-red-hover: #d32f2f;

.custom-edge {
  stroke-width: 2.5px; // Base line width for all edge types
  transition: all 0.2s ease;
  cursor: pointer;
  stroke-dasharray: none !important; // Force solid lines by default
  pointer-events: all !important; // Ensure edges are clickable
  z-index: 100 !important; // Higher z-index for visibility
  opacity: 1 !important; // Ensure full opacity
  filter: drop-shadow(0 0 2px rgba(33, 150, 243, 0.3)) !important; // Subtle glow effect
  shape-rendering: geometricPrecision !important; // Improves edge rendering precision
  stroke-linecap: round !important; // Rounded ends for better connection with dots

  &:hover {
    stroke-width: 3.5px; // Slightly thicker on hover
    filter: drop-shadow(0 0 4px rgba(33, 150, 243, 0.6)) !important;
    z-index: 1000 !important; // Bring to front on hover

    // Show buttons on hover
    .edge-buttons-group {
      opacity: 1;
    }
  }

  &.selected {
    stroke-width: 3.5px; // Same thickness when selected
    filter: drop-shadow(0 0 5px rgba(33, 150, 243, 0.7)) !important;
    z-index: 1001 !important; // Highest z-index when selected

    // Always show buttons when selected
    .edge-buttons-group {
      opacity: 1;
    }
  }

  // Connection type styles
  &.default {
    // Default connection - solid line
    stroke-dasharray: none !important;
  }

  &.conditional {
    // Conditional connection - dashed line
    stroke-dasharray: 5 5 !important;
  }

  // Route type styles
  &.rework {
    // Rework route - dashed line
    stroke-dasharray: 5 5 !important;
  }

  // Edge buttons group
  .edge-buttons-group {
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: all;
  }

  // Edge button wrappers
  .edge-button-wrapper {
    transition: transform 0.2s ease;
    pointer-events: all;

    &:hover {
      transform: scale(1.1);
    }

    &.edit .edge-edit-button {
      transition: stroke-width 0.2s ease, transform 0.2s ease;
      pointer-events: all;

      &:hover {
        stroke-width: 2.5px;
      }
    }

    &.delete .edge-delete-button {
      transition: stroke-width 0.2s ease;

      &:hover {
        stroke-width: 2.5px;
      }
    }
  }

  &:hover .edge-delete-button {
    opacity: 1;
    transform: scale(1);
  }
}

// Edge button container - new implementation
.edge-button-container {
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
  pointer-events: all;
  transform-origin: center center;
  z-index: 9999; /* Ensure it's above all other elements including the collapse bar */
  
  &.visible {
    opacity: 1;
    visibility: visible;
  }
  
  .edge-edit-button {
    transition: transform 0.2s ease, stroke-width 0.2s ease;
    
    &:hover {
      transform: scale(1.1);
      stroke-width: 2.5px;
    }
  }
}

// Edge label styling
.edge-label {
  background-color: white;
  padding: 3px 6px;
  border-radius: 4px;
  font-size: 10px;
  box-shadow: $shadow-light;
  pointer-events: none;

  &.default {
    border: 1px solid $primary-color;
  }

  &.conditional {
    border: 1px solid $secondary-color;
  }

  &.rework {
    border: 1px solid $rework-color;
  }

  .dark-mode & {
    background-color: #2d3748;
    color: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
  }
}

// Edge delete button styling - SVG-based approach
.edge-delete-button-wrapper {
  // SVG group for the delete button
  cursor: pointer;
  pointer-events: all !important;
  transition: opacity 0.2s ease, visibility 0.2s ease;

  // Add a subtle animation on hover
  circle {
    transition: all 0.2s ease;
    filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.3));

    &:hover {
      // Use transform instead of 'r' property which causes lint errors
      transform: scale(1.2); // Slightly larger on hover
      stroke-width: 3;
      filter: drop-shadow(0 3px 5px rgba(0, 0, 0, 0.4));
    }
  }

  // Style the X text
  text {
    transition: all 0.2s ease;

    &:hover {
      font-size: 18px;
      font-weight: 900;
    }
  }

  // Dark mode adjustments
  .dark-mode & {
    circle {
      fill: #2d3748;
      stroke: $delete-red;
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.5));
    }

    text {
      fill: white;
    }
  }
}

// Edge type styles
.edge-bezier {
  // Default style is bezier curve
  stroke-linecap: round;

  // Ensure consistent thickness
  path {
    stroke-width: 2.5px !important;
    stroke-dasharray: none !important;
  }

  &:hover path,
  &.selected path {
    stroke-width: 3.5px !important;
  }
}

.edge-straight {
  // No curve, direct line
  stroke-linecap: round; // Changed to round for better connection with dots

  // Ensure consistent thickness
  path {
    stroke-width: 2.5px !important;
    stroke-dasharray: none !important;
  }

  &:hover path,
  &.selected path {
    stroke-width: 3.5px !important;
  }
}

.edge-cross {
  // Cross-shaped edge
  stroke-linecap: round;
  stroke-linejoin: round;

  // Make cross edges consistent with other edge types
  path {
    stroke-width: 2.5px !important;
    stroke-dasharray: none !important; // Force solid lines for cross edges
  }

  &:hover path,
  &.selected path {
    stroke-width: 3.5px !important;
  }

  // Add a subtle shadow for better visibility
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

.edge-step {
  // Step-like appearance
  stroke-linecap: round; // Changed to round for better connection with dots
  stroke-linejoin: round; // Changed to round for smoother corners

  // Make step edges consistent with other edge types
  path {
    stroke-width: 2.5px !important;
    stroke-dasharray: none !important; // Force solid lines for step edges
  }

  &:hover path,
  &.selected path {
    stroke-width: 3.5px !important;
  }

  // Add a subtle shadow for better visibility
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

.edge-smoothstep {
  // Smooth step appearance
  stroke-linecap: round;
  stroke-linejoin: round;

  // Make smoothstep edges consistent with other edge types
  path {
    stroke-width: 2.5px !important;
    stroke-dasharray: none !important; // Force solid lines for smoothstep edges
  }

  &:hover path,
  &.selected path {
    stroke-width: 3.5px !important;
  }

  // Add a subtle shadow for better visibility
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

// Edge settings panel
.edge-settings-panel {
  background-color: white;
  border-radius: 8px;
  padding: 0;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  z-index: 9999; /* Ensure it's above all other elements including the collapse bar */
  position: relative;
  overflow: hidden;
  border: 1px solid #e0e0e0;
  
  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
    
    h4 {
      margin: 0;
      font-size: 14px;
      color: #2196f3;
    }
    
    .close-button {
      background: none;
      border: none;
      font-size: 18px;
      font-weight: bold;
      color: #666;
      cursor: pointer;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      
      &:hover {
        background-color: #e0e0e0;
        color: #333;
      }
    }
  }
  
  .connection-details {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 15px;
    background-color: #e3f2fd;
    border-bottom: 1px solid #e0e0e0;
    font-size: 12px;
    
    .connection-label {
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      
      strong {
        color: #2196f3;
      }
    }
    
    .connection-arrow {
      margin: 0 10px;
      color: #2196f3;
      font-weight: bold;
    }
  }
  
  .edge-type-selector {
    display: flex;
    flex-wrap: wrap;
    padding: 10px;
    gap: 8px;
    
    button {
      flex: 1 0 calc(50% - 8px); /* Two buttons per row with gap */
      min-width: 0;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 6px;
      background-color: white;
      cursor: pointer;
      transition: all 0.2s ease;
      font-size: 12px;
      display: flex;
      flex-direction: column;
      align-items: center;
      
      .edge-preview-icon {
        width: 40px;
        height: 20px;
        position: relative;
        margin-bottom: 5px;
        
        &::before, &::after {
          content: '';
          position: absolute;
          width: 6px;
          height: 6px;
          background-color: #2196f3;
          border-radius: 50%;
          top: 50%;
          transform: translateY(-50%);
        }
        
        &::before {
          left: 0;
        }
        
        &::after {
          right: 0;
        }
        
        &.bezier {
          &::after {
            content: '';
            position: absolute;
            width: 100%;
            height: 2px;
            background: transparent;
            border: none;
            top: 50%;
            left: 0;
            transform: translateY(-50%);
            
            &::before {
              content: '';
              position: absolute;
              width: 100%;
              height: 20px;
              border: 2px solid #2196f3;
              border-top-left-radius: 50%;
              border-top-right-radius: 50%;
              border-bottom: none;
              top: -10px;
              left: 0;
            }
          }
        }
        
        &.straight {
          &::after {
            content: '';
            position: absolute;
            width: 100%;
            height: 2px;
            background-color: #2196f3;
            top: 50%;
            left: 0;
            transform: translateY(-50%);
          }
        }
        
        &.step {
          &::after {
            content: '';
            position: absolute;
            width: 100%;
            height: 10px;
            border-top: 2px solid #2196f3;
            border-right: 2px solid #2196f3;
            border-bottom: 2px solid #2196f3;
            border-left: none;
            top: 50%;
            left: 0;
            transform: translateY(-50%);
          }
        }
        
        &.smoothstep {
          &::after {
            content: '';
            position: absolute;
            width: 100%;
            height: 10px;
            border-top: 2px solid #2196f3;
            border-right: 2px solid #2196f3;
            border-bottom: 2px solid #2196f3;
            border-left: none;
            border-top-right-radius: 5px;
            border-bottom-right-radius: 5px;
            top: 50%;
            left: 0;
            transform: translateY(-50%);
          }
        }
      }
      
      span {
        font-size: 11px;
      }
      
      &:hover {
        background-color: #f5f5f5;
        transform: translateY(-2px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      }
      
      &.active {
        background-color: #2196f3;
        color: white;
        border-color: #1976d2;
        
        .edge-preview-icon {
          &::before, &::after {
            background-color: white;
          }
          
          &.bezier, &.straight, &.step, &.smoothstep {
            &::after {
              border-color: white;
            }
          }
          
          &.straight::after {
            background-color: white;
          }
        }
      }
    }
  }
  
  .panel-footer {
    padding: 10px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: center;
    
    .delete-edge-button {
      background-color: #f44336;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 6px 12px;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.2s ease;
      
      &:hover {
        background-color: #d32f2f;
        transform: translateY(-2px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
      }
    }
  }
}