// CustomNode Styling
$node-width: 160px;
$node-height: 90px;
$node-border-radius: 4px;
$shadow-light: 0 2px 8px rgba(0, 0, 0, 0.08);
$shadow-dark: 0 4px 12px rgba(0, 0, 0, 0.2);
$transition: all 0.2s ease;

// Node Types Colors
$process-color: #4285F4;
$inspection-color: #34A853;
$testing-color: #FBBC05;
$rework-color: #EA4335;
$load-color: #5E35B1;
$unload-color: #00897B;
$storage-color: #F9A825;
$default-color: #7E57C2;

.custom-node {
  width: $node-width;
  height: $node-height;
  background-color: white;
  border: 0.5px solid rgba($default-color, 0.7);
  border-radius: $node-border-radius;
  position: relative;
  /* Simplify transitions */
  transition: box-shadow 0.3s ease;
  box-shadow: $shadow-light;
  display: flex;
  flex-direction: column;
  overflow: visible; // Allow handles to be visible outside the node
  /* Add contain property to improve performance */
  contain: layout style;

  &:hover {
    box-shadow: $shadow-dark;
  }

  &.selected {
    box-shadow: 0 0 0 2px #2196f3, $shadow-dark;
    transform: scale(1.02);
  }

  &.start-node {
    box-shadow: 0 0 0 3px #4caf50, $shadow-light;

    &:hover,
    &.selected {
      box-shadow: 0 0 0 3px #4caf50, $shadow-dark;
    }
  }

  &.end-node {
    box-shadow: 0 0 0 3px #f44336, $shadow-light;

    &:hover,
    &.selected {
      box-shadow: 0 0 0 3px #f44336, $shadow-dark;
    }
  }

  // Dark mode styling
  .dark-mode & {
    background-color: #2d3748;
    box-shadow: $shadow-dark;

    .node-header {
      border-bottom-color: #444;
    }

    .node-body {
      color: #f0f0f0;
    }
  }
}

.node-delete-btn {
  /* Fixed position in top-right corner */
  position: absolute;
  top: -10px;
  right: -10px;
  width: 24px;
  height: 24px;
  background-color: #f44336;
  border-radius: 50%;
  /* Use flex for perfect centering of the X */
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  font-weight: bold;
  line-height: 1;
  cursor: pointer;
  /* Smooth transition for opacity and visibility */
  opacity: 0;
  visibility: hidden;
  /* Extremely high z-index to ensure it's above everything */
  z-index: 10000;
  border: 2px solid white;
  padding: 0;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  /* Ensure it captures all pointer events */
  pointer-events: all !important;
  /* Add smooth transition for opacity */
  transition: opacity 0.15s ease, visibility 0.15s ease, background-color 0.15s ease, transform 0.15s ease;
  /* Ensure the button doesn't get clipped */
  overflow: visible;

  &:hover {
    /* Use direct values instead of transitions */
    background-color: #d32f2f;
    transform: scale(1.1);
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.5);
  }

  &:active {
    transform: scale(0.95);
    background-color: #b71c1c;
  }
}

/* Make delete button visible on hover and when selected */
.custom-node:hover .node-delete-btn,
.custom-node.selected .node-delete-btn {
  opacity: 1;
  visibility: visible;
}

.node-header {
  padding: 6px 8px;
  border-top-left-radius: $node-border-radius - 1px;
  border-top-right-radius: $node-border-radius - 1px;
  display: flex;
  align-items: center;
  background-color: $default-color;
  color: white;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  height: 28px;
}

.node-icon {
  font-size: 13px;
  margin-right: 5px;
}

.node-id {
  font-size: 10px;
  opacity: 0.8;
  font-family: monospace;
}

.node-body {
  padding: 6px 8px;
  color: #333;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden; // Prevent content from overflowing
  z-index: 1; // Ensure content is above handles
}

.node-label {
  font-weight: 600;
  font-size: 11px;
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.node-type {
  font-size: 9px;
  color: #666;
  font-style: italic;
  margin-bottom: 3px;
}

.node-role {
  display: inline-block;
  font-size: 8px;
  font-weight: bold;
  padding: 2px 3px;
  border-radius: 2px;
  margin-top: 2px;
  align-self: flex-start;

  &.start {
    background-color: #e8f5e9;
    color: #2e7d32;
  }

  &.end {
    background-color: #ffebee;
    color: #c62828;
  }

  .dark-mode & {
    &.start {
      background-color: #2e7d32;
      color: #e8f5e9;
    }

    &.end {
      background-color: #c62828;
      color: #ffebee;
    }
  }
}

// Connector wrappers for centered handles
.connector-wrapper {
  position: absolute;
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: crosshair; // Use crosshair cursor to indicate connection point
  transition: all 0.15s ease;
  width: 16px; // Even smaller clickable area
  height: 16px;
  border-radius: 50%;
  background-color: transparent; // No background by default
  pointer-events: all !important; // Ensure it can receive mouse events

  &:hover {
    background-color: rgba(33, 150, 243, 0.05); // Extremely subtle highlight on hover
    transform: none; // No scaling on hover
    box-shadow: none; // No shadow on hover
    border: none; // No border on hover
  }

  // Make the connector more visible when the node is selected
  .custom-node.selected & {
    background-color: rgba(33, 150, 243, 0.1);
  }

  &.top-connector {
    top: 0;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  &.bottom-connector {
    bottom: 0;
    left: 50%;
    transform: translate(-50%, 50%);
  }

  &.left-connector {
    left: 0;
    top: 50%;
    transform: translate(-50%, -50%);
  }

  &.right-connector {
    right: 0;
    top: 50%;
    transform: translate(50%, -50%);
  }

  // Ensure the handle is properly visible
  .react-flow__handle {
    opacity: 0.8 !important;
    visibility: visible !important;
    transition: all 0.15s ease;

    &:hover {
      transform: scale(1.1) !important;
      box-shadow: 0 0 2px rgba(33, 150, 243, 0.4) !important;
      opacity: 1 !important;
    }
  }

  // Handle styling for the node
  .node-handle {
    &.source-handle {
      background-color: rgba(76, 175, 80, 0.8) !important;
      transition: all 0.2s ease;

      &:hover {
        background-color: rgba(76, 175, 80, 1) !important;
        transform: scale(1.2) !important;
        box-shadow: 0 0 5px rgba(76, 175, 80, 0.6) !important;
      }
    }

    &.target-handle {
      background-color: rgba(233, 30, 99, 0.8) !important;
      transition: all 0.2s ease;

      &:hover {
        background-color: rgba(233, 30, 99, 1) !important;
        transform: scale(1.2) !important;
        box-shadow: 0 0 5px rgba(233, 30, 99, 0.6) !important;
      }
    }

    opacity: 1; // Fully visible by default

    // Highlight on hover
    .custom-node:hover &,
    .custom-node.selected & {
      transform: scale(1.3);
      box-shadow: 0 0 8px rgba(33, 150, 243, 0.8);
      opacity: 1;
      background-color: #1976d2; // Darker blue on hover
    }

    // Make the connector dot more visible when hovered directly
    &:hover {
      transform: scale(1.3);
      background-color: #1976d2; // Darker blue on hover
      box-shadow: 0 0 10px rgba(33, 150, 243, 1);
    }
  }
}

.node-handle {
  width: 20px !important; // Larger handle area for better interaction
  height: 20px !important;
  background-color: rgba(33, 150, 243, 0.3) !important; // Make handle slightly visible
  border: 2px solid white !important; // Add border for visibility
  border-radius: 50% !important;
  transition: all 0.2s ease;
  z-index: 100 !important; // Much higher z-index to ensure it's above other elements
  opacity: 1 !important; // Always visible
  position: absolute;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.3) !important;

  // Make handles more visible on hover
  .custom-node:hover &,
  .custom-node.selected & {
    background-color: rgba(33, 150, 243, 0.5) !important;
    box-shadow: 0 0 8px rgba(33, 150, 243, 0.8) !important;
  }

  // Enhance visibility and cursor on hover
  &:hover {
    cursor: crosshair;
    background-color: #1976d2 !important;
    transform: scale(1.3);
    box-shadow: 0 0 10px rgba(33, 150, 243, 1) !important;
  }

  // Make the handle area larger for better interaction
  &::before {
    content: '';
    position: absolute;
    top: -8px;
    left: -8px;
    right: -8px;
    bottom: -8px;
    z-index: -1;
  }

  // We're using the connector dot instead of colored handles

  &.top-handle {
    top: -10px !important;
  }

  &.right-handle {
    right: -10px !important;
  }

  &.bottom-handle {
    bottom: -10px !important;
  }

  &.left-handle {
    left: -10px !important;
  }

  // Specific styling for source and target handles
  &.source-handle {
    background-color: rgba(76, 175, 80, 0.5) !important; // Green for source

    &:hover {
      background-color: rgba(76, 175, 80, 0.8) !important;
    }
  }

  &.target-handle {
    background-color: rgba(233, 30, 99, 0.5) !important; // Pink for target

    &:hover {
      background-color: rgba(233, 30, 99, 0.8) !important;
    }
  }

  .dark-mode & {
    background-color: #2d3748;
  }
}