import React from 'react';
import RouteInfo from './RouteInfo';

interface RightSidebarProps {
  collapsed: boolean;
  setCollapsed: (collapsed: boolean) => void;
  configName: string;
  configCode: string;
  setConfigName: (name: string) => void;
  setConfigCode: (code: string) => void;
  nodeOptions: { value: string; label: string }[];
  startNodeId: string;
  endNodeId: string;
  setStartNodeId: (id: string) => void;
  setEndNodeId: (id: string) => void;
  onGenerateJson: () => void;
  onImportClick: () => void;
  onSaveSchema?: () => void;
  isSaving?: boolean;
  hasUnsavedChanges?: boolean;
}

const RightSidebar: React.FC<RightSidebarProps> = ({
  collapsed,
  setCollapsed,
  configName,
  configCode,
  setConfigName,
  setConfigCode,
  nodeOptions,
  startNodeId,
  endNodeId,
  setStartNodeId,
  setEndNodeId,
  onGenerate<PERSON>son,
  onImportClick,
  onSaveSchema,
  isSaving = false,
  hasUnsavedChanges = false
}) => {
  return (
    <div className={`sidebar right-sidebar ${collapsed ? 'collapsed' : ''}`}>
      <button
        className="sidebar-toggle right-toggle"
        onClick={() => setCollapsed(!collapsed)}
        aria-label={collapsed ? "Expand configuration" : "Collapse configuration"}
      >
        {collapsed ? '←' : '→'}
      </button>

      {!collapsed && (
        <div className="controls-container">
          <div className="help-text">
            <p><strong>Connection Rules:</strong></p>
            <ul>
              <li>Connect from green handles (outputs) to orange handles (inputs)</li>
              <li>Each connection can have its own type (default/conditional)</li>
              <li>Use the connection modal to configure connection properties</li>
            </ul>
          </div>

          <RouteInfo
            configName={configName}
            configCode={configCode}
            setConfigName={setConfigName}
            setConfigCode={setConfigCode}
            nodeOptions={nodeOptions}
            startNodeId={startNodeId}
            endNodeId={endNodeId}
            setStartNodeId={setStartNodeId}
            setEndNodeId={setEndNodeId}
          />
          <div className="control-divider"></div>

          <div className="help-text connection-guide">
            <p><strong>Multiple Connections Guide:</strong></p>
            <div className="handle-explanation">
              <div className="handle-diagram">
                <div className="node-example">
                  <div className="top-dot">⬤</div>
                  <div className="left-dot">⬤</div>
                  <div className="node-box">Node</div>
                  <div className="right-dot">⬤</div>
                  <div className="bottom-dot">⬤</div>
                </div>
              </div>
              <ul className="handle-list">
                <li><span className="input-dot">⬤</span> Left & Top: Input handles</li>
                <li><span className="output-dot">⬤</span> Right & Bottom: Output handles</li>
              </ul>
            </div>
            <p><small>You can create multiple connections between nodes using different handles</small></p>
          </div>

          {/* Buttons in horizontal layout */}
          <div className="action-buttons-container">
            <div className="button-row">
              <button onClick={onGenerateJson} className="action-button generate-button">
                Generate JSON
              </button>
              <button onClick={onImportClick} className="action-button import-button">
                Import JSON
              </button>
            </div>
            <button
              onClick={onSaveSchema}
              className="action-button save-button"
              disabled={isSaving || !hasUnsavedChanges}
            >
              {isSaving ? 'Saving...' : 'Save Schema'}
            </button>
          </div>

          {/* Add more space at the bottom to ensure Save Schema button is visible */}
          <div style={{ height: '50px' }}></div>
        </div>
      )}
    </div>
  );
};

export default RightSidebar;
