import React from 'react';
import React<PERSON>low, {
  Background,
  BackgroundVariant,
  Controls,
  Panel,
  ConnectionLineType,
  ConnectionMode,
  Node,
  Edge,
  Connection,
  OnNodesChange,
  OnEdgesChange,
  ReactFlowInstance
} from 'reactflow';
import EdgeOperationsPanel from './EdgeOperationsPanel';

interface FlowCanvasProps {
  nodes: Node[];
  edges: Edge[];
  onNodesChange?: OnNodesChange;
  onEdgesChange?: OnEdgesChange;
  onConnect?: (params: Connection) => void;
  onInit?: (instance: ReactFlowInstance) => void;
  onDrop?: (event: React.DragEvent<HTMLDivElement>) => void;
  onDragOver?: (event: React.DragEvent<HTMLDivElement>) => void;
  onEdgeClick?: (event: React.MouseEvent, edge: Edge) => void;
  onNodeClick?: (event: React.MouseEvent, node: Node) => void;
  onPaneClick?: () => void;
  onConnectStart?: (event: React.MouseEvent | React.TouchEvent, params: any) => void;
  onConnectEnd?: (event: MouseEvent | TouchEvent) => void;
  nodeTypes: any;
  edgeTypes: any;
  isValidConnection?: (connection: Connection) => boolean;
  selectedEdge: Edge | null;
  editSelectedEdge?: () => void;
  deleteSelectedEdge?: () => void;
  darkMode: boolean;
  toggleDarkMode?: () => void;
  reactFlowWrapper: React.RefObject<HTMLDivElement>;
  leftSidebarCollapsed: boolean;
  rightSidebarCollapsed: boolean;
  readonly?: boolean
}

const FlowCanvas: React.FC<FlowCanvasProps> = ({
  nodes,
  edges,
  onNodesChange,
  onEdgesChange,
  onConnect,
  onInit,
  onDrop,
  onDragOver,
  onEdgeClick,
  onNodeClick,
  onPaneClick,
  onConnectStart,
  onConnectEnd,
  nodeTypes,
  edgeTypes,
  isValidConnection,
  selectedEdge,
  editSelectedEdge,
  deleteSelectedEdge,
  darkMode,
  toggleDarkMode,
  reactFlowWrapper,
  leftSidebarCollapsed,
  rightSidebarCollapsed,
  readonly
}) => {
  return (
    <div
      className={`canvas-container ${leftSidebarCollapsed ? 'left-expanded' : ''} ${rightSidebarCollapsed ? 'right-expanded' : ''}`}
      ref={reactFlowWrapper}
    >
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onInit={onInit}
        onDrop={onDrop}
        onDragOver={onDragOver}
        onEdgeClick={onEdgeClick}
        onNodeClick={onNodeClick}
        onPaneClick={onPaneClick}
        onConnectStart={onConnectStart}
        onConnectEnd={onConnectEnd}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        defaultEdgeOptions={{
          type: 'custom',
          animated: false,
          style: {
            stroke: '#2196f3',
            strokeWidth: 4,
            strokeDasharray: 'none',
            opacity: 1,
            zIndex: 1000,
            filter: 'drop-shadow(0 0 5px rgba(33, 150, 243, 0.5))'
          },
          markerEnd: undefined
        }}
        connectionLineType={ConnectionLineType.Bezier}
        connectionLineStyle={{
          stroke: '#4caf50',
          strokeWidth: 4,
          strokeDasharray: 'none',
          filter: 'drop-shadow(0 0 8px rgba(76, 175, 80, 0.7))'
        }}
        connectionRadius={75}
        connectionMode={ConnectionMode.Loose}
        snapToGrid={true}
        snapGrid={[10, 10]}
        isValidConnection={isValidConnection}
        edgesFocusable={true}
        elementsSelectable={true}
        deleteKeyCode={['Backspace', 'Delete']}
        multiSelectionKeyCode={['Control', 'Meta']}
        minZoom={readonly ? 0.1 : 0.5}
        maxZoom={readonly ? 0.8 : 2}
        defaultZoom={readonly ? 0.5 : 1.0}
        fitView={false}
      >
        <Background
          variant={darkMode ? BackgroundVariant.Dots : BackgroundVariant.Lines}
          gap={darkMode ? 20 : 25}
          color={darkMode ? '#aaa' : '#eee'}
        />
        {!readonly && <Controls />}

        {/* Additional utility Panels */}
        {!readonly && (
          <Panel position="top-left" className="utility-panel">
            <button className="dark-mode-toggle" onClick={toggleDarkMode}>
              {darkMode ? '☀️ Light Mode' : '🌙 Dark Mode'}
            </button>
          </Panel>
        )}

        {/* Edge Operations Panel */}
        {!readonly &&
          <EdgeOperationsPanel
            selectedEdge={selectedEdge}
            onEdit={editSelectedEdge}
            onDelete={deleteSelectedEdge}
          />
        }
      </ReactFlow>
    </div>
  );
};

export default FlowCanvas;
