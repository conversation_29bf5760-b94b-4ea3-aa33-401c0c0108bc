/* ConnectionModal.scss */
/* Use variables directly */
$primary-color: #2196f3;
$secondary-color: #ff9800;
$rework-color: #e91e63;
$light-bg: #f8f9fa;
$dark-bg: #2d3748;
$light-text: #f8f9fa;
$dark-text: #2d3748;
$sidebar-width: 240px;
$collapsed-width: 45px;
$border-radius: 4px;
$shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
$shadow-dark: 0 2px 10px rgba(0, 0, 0, 0.3);
$transition: all 0.3s ease;

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999; /* Increased z-index to ensure it's above all other elements including drawers */
}

.modal-content {
  background-color: white;
  padding: 25px;
  border-radius: 12px;
  min-width: 380px;
  max-width: 500px;
  max-height: 90vh; /* Limit height to prevent overflow */
  overflow-y: auto; /* Add scrolling for tall content */
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  position: relative;
  margin-right: 240px; /* Offset to prevent overlap with right drawer */
  
  @media (max-width: 768px) {
    margin-right: 0; /* Remove offset on smaller screens */
    min-width: 320px;
  }

  h2 {
    margin-top: 0;
    margin-bottom: 20px;
    text-align: center;
    color: #2196f3;
    font-size: 1.5rem;
    border-bottom: 2px solid #e3f2fd;
    padding-bottom: 10px;
  }

  .connection-info {
    background-color: #f5f5f5;
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 15px;
    font-size: 0.9rem;
    text-align: center;
    
    strong {
      color: #2196f3;
    }
  }

  div,
  fieldset {
    margin-bottom: 15px;
  }

  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
  }

  select,
  input[type="text"],
  input[type="number"] {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    box-sizing: border-box; /* Include padding in width */
    background-color: #fff;
    font-size: 14px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    
    &:focus {
      border-color: #2196f3;
      box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.2);
      outline: none;
    }
  }

  /* Ensure select elements work properly across browsers */
  select {
    appearance: menulist !important;
    -webkit-appearance: menulist !important;
    -moz-appearance: menulist !important;
    background-image: none !important;
    padding-right: 20px !important;
    cursor: pointer;
  }

  /* Style for the connection name input */
  input[type="text"][placeholder*="Connection from"] {
    font-weight: 500;
    color: #2196f3;
    background-color: #f0f8ff;
  }

  /* Style for the priority input */
  input[type="number"] {
    width: 80px;
    text-align: center;
    font-weight: bold;
  }
}

.conditional-fieldset {
  border: 1px solid #2196f3;
  padding: 15px;
  border-radius: 8px;
  background-color: rgba(33, 150, 243, 0.05);
  margin-top: 15px;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.1);

  legend {
    font-weight: bold;
    padding: 0 10px;
    color: #2196f3;
    background-color: white;
    border-radius: 4px;
  }

  small {
    display: block;
    margin-top: 10px;
    color: #666;
    font-style: italic;
  }
}

.modal-actions {
  display: flex;
  justify-content: space-between;
  gap: 15px; /* Increased space between buttons */
  margin-top: 20px;
  position: sticky; /* Make buttons sticky */
  bottom: 0; /* Stick to bottom */
  background-color: white;
  padding: 15px 0 5px;
  border-top: 1px solid #eee;
  z-index: 10;

  button {
    padding: 12px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    flex: 1; /* Make buttons equal width */
    font-weight: 600;
    font-size: 14px;
    transition: all 0.2s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);

    &.save-button {
      /* Save button */
      background-color: #2196f3;
      color: white;

      &:hover {
        background-color: #1976d2;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      }
    }

    &.cancel-button {
      /* Cancel button */
      background-color: #f5f5f5;
      color: #333;
      border: 1px solid #ddd;

      &:hover {
        background-color: #e0e0e0;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }
    }

    &:disabled {
      background-color: #cccccc;
      color: #666666;
      cursor: not-allowed;
      opacity: 0.7;
      transform: none;
      box-shadow: none;
    }
  }
}

/* Form row for handle selection */
.form-row {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.half-width {
  flex: 1;
  margin-bottom: 0;
}

/* Edge Type Preview Styles */
.edge-type-preview {
  margin-top: 15px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #f8f9fa;
  padding: 10px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
  
  &::before, &::after {
    content: '';
    position: absolute;
    width: 12px;
    height: 12px;
    background-color: #2196f3;
    border-radius: 50%;
    z-index: 2;
  }
  
  &::before {
    left: 15px;
  }
  
  &::after {
    right: 15px;
  }
}

.edge-preview {
  position: relative;
  width: 90%;
  height: 3px;
  background-color: #2196f3;
  box-shadow: 0 1px 3px rgba(33, 150, 243, 0.5);

  /* Arrow styling for all edge types */
  &::after {
    content: '';
    position: absolute;
    right: -2px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 8px solid #2196f3;
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
  }
}

.edge-preview-bezier {
  height: 2px;
  transform: perspective(100px);
  border-radius: 50%;

  &::before,
  &::after {
    z-index: 2;
  }

  &::after {
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
  }
}

.edge-preview-straight {
  height: 2px;
}

.edge-preview-step {
  width: 90%;
  height: 2px;
  background-color: transparent;
  position: relative;

  &::before,
  &::after {
    z-index: 2;
  }

  &::before {
    box-shadow: 20px 0 0 -1px #e3f2fd, 21px 0 0 -1px #2196f3;
  }

  &::after {
    box-shadow: -20px 0 0 -1px #e3f2fd, -21px 0 0 -1px #2196f3;
  }

  /* This creates the step shape */
  &::after {
    content: '';
    position: absolute;
    left: 10px;
    top: -10px;
    width: calc(100% - 20px);
    height: 20px;
    border-left: 2px solid #2196f3;
    border-right: 2px solid #2196f3;
    border-top: 2px solid #2196f3;
    border-bottom: none;
  }
}

/* Smoothstep edge preview */
.edge-preview-smoothstep {
  background-color: transparent;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    top: -10px;
    height: 20px;
    border-radius: 10px;
    border-top: 2px solid #2196f3;
  }
}
