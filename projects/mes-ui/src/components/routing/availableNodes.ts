// availableNodes.js - List of available nodes for the PCB routing editor

const availableNodes = [
  { id: 'pcb_load_feeder', label: 'PCB Load Feeder', nodeType: 'load' },
  { id: 'laser_marking', label: 'Laser Marking', nodeType: 'process' },
  { id: 'solder_paste_printing', label: 'Solder Paste Printing', nodeType: 'process' },
  { id: 'spi', label: 'SPI Inspection', nodeType: 'inspection' },
  { id: 'pick_place', label: 'Pick & Place', nodeType: 'process' },
  { id: 'reflow', label: 'Reflow Oven', nodeType: 'process' },
  { id: 'aoi', label: 'AOI Inspection', nodeType: 'inspection' },
  { id: 'pb_pcb_unload', label: 'PCB Unload', nodeType: 'unload' },
  { id: 'pb_depanel', label: 'Depanelization', nodeType: 'process' },
  { id: 'pb_rework_aoi', label: 'Rework AOI', nodeType: 'rework' },
  { id: 'pb_thc_mounting', label: 'THC Mounting', nodeType: 'process' },
  { id: 'pb_pre_wave', label: 'Pre-Wave', nodeType: 'process' },
  { id: 'wave_soldring', label: 'Wave Soldering', nodeType: 'process' },
  { id: 'lead_cutting', label: 'Lead Cutting', nodeType: 'process' },
  { id: 'touch_up', label: 'Touch Up', nodeType: 'process' },
  { id: 'pb_post_wave', label: 'Post-Wave', nodeType: 'process' },
  { id: 'sap_confirmation', label: 'SAP Confirmation', nodeType: 'process' },
  { id: 'fpt', label: 'Flying Probe Test', nodeType: 'testing' },
  { id: 'pb_testing', label: 'PCB Testing', nodeType: 'testing' },
  { id: 'conformal_coating', label: 'Conformal Coating', nodeType: 'process' },
  { id: 'conformal_inspection', label: 'Conformal Inspection', nodeType: 'inspection' },
  { id: 'pdi', label: 'PDI Inspection', nodeType: 'inspection' },
  { id: 'sap_confirmation_ii', label: 'SAP Confirmation II', nodeType: 'process' },
  { id: 'move_to_store', label: 'Move to Store', nodeType: 'storage' },
  { id: 'pb_rework_1', label: 'Rework Station 1', nodeType: 'rework' },
  { id: 'pb_rework_analysis', label: 'Rework Analysis', nodeType: 'rework' },
  
  // Additional node types that might be needed
  { id: 'functional_test', label: 'Functional Test', nodeType: 'testing' },
  { id: 'ictt', label: 'In-Circuit Test', nodeType: 'testing' },
  { id: 'qc_inspection', label: 'QC Inspection', nodeType: 'inspection' },
  { id: 'final_test', label: 'Final Test', nodeType: 'testing' },
  { id: 'packaging', label: 'Packaging', nodeType: 'process' }
];

export default availableNodes;
