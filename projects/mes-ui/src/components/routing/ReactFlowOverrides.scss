/* ReactFlow Overrides to fix z-index and UI issues */

// Define z-index variables directly in this file to avoid import issues
$z-base: 1;
$z-canvas: 5;
$z-sidebars: 10;
$z-controls: 15;
$z-react-flow-controls: 20;
$z-nodes: 30;
$z-selected-nodes: 35;
$z-edges: 40;
$z-selected-edges: 45;
$z-edge-controls: 50;
$z-edge-buttons: 9999; // Higher z-index for edge buttons
$z-tooltips: 60;
$z-modals: 100;

.react-flow {
  &__renderer {
    z-index: $z-canvas;
  }

  &__pane {
    z-index: $z-base !important;
  }

  &__node {
    z-index: $z-nodes !important;

    &.selected {
      z-index: $z-selected-nodes !important;
    }
  }

  &__edge {
    z-index: $z-edges !important;

    &.selected {
      z-index: $z-selected-edges !important;
    }

    &-path {
      pointer-events: all !important;
    }

    &-interaction {
      opacity: 0;
      pointer-events: all !important;
    }

    &__controls {
      z-index: $z-edge-controls !important;
    }

    // Ensure SVG elements within edges have proper pointer events
    svg,
    g,
    path,
    circle,
    text,
    foreignObject {
      pointer-events: all !important;
    }
  }

  &__controls {
    z-index: $z-react-flow-controls !important;


    .dark-mode & {
      background-color: #364150;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    }

    button {
      background-color: white;
      border: none;
      border-bottom: 1px solid #eee;
      cursor: pointer;

      path {
        fill: #555;
      }

      &:hover {
        background-color: #f0f0f0;
      }

      .dark-mode & {
        background-color: #364150;
        border-color: #4a5568;

        path {
          fill: #ddd;
        }

        &:hover {
          background-color: #2d3748;
        }
      }
    }
  }

  &__minimap {
    z-index: $z-react-flow-controls !important;
    bottom: 20px !important;
    right: 20px !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;
    position: absolute;
    opacity: 0.8;
    transition: opacity 0.2s ease;

    &:hover {
      opacity: 1;
    }

    .dark-mode & {
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    }
  }
}

// Fix for Safari and Firefox
.react-flow__edge-path {
  pointer-events: all !important;
}