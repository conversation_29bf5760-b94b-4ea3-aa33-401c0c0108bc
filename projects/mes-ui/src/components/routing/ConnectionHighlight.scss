/* Styles for highlighting connectors during connection */
body.connecting {
  .connector-wrapper {
    background-color: rgba(33, 150, 243, 0.2);
    transform: scale(1.2);
    box-shadow: 0 0 8px rgba(33, 150, 243, 0.5);
    border: 2px solid rgba(33, 150, 243, 0.5);
  }

  .connector-dot {
    transform: scale(1.3);
    box-shadow: 0 0 10px rgba(33, 150, 243, 0.9);
    background-color: #1976d2;
    opacity: 1;
  }
}

/* Pulse animation for connection line */
@keyframes pulse {
  0% {
    stroke-opacity: 0.6;
    stroke-width: 2;
  }
  50% {
    stroke-opacity: 1;
    stroke-width: 4;
  }
  100% {
    stroke-opacity: 0.6;
    stroke-width: 2;
  }
}

.react-flow__connection-path {
  animation: pulse 1.5s infinite;
}
