// Node Palette Styling
$sidebar-width: 280px;
$shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
$shadow-dark: 0 2px 10px rgba(0, 0, 0, 0.5);
$border-radius: 8px;
$transition: all 0.3s ease;

// Node Types Colors
$process-color: #4285F4;
$inspection-color: #34A853;
$testing-color: #FBBC05;
$rework-color: #EA4335;
$load-color: #5E35B1;
$unload-color: #00897B;
$storage-color: #F9A825;
$default-color: #7E57C2;

.node-palette-container {
  height: 100%;
  width: 100%;
  padding: 16px;
  overflow-y: auto;
}

.node-palette-header {
  margin: 0 0 20px 0;
  padding-bottom: 10px;
  border-bottom: 2px solid #f0f0f0;

  h2 {
    font-size: 18px;
    color: #333;
    margin: 0;
  }

  .dark-mode & {
    border-color: #444;

    h2 {
      color: #f0f0f0;
    }
  }
}

.node-category {
  margin-bottom: 24px;

  h3 {
    font-size: 14px;
    color: #666;
    margin: 0 0 12px 0;
    padding: 0;
    display: flex;
    align-items: center;

    .category-icon {
      margin-right: 8px;
      font-size: 16px;
    }

    .dark-mode & {
      color: #bbb;
    }
  }
}

.node-items-grid {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0 8px;
}

.node-item {
  background-color: white;
  border: 1px solid #eee;
  border-radius: $border-radius;
  padding: 6px 10px;
  display: flex;
  flex-direction: row;
  align-items: center;
  cursor: grab;
  transition: $transition;
  position: relative;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  height: 38px;
  width: 100%;
  margin-bottom: 5px;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 10;
  }

  &:active {
    cursor: grabbing;
    transform: translateY(-1px) scale(0.98);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &.process {
    border-color: $process-color;

    .node-icon {
      color: $process-color;
    }

    &::after {
      background-color: rgba($process-color, 0.05);
    }
  }

  &.inspection {
    border-color: $inspection-color;

    .node-icon {
      color: $inspection-color;
    }

    &::after {
      background-color: rgba($inspection-color, 0.05);
    }
  }

  &.testing {
    border-color: $testing-color;

    .node-icon {
      color: $testing-color;
    }

    &::after {
      background-color: rgba($testing-color, 0.05);
    }
  }

  &.rework {
    border-color: $rework-color;

    .node-icon {
      color: $rework-color;
    }

    &::after {
      background-color: rgba($rework-color, 0.05);
    }
  }

  &.load,
  &.unload {
    border-color: $load-color;

    .node-icon {
      color: $load-color;
    }

    &::after {
      background-color: rgba($load-color, 0.05);
    }
  }

  &.storage {
    border-color: $storage-color;

    .node-icon {
      color: $storage-color;
    }

    &::after {
      background-color: rgba($storage-color, 0.05);
    }
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
  }

  .node-icon {
    font-size: 18px;
    margin-right: 10px;
    transition: transform 0.2s ease;
    flex-shrink: 0;

    .node-item:hover & {
      transform: scale(1.1);
    }
  }

  .node-label {
    font-size: 12px;
    text-align: left;
    font-weight: 500;
    color: #333;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    white-space: nowrap;
    letter-spacing: 0.1px;
    line-height: 1.2;
    flex-grow: 1;
  }

  .dark-mode & {
    background-color: #364150;
    border-color: #4a5568;

    .node-label {
      color: #f0f0f0;
    }

    &:hover {
      box-shadow: $shadow-dark;
    }
  }
}

.palette-search {
  position: relative;
  margin-bottom: 16px;

  input {
    width: 100%;
    padding: 8px 12px;
    padding-left: 32px;
    border-radius: $border-radius;
    border: 1px solid #ddd;
    transition: $transition;

    &:focus {
      outline: none;
      border-color: $process-color;
      box-shadow: 0 0 0 2px rgba($process-color, 0.2);
    }
  }

  .search-icon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    font-size: 14px;
  }

  .dark-mode & {
    input {
      background-color: #2d3748;
      border-color: #4a5568;
      color: #f0f0f0;

      &::placeholder {
        color: #a0aec0;
      }

      &:focus {
        border-color: $process-color;
      }
    }
  }
}

.empty-search-message {
  text-align: center;
  padding: 20px;
  color: #666;
  font-style: italic;

  .dark-mode & {
    color: #a0aec0;
  }
}