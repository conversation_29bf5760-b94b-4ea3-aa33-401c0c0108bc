/* src/NodePalette.css */
.node-palette {
  display: flex;
  flex-direction: column;
  padding: 10px;
  height: 100%;
  overflow-y: auto;
}

.node-palette h3 {
  margin-top: 0;
  text-align: center;
  border-bottom: 1px solid #ddd;
  padding-bottom: 10px;
  margin-bottom: 15px;
}

/* Search container styling */
.search-container {
  position: relative;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.search-input {
  width: 100%;
  padding: 8px 30px 8px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
}

.search-input:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.clear-search-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  font-size: 20px;
  color: #777;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
}

.clear-search-btn:hover {
  color: #333;
  background-color: #f0f0f0;
}

/* Search results count styling */
.search-results-count {
  font-size: 12px;
  color: #666;
  margin-bottom: 10px;
  text-align: right;
  font-style: italic;
}

.no-results {
  text-align: center;
  padding: 15px;
  color: #666;
  font-style: italic;
  background-color: #f8f8f8;
  border-radius: 4px;
}

/* Node list styling */
.node-list {
  overflow-y: auto;
  flex-grow: 1;
  margin-right: 5px; /* Space for scrollbar */
}

.palette-node {
  padding: 8px;
  margin: 4px 0;
  background: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: grab;
  text-align: center;
  transition: background-color 0.2s, transform 0.1s;
}

.palette-node:hover {
  background-color: #e0e0e0;
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.palette-node:active {
  cursor: grabbing;
  transform: translateY(0);
}

.node-label {
  font-weight: bold;
  margin-bottom: 4px;
}

.node-id {
  font-size: 12px;
  color: #666;
  font-family: monospace;
}
