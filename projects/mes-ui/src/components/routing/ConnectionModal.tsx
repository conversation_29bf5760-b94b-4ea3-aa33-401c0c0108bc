// src/ConnectionModal.js
import React, { useState, useEffect } from 'react';
import './ConnectionModal.scss';

interface ConnectionData {
  connectionType: string;
  routeType: string;
  edgeType: string;
  conditionPath?: string;
  conditionValue?: string;
  operator?: string;
}

interface ConnectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: ConnectionData) => void;
  sourceNodeId: string;
  targetNodeId: string;
  initialData?: ConnectionData;
  isEditing: boolean;
}

const ConnectionModal: React.FC<ConnectionModalProps> = ({
  isOpen,
  onClose,
  onSave,
  sourceNodeId,
  targetNodeId,
  initialData,
  isEditing
}) => {
  const [connectionType, setConnectionType] = useState('default'); // 'default' or 'conditional'
  const [routeType, setRouteType] = useState('main'); // 'main' or 'rework'
  const [edgeType, setEdgeType] = useState('bezier'); // 'bezier', 'straight', 'step', or 'smoothstep'

  // --- Simplified Conditional Fields ---
  // In a real app, you'd have state for operator, path, value, etc.
  // and likely a way to add multiple conditions per edge.
  const [conditionPath, setConditionPath] = useState('');
  const [conditionValue, setConditionValue] = useState('');
  const [operator, setOperator] = useState('equals');

  // Initialize form with existing data when editing
  useEffect(() => {
    if (isEditing && initialData) {
      setConnectionType(initialData.connectionType || 'default');
      setRouteType(initialData.routeType || 'main');
      setEdgeType(initialData.edgeType || 'bezier');
      setConditionPath(initialData.conditionPath || '');
      setConditionValue(initialData.conditionValue || '');
      setOperator(initialData.operator || 'equals');
    }
  }, [isEditing, initialData]);

  if (!isOpen) {
    console.log('Connection modal is closed');
    return null;
  }

  console.log('Rendering connection modal for', sourceNodeId, 'to', targetNodeId);

  const handleSave = () => {
    const connectionData: ConnectionData = {
      connectionType,
      routeType,
      edgeType,
    };
    if (connectionType === 'conditional') {
      // Add simplified conditional data
      connectionData.conditionPath = conditionPath || 'condition_path_placeholder';
      connectionData.conditionValue = conditionValue || 'condition_value_placeholder';
      connectionData.operator = operator;
      // In a real app, you would validate these fields
    }

    console.log('Saving connection data:', connectionData);
    onSave(connectionData);

    // Reset form for next time (optional)
    setConnectionType('default');
    setRouteType('main');
    setEdgeType('bezier');
    setConditionPath('');
    setConditionValue('');
    setOperator('equals');
  };

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <h2>{isEditing ? 'Edit Connection' : 'Configure Connection'}</h2>
        {sourceNodeId && targetNodeId && (
          <p className="connection-info">
            From: <strong>{sourceNodeId}</strong> To: <strong>{targetNodeId}</strong>
          </p>
        )}

        <div className="form-group">
          <label>Connection Type:</label>
          <select value={connectionType} onChange={(e) => setConnectionType(e.target.value)}>
            <option value="default">Default</option>
            <option value="conditional">Conditional</option>
          </select>
        </div>

        <div className="form-group">
          <label>Route Type:</label>
          <select value={routeType} onChange={(e) => setRouteType(e.target.value)}>
            <option value="main">Main</option>
            <option value="rework">Rework</option>
          </select>
        </div>

        <div className="form-group">
          <label>Edge Style:</label>
          <select value={edgeType} onChange={(e) => setEdgeType(e.target.value)}>
            <option value="bezier">Bezier (Curved)</option>
            <option value="straight">Straight</option>
            <option value="step">Step</option>
            <option value="smoothstep">Smooth Step</option>
          </select>

          {/* Visual preview of the selected edge type */}
          <div className="edge-type-preview">
            <div className={`edge-preview edge-preview-${edgeType}`}></div>
          </div>
          <small>This is how the connection will appear in the diagram</small>
        </div>

        {connectionType === 'conditional' && (
          <fieldset className='conditional-fieldset'>
            <legend>Condition Details</legend>
            <div className="form-group">
              <label>Operator:</label>
              <select value={operator} onChange={e => setOperator(e.target.value)}>
                <option value="equals">equals</option>
                <option value="not_equals">not equals</option>
                <option value="greater_than">greater than</option>
                <option value="less_than">less than</option>
              </select>
            </div>
            <div className="form-group">
              <label>Property Path:</label>
              <input
                type="text"
                value={conditionPath}
                onChange={(e) => setConditionPath(e.target.value)}
                placeholder="e.g., inspection_status"
              />
            </div>
            <div className="form-group">
              <label>Value:</label>
              <input
                type="text"
                value={conditionValue}
                onChange={(e) => setConditionValue(e.target.value)}
                placeholder="e.g., false or design"
              />
            </div>
          </fieldset>
        )}

        <div className="modal-actions">
          <button onClick={handleSave} className="save-button">
            {isEditing ? 'Update Connection' : 'Save Connection'}
          </button>
          <button onClick={() => {
            console.log('Cancelling connection modal');
            onClose();
          }} className="cancel-button">Cancel</button>
        </div>
      </div>
    </div>
  );
};

export default ConnectionModal;
