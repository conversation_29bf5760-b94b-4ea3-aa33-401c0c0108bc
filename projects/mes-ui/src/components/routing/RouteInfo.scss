/* Route info styles */
/* Use variables directly */
$primary-color: #2196f3;
$secondary-color: #ff9800;
$rework-color: #e91e63;
$light-bg: #f8f9fa;
$dark-bg: #2d3748;
$light-text: #f8f9fa;
$dark-text: #2d3748;
$sidebar-width: 240px;
$collapsed-width: 45px;
$border-radius: 4px;
$shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
$shadow-dark: 0 2px 10px rgba(0, 0, 0, 0.3);
$transition: all 0.3s ease;

.route-info {
  padding: 0;

  h3 {
    margin-top: 0;
    color: #444;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 10px;
    margin-bottom: 16px;

    .dark-mode & {
      color: $light-text;
      border-color: #444;
    }
  }

  .control-item {
    margin-bottom: 16px;

    label {
      display: block;
      margin-bottom: 6px;
      font-weight: 500;
      font-size: 14px;
      color: #555;

      .dark-mode & {
        color: $light-text;
      }
    }

    select,
    .control-input {
      width: 100%;
      padding: 8px 12px;
      height: 40px;
      border-radius: 4px;
      border: 1px solid #e2e8f0;
      background-color: white;
      font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      font-size: 0.875rem;
      color: rgb(2, 8, 23);
      appearance: none;
      background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
      background-repeat: no-repeat;
      background-position: right 8px center;
      background-size: 16px;
      cursor: pointer;

      &:hover {
        border-color: #cbd5e1;
      }

      &:focus {
        outline: none;
        border-color: #94a3b8;
        box-shadow: none;
      }

      .dark-mode & {
        background-color: #3a4556;
        color: $light-text;
        border-color: #444;
      }
    }
  }
}