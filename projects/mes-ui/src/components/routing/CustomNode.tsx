import React, { memo, useCallback, useRef, useEffect } from 'react';
import { <PERSON>le, Position, useReactFlow } from 'reactflow';
import './CustomNode.scss';

type NodeType = 'process' | 'inspection' | 'testing' | 'rework' | 'load' | 'unload' | 'storage' | 'default';

interface NodeData {
  nodeType: string;
  label: string;
  isStartNode?: boolean;
  isEndNode?: boolean;
  inputs?: number;
  outputs?: number;
}

interface CustomNodeProps {
  data: NodeData;
  id: string;
  selected: boolean;
  isConnectable: boolean;
  xPos?: number;
  yPos?: number;
  onDelete?: (nodeId: string) => void;
}

// Map node types to icons
const nodeIcons: Record<NodeType, string> = {
  'process': '⚙️',
  'inspection': '🔍',
  'testing': '🧪',
  'rework': '🔄',
  'load': '⬆️',
  'unload': '⬇️',
  'storage': '📦',
  'default': '📋'
};

// Map node types to colors
const nodeColors: Record<NodeType, string> = {
  'process': '#4285F4',
  'inspection': '#34A853',
  'testing': '#FBBC05',
  'rework': '#EA4335',
  'load': '#5E35B1',
  'unload': '#00897B',
  'storage': '#F9A825',
  'default': '#7E57C2'
};

// Get color based on node type
const getNodeColor = (nodeType: string): string => {
  // Extract base type from node types like "aoi_inspection"
  const baseType = Object.keys(nodeColors).find(type =>
    nodeType.toLowerCase().includes(type)
  ) as NodeType;
  return nodeColors[baseType] || nodeColors.default;
};

// Get icon based on node type
const getNodeIcon = (nodeType: string): string => {
  const baseType = Object.keys(nodeIcons).find(type =>
    nodeType.toLowerCase().includes(type)
  ) as NodeType;
  return nodeIcons[baseType] || nodeIcons.default;
};

const CustomNode: React.FC<CustomNodeProps> = ({ data, id, selected, isConnectable, onDelete }) => {
  // Use the ReactFlow hook to access node deletion functionality
  const { deleteElements } = useReactFlow();

  // Use ref for the node element
  const nodeRef = useRef<HTMLDivElement>(null);

  // Determine if this is a start or end node
  const isStartNode = data.isStartNode;
  const isEndNode = data.isEndNode;

  // Get appropriate styling based on node type
  const nodeColor = getNodeColor(data.nodeType || 'default');
  const nodeIcon = getNodeIcon(data.nodeType || 'default');

  // Delete node handler that stops propagation
  const onDeleteNode = useCallback(
    (event: React.MouseEvent) => {
      // Ensure the event doesn't propagate to parent elements
      event.stopPropagation();
      event.preventDefault();

      console.log('Delete node clicked for node:', id);

      if (onDelete) {
        // Use the parent component's delete function if provided
        console.log('Using parent onDelete function');
        onDelete(id);
      } else {
        // Fallback to ReactFlow's deleteElements
        console.log('Using ReactFlow deleteElements');
        deleteElements({ nodes: [{ id }] });
      }
    },
    [id, deleteElements, onDelete]
  );

  // We don't need to manually manage the delete button visibility anymore
  // as we're using CSS to show/hide it based on hover and selected state

  // Create a single handle for each side of the node
  const createHandles = () => {
    // Define handle styles
    const handleStyle = {
      zIndex: 100,
      border: '1px solid white',
      width: '6px',
      height: '6px',
      boxShadow: '0 0 1px rgba(0, 0, 0, 0.2)',
      opacity: 0.8,
      transform: 'translate(-50%, -50%)' // Ensure perfect centering
    };

    return {
      // Left handle (input)
      left: (
        <Handle
          id="input-0"
          type="target"
          position={Position.Left}
          className="node-handle left-handle target-handle"
          isConnectable={isConnectable}
          style={{
            ...handleStyle,
            left: -8,
            top: '50%',
            transform: 'translateY(-50%)',
            backgroundColor: 'rgba(233, 30, 99, 0.8)'
          }}
        />
      ),
      // Right handle (output)
      right: (
        <Handle
          id="output-0"
          type="source"
          position={Position.Right}
          className="node-handle right-handle source-handle"
          isConnectable={isConnectable}
          style={{
            ...handleStyle,
            right: -8,
            top: '50%',
            transform: 'translateY(-50%)',
            backgroundColor: 'rgba(76, 175, 80, 0.8)'
          }}
        />
      ),
      // Top handle (input)
      top: (
        <Handle
          id="input-1"
          type="target"
          position={Position.Top}
          className="node-handle top-handle target-handle"
          isConnectable={isConnectable}
          style={{
            ...handleStyle,
            top: -8,
            left: '50%',
            transform: 'translateX(-50%)',
            backgroundColor: 'rgba(233, 30, 99, 0.8)'
          }}
        />
      ),
      // Bottom handle (output)
      bottom: (
        <Handle
          id="output-1"
          type="source"
          position={Position.Bottom}
          className="node-handle bottom-handle source-handle"
          isConnectable={isConnectable}
          style={{
            ...handleStyle,
            bottom: -8,
            left: '50%',
            transform: 'translateX(-50%)',
            backgroundColor: 'rgba(76, 175, 80, 0.8)'
          }}
        />
      )
    };
  };

  // Get all handles
  const handles = createHandles();

  return (
    <div
      ref={nodeRef}
      className={`custom-node ${selected ? 'selected' : ''} ${isStartNode ? 'start-node' : ''} ${isEndNode ? 'end-node' : ''}`}
      style={{ borderColor: nodeColor }}
    >
      {/* Delete button positioned absolutely */}
      <div
        className="node-delete-btn"
        onClick={(e) => {
          e.stopPropagation();
          e.preventDefault();
          onDeleteNode(e);
        }}
        title="Delete node"
        role="button"
        aria-label="Delete node"
      >
        ×
      </div>
      {/* Top handle - centered connector */}
      <div className="connector-wrapper top-connector">
        {handles.top}
      </div>

      {/* Left side - input handle */}
      <div className="connector-wrapper left-connector">
        {handles.left}
      </div>

      {/* Right side - output handle */}
      <div className="connector-wrapper right-connector">
        {handles.right}
      </div>

      <div className="node-content">
        <div className="node-header" style={{ backgroundColor: nodeColor }}>
          <span className="node-icon">{nodeIcon}</span>
          <span className="node-id">{id}</span>
        </div>

        <div className="node-body">
          <div className="node-label">{data.label}</div>
          <div className="node-type">{data.nodeType}</div>

          {/* Role markers for start/end nodes */}
          {isStartNode && <div className="node-role start">START</div>}
          {isEndNode && <div className="node-role end">END</div>}
        </div>
      </div>

      {/* Bottom handle - centered connector */}
      <div className="connector-wrapper bottom-connector">
        {handles.bottom}
      </div>
    </div>
  );
};

export default memo(CustomNode);
