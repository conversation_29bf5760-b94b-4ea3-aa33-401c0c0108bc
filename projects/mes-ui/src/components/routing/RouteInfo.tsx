import React from 'react';
import './RouteInfo.scss';

interface RouteInfoProps {
  configName: string;
  configCode: string;
  setConfigName: (name: string) => void;
  setConfigCode: (code: string) => void;
  nodeOptions: { value: string; label: string }[];
  startNodeId: string;
  endNodeId: string;
  setStartNodeId: (id: string) => void;
  setEndNodeId: (id: string) => void;
}

/**
 * RouteInfo component for displaying and editing route information
 */
const RouteInfo: React.FC<RouteInfoProps> = ({
  configName,
  configCode,
  setConfigName,
  setConfigCode,
  nodeOptions,
  startNodeId,
  endNodeId,
  setStartNodeId,
  setEndNodeId
}) => {
  return (
    <div className="route-info">
      <h3>Route Configuration</h3>
      
      <div className="control-item">
        <label htmlFor="routeName">Route Name:</label>
        <input
          id="routeName"
          type="text"
          value={configName}
          onChange={(e) => setConfigName(e.target.value)}
          placeholder="Enter route name"
          className="control-input"
        />
      </div>
      
      <div className="control-item">
        <label htmlFor="routeCode">Route Code:</label>
        <input
          id="routeCode"
          type="text"
          value={configCode}
          onChange={(e) => setConfigCode(e.target.value)}
          placeholder="Enter route code"
          className="control-input"
        />
      </div>
      
      <div className="control-item">
        <label htmlFor="startNode">Start Node:</label>
        <select 
          id="startNode" 
          value={startNodeId} 
          onChange={(e) => setStartNodeId(e.target.value)}
        >
          <option value="">-- Select Start --</option>
          {nodeOptions.map(opt => (
            <option key={opt.value} value={opt.value}>
              {opt.label}
            </option>
          ))}
        </select>
      </div>
      
      <div className="control-item">
        <label htmlFor="endNode">End Node:</label>
        <select 
          id="endNode" 
          value={endNodeId} 
          onChange={(e) => setEndNodeId(e.target.value)}
        >
          <option value="">-- Select End --</option>
          {nodeOptions.map(opt => (
            <option key={opt.value} value={opt.value}>
              {opt.label}
            </option>
          ))}
        </select>
      </div>
    </div>
  );
};

export default RouteInfo;
