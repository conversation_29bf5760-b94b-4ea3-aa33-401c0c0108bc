/* Routing Config Editor Styles */
/* Use variables directly */
$primary-color: #2196f3;
$secondary-color: #ff9800;
$rework-color: #e91e63;
$light-bg: #f8f9fa;
$dark-bg: #2d3748;
$light-text: #f8f9fa;
$dark-text: #2d3748;
$sidebar-width: 240px;
$collapsed-width: 45px;
$border-radius: 4px;
$shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
$shadow-dark: 0 2px 10px rgba(0, 0, 0, 0.3);
$transition: all 0.3s ease;

/* Edge specific variables */
$hover-width: 5px;
$selected-width: 4px;
$default-width: 3px;
$delete-red: #f44336;
$delete-red-hover: #d32f2f;

/* Main Container */
.app-container {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  height: 100vh;
  overflow: auto;
  /* Changed from hidden to auto to enable scrolling */
  position: relative;
  background-color: $light-bg;
  transition: $transition;

  &.dark-mode {
    background-color: $dark-bg;
    color: $light-text;
  }

  /* Routing Header takes full width at the top */
  .routing-header {
    width: 100%;
    flex: 0 0 100%;
    order: 0;
  }

  /* Left sidebar comes next */
  .left-sidebar {
    order: 1;
  }

  /* Canvas in the middle */
  .canvas-container {
    order: 2;
  }

  /* Right sidebar last */
  .right-sidebar {
    order: 3;
  }
}

/* Z-index values for proper stacking */
$z-base: 1;
$z-canvas: 5;
$z-sidebars: 10;
$z-controls: 15;
$z-react-flow-controls: 20;
$z-nodes: 30;
$z-selected-nodes: 35;
$z-edges: 40;
$z-selected-edges: 45;
$z-edge-controls: 50;
$z-tooltips: 60;
$z-modals: 100;

/* Animation for connection lines */
@keyframes dashdraw {
  0% {
    stroke-dashoffset: 10;
  }

  100% {
    stroke-dashoffset: 0;
  }
}

/* Custom Controls */
.custom-controls-panel {
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: $border-radius;
  padding: 10px;
  box-shadow: $shadow-light;

  .control-buttons {
    display: flex;
    flex-direction: column;
    gap: 8px;

    button {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      background-color: white;
      border: 1px solid #ddd;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 18px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      transition: all 0.2s ease;

      &:hover {
        transform: scale(1.1);
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
      }

      &:active {
        transform: scale(0.95);
      }

      span {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .dark-mode & {
    background-color: rgba($dark-bg, 0.8);
    box-shadow: $shadow-dark;

    .control-buttons button {
      background-color: #3a4556;
      border-color: #4a5568;
      color: white;
    }
  }
}

/* Sidebar Styles */
.sidebar {
  height: 100%;
  background-color: white;
  transition: $transition;
  z-index: $z-sidebars;
  box-shadow: $shadow-light;
  display: flex;
  flex-direction: column;
  position: relative;
  border-radius: 0;
  overflow-y: auto;
  overflow-x: hidden;
  /* Ensure vertical scrolling works properly */

  &.left-sidebar {
    width: 280px;
    border-right: 1px solid #e2e8f0;
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;

    &.collapsed {
      width: $collapsed-width;
      overflow: hidden;
      /* Keep collapsed sidebar from scrolling */
    }
  }

  &.right-sidebar {
    width: 280px;
    border-left: 1px solid #e2e8f0;
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;

    &.collapsed {
      width: $collapsed-width;
      overflow: hidden;
      /* Keep collapsed sidebar from scrolling */
    }
  }

  .dark-mode & {
    background-color: #1a202c;
    /* Replaced darken($dark-bg, 3%) */
    border-color: #444;
    box-shadow: $shadow-dark;
  }
}

/* Sidebar Toggle Button */
.sidebar-toggle {
  position: absolute;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  z-index: 11;
  font-weight: bold;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: $transition;
  font-size: 14px;
  color: #64748b;

  &:hover {
    background-color: #f8fafc;
    transform: scale(1.05);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: scale(0.95);
  }

  &.left-toggle {
    right: -12px;
    top: 20px;
  }

  &.right-toggle {
    left: -12px;
    top: 20px;
  }

  .dark-mode & {
    background-color: #3a4556;
    color: $light-text;
    border-color: #4a5568;

    &:hover {
      background-color: #4e5a6c;
      /* Replaced lighten(#3a4556, 10%) */
    }
  }
}

/* Canvas Container */
.canvas-container {
  flex-grow: 1;
  position: relative;
  transition: $transition;

  &.left-expanded {
    margin-left: -($sidebar-width - $collapsed-width);
  }

  &.right-expanded {
    margin-right: -($sidebar-width - $collapsed-width);
  }
}

/* Node Palette */
.node-palette {
  padding: 16px;
  height: 100%;
  overflow-y: auto;

  h3 {
    margin-top: 0;
    color: #444;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 10px;
    margin-bottom: 16px;

    .dark-mode & {
      color: $light-text;
      border-color: #444;
    }
  }

  .help-text {
    background-color: #f8f9fa;
    border-left: 4px solid #2196f3;
    padding: 10px;
    margin-bottom: 16px;
    border-radius: 4px;
    font-size: 13px;
    line-height: 1.4;

    p {
      margin: 0 0 8px 0;
    }

    ul {
      margin: 0;
      padding-left: 20px;

      li {
        margin-bottom: 4px;
      }
    }

    strong {
      color: #2196f3;
    }

    .dark-mode & {
      background-color: #2a3441;
      border-left-color: #64b5f6;

      strong {
        color: #64b5f6;
      }
    }
  }

  .node-category {
    margin-bottom: 20px;

    h4 {
      margin-top: 0;
      margin-bottom: 10px;
      font-size: 14px;
      color: #666;

      .dark-mode & {
        color: #aaa;
      }
    }
  }

  .node-item {
    background-color: white;
    border: 2px solid #eee;
    padding: 10px;
    margin-bottom: 8px;
    border-radius: $border-radius;
    cursor: grab;
    transition: $transition;
    display: flex;
    align-items: center;

    &:hover {
      transform: translateY(-2px);
      box-shadow: $shadow-light;
      border-color: $primary-color;
    }

    .node-icon {
      margin-right: 10px;
      font-size: 18px;
    }

    .node-label {
      font-size: 14px;
      font-weight: 500;
    }

    .dark-mode & {
      background-color: #3a4556;
      /* Replaced lighten($dark-bg, 5%) */
      border-color: #444;

      &:hover {
        box-shadow: $shadow-dark;
      }
    }
  }
}

/* React Flow Overrides */
.react-flow__attribution {
  display: none;
}

.react-flow__controls {
  box-shadow: $shadow-light;
  border-radius: $border-radius;
  overflow: hidden;

  .dark-mode & {
    background-color: #475366;
    /* Replaced lighten($dark-bg, 10%) */
    box-shadow: $shadow-dark;
  }

  button {
    background-color: white;
    border: none;
    border-bottom: 1px solid #eee;

    path {
      fill: #555;
    }

    &:hover {
      background-color: #f0f0f0;
    }

    .dark-mode & {
      background-color: #475366;
      /* Replaced lighten($dark-bg, 10%) */
      border-color: #444;

      path {
        fill: #ddd;
      }

      &:hover {
        background-color: #546177;
        /* Replaced lighten($dark-bg, 15%) */
      }
    }
  }
}

/* Action Buttons Container */
.action-buttons-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin: 15px 0;
  padding: 0;

  .button-row {
    display: flex;
    gap: 8px;
    width: 100%;
  }

  .action-button {
    height: 36px;
    padding: 0 16px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-weight: 500;
    font-size: 14px;
    transition: $transition;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    &.generate-button {
      background-color: #0f172a;
      color: white;
      flex: 1;

      &:hover {
        background-color: #1e293b;
        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
      }
    }

    &.save-button {
      background-color: #0ea5e9;
      color: white;
      width: 100%;

      &:hover {
        background-color: #0284c7;
        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
      }

      &:disabled {
        background-color: #94a3b8;
        color: #e2e8f0;
        cursor: not-allowed;
        box-shadow: none;
      }
    }

    &.import-button {
      background-color: #0f172a;
      color: white;
      flex: 1;

      &:hover {
        background-color: #1e293b;
        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
      }

      &:disabled {
        background-color: #94a3b8;
        color: #e2e8f0;
        cursor: not-allowed;
        box-shadow: none;
      }
    }
  }

  .dark-mode & {
    .action-button {
      &:disabled {
        background-color: #475569;
        color: #94a3b8;
      }
    }
  }
}

/* Control Panels */
.utility-panel {
  padding: 5px;

  .dark-mode-toggle {
    background-color: white;
    border: 1px solid #eee;
    border-radius: $border-radius;
    padding: 8px 16px;
    cursor: pointer;
    transition: $transition;

    &:hover {
      background-color: #f0f0f0;
    }

    .dark-mode & {
      background-color: #475366;
      /* Replaced lighten($dark-bg, 10%) */
      color: $light-text;
      border-color: #444;

      &:hover {
        background-color: #546177;
        /* Replaced lighten($dark-bg, 15%) */
      }
    }
  }
}

.config-name-panel {
  display: flex;
  flex-direction: column;
  gap: 8px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: $border-radius;
  padding: 10px;
  box-shadow: $shadow-light;

  input {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px;
    width: 220px;

    &.config-name-input {
      font-weight: 500;
      font-size: 16px;
    }

    &.config-code-input {
      font-size: 14px;
      font-family: monospace;
    }

    &:focus {
      outline: none;
      border-color: $primary-color;
      box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
    }

    .dark-mode & {
      background-color: #3a4556;
      /* Replaced lighten($dark-bg, 5%) */
      color: $light-text;
      border-color: #444;

      &:focus {
        border-color: $primary-color;
      }
    }
  }

  .dark-mode & {
    background-color: rgba($dark-bg, 0.8);
    box-shadow: $shadow-dark;

    input {
      background-color: #3a4556;
      /* Replaced lighten($dark-bg, 5%) */
      color: $light-text;
      border-color: #444;
    }
  }
}

.edge-operations-panel,
.node-operations-panel {
  background-color: white;
  border-radius: $border-radius;
  padding: 10px;
  box-shadow: $shadow-light;
  width: 250px;

  p {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 14px;
  }

  .edit-button,
  .delete-button {
    padding: 8px 12px;
    border-radius: 4px;
    margin-right: 8px;
    cursor: pointer;
    transition: $transition;
    border: none;
  }

  .edit-button {
    background-color: $primary-color;
    color: white;
    margin-bottom: 5px;

    &:hover {
      background-color: #1565c0;
      /* Replaced darken($primary-color, 10%) */
    }
  }

  .delete-button {
    background-color: #f44336;
    color: white;

    &:hover {
      background-color: #d32f2f;
      /* Replaced darken(#f44336, 10%) */
    }
  }

  .dark-mode & {
    background-color: #3a4556;
    /* Replaced lighten($dark-bg, 5%) */
    box-shadow: $shadow-dark;

    p {
      color: $light-text;
    }
  }
}

/* Controls Container */
.controls-container {
  padding: 15px;
  height: calc(100% - 30px);
  /* Adjust height to account for padding */
  overflow-y: auto;
  background-color: rgba(255, 255, 255, 0.95);
  border-left: 1px solid #eee;
  display: flex;
  flex-direction: column;

  .dark-mode & {
    background-color: rgba($dark-bg, 0.95);
    border-left: 1px solid #444;
  }

  /* Help text with handle explanation */
  .help-text {
    margin: 0 0 20px;
    font-size: 0.9rem;
    color: #666;
    background-color: #f5f5f5;
    padding: 10px;
    border-radius: 4px;
    border-left: 3px solid #2196f3;

    p {
      margin: 0 0 8px 0;
    }

    ul {
      margin: 0;
      padding-left: 20px;
    }

    li {
      margin-bottom: 5px;
    }

    /* Handle explanation styles */
    .handle-explanation {
      margin: 5px 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      background-color: rgba(255, 255, 255, 0.7);
      border-radius: 4px;
      padding: 5px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

      .handle-diagram {
        margin-bottom: 10px;

        .node-example {
          position: relative;
          width: 80px;
          height: 50px;
          background: white;
          border: 1px solid #ccc;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 15px auto;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

          .node-box {
            font-weight: bold;
            font-size: 12px;
          }

          .top-dot,
          .bottom-dot,
          .left-dot,
          .right-dot {
            position: absolute;
            font-size: 6px;
            color: #e91e63;
            /* Input color for top/left */
          }

          .right-dot,
          .bottom-dot {
            color: #4caf50;
            /* Output color for right/bottom */
          }

          .top-dot {
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
          }

          .bottom-dot {
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
          }

          .left-dot {
            left: -10px;
            top: 50%;
            transform: translateY(-50%);
          }

          .right-dot {
            right: -10px;
            top: 50%;
            transform: translateY(-50%);
          }
        }
      }

      .handle-list {
        list-style: none;
        padding: 0;
        margin: 0;
        width: 100%;

        li {
          display: flex;
          align-items: center;
          margin-bottom: 5px;
          font-size: 11px;

          .input-dot,
          .output-dot {
            font-size: 6px;
            margin-right: 5px;
          }

          .input-dot {
            color: #e91e63;
          }

          .output-dot {
            color: #4caf50;
          }
        }
      }
    }
  }

  h3 {
    margin-top: 0;
    color: #444;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 10px;
    margin-bottom: 15px;
    font-size: 16px;

    .dark-mode & {
      color: $light-text;
      border-color: #444;
    }
  }

  .control-item {
    margin-bottom: 15px;

    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      font-size: 14px;
      color: #555;

      .dark-mode & {
        color: #ddd;
      }
    }

    select,
    .control-input {
      width: 100%;
      padding: 10px;
      border-radius: 4px;
      border: 1px solid #ddd;
      background-color: white;
      font-size: 13px;
      transition: $transition;

      &:focus {
        outline: none;
        border-color: $primary-color;
        box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
      }

      .dark-mode & {
        background-color: #3a4556;
        /* Replaced lighten($dark-bg, 5%) */
        color: $light-text;
        border-color: #444;
      }
    }
  }

  button {
    width: 100%;
    padding: 10px;
    border-radius: 4px;
    background-color: $primary-color;
    color: white;
    border: none;
    margin: 5px 0;
    cursor: pointer;
    transition: $transition;
    font-size: 13px;
    font-weight: 500;

    &:hover {
      background-color: #1565c0;
      /* Replaced darken($primary-color, 10%) */
    }

    &.import-button {
      background-color: #4caf50;

      &:hover {
        background-color: #388e3c;
        /* Replaced darken(#4caf50, 10%) */
      }
    }
  }

  .control-divider {
    height: 1px;
    background-color: #eee;
    margin: 20px 0;

    .dark-mode & {
      background-color: #444;
    }
  }
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: $border-radius;
  padding: 20px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);

  h2 {
    margin-top: 0;
    margin-bottom: 16px;
    color: #444;
  }

  .modal-description {
    margin-bottom: 16px;
    color: #666;
    font-size: 14px;
  }

  .json-import-textarea,
  .json-export-textarea {
    width: 100%;
    min-height: 200px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: monospace;
    margin-bottom: 16px;
    resize: vertical;

    &:focus {
      outline: none;
      border-color: $primary-color;
      box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
    }
  }

  .modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;

    button {
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      transition: $transition;
      border: none;
    }

    .import-action-button,
    .copy-button {
      background-color: $primary-color;
      color: white;

      &:hover {
        background-color: #1565c0;
        /* Replaced darken($primary-color, 10%) */
      }
    }

    .cancel-button,
    .close-button {
      background-color: #f0f0f0;
      color: #444;

      &:hover {
        background-color: #d6d6d6;
        /* Replaced darken(#f0f0f0, 10%) */
      }
    }
  }

  .dark-mode & {
    background-color: $dark-bg;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.5);

    h2 {
      color: $light-text;
    }

    .modal-description {
      color: #bbb;
    }

    .json-import-textarea,
    .json-export-textarea {
      background-color: #3a4556;
      /* Replaced lighten($dark-bg, 5%) */
      color: $light-text;
      border-color: #444;
    }

    .modal-actions {

      .cancel-button,
      .close-button {
        background-color: #555;
        color: $light-text;

        &:hover {
          background-color: #707070;
          /* Replaced lighten(#555, 10%) */
        }
      }
    }
  }
}

.conditional-fieldset {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 16px;

  legend {
    padding: 0 8px;
    font-weight: 500;
  }

  .dark-mode & {
    border-color: #444;
  }
}