import React from 'react';
import { Panel, useReactFlow } from 'reactflow';
import './Controls.scss';

/**
 * Custom controls component for ReactFlow
 * Provides fit view functionality without zoom buttons
 */
const Controls: React.FC = () => {
  const { fitView } = useReactFlow();

  return (
    <Panel position="bottom-right" className="custom-controls-panel">
      <div className="control-buttons">
        <button
          onClick={() => fitView({ padding: 0.2, duration: 800 })}
          title="Fit View"
          aria-label="Fit view to show all nodes"
          className="fit-view-button"
        >
          <span>🔍</span>
        </button>
      </div>
    </Panel>
  );
};

export default Controls;
