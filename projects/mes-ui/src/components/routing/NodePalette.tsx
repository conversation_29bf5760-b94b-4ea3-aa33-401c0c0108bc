// src/NodePalette.js
import React, { useState, useEffect, useRef, ChangeEvent, DragEvent, useMemo } from 'react';
import './NodePalette.scss';

declare global {
  interface Window {
    __LAST_DRAGGED_ITEM: NodeInfo | null;
  }
}

interface NodeInfo {
  id: string;
  label: string;
  nodeType: string;
}

interface NodePaletteProps {
  availableNodes: NodeInfo[];
}

// Node type icons mapping
const nodeTypeIcons: Record<string, string> = {
  'process': '⚙️',
  'machine': '⚙️',
  'inspection': '🔍',
  'testing': '🧪',
  'rework': '🔄',
  'rework_station': '🔄',
  'load': '⬆️',
  'unload': '⬇️',
  'storage': '📦',
  'default': '📋'
};

// Node categories for grouping
type NodeCategory = {
  name: string;
  types: string[];
  icon: string;
};

const nodeCategories: NodeCategory[] = [
  { name: 'Process', types: ['process', 'machine'], icon: '⚙️' },
  { name: 'Inspection', types: ['inspection'], icon: '🔍' },
  { name: 'Testing', types: ['testing'], icon: '🧪' },
  { name: 'Rework', types: ['rework', 'rework_station'], icon: '🔄' },
  { name: 'Material Handling', types: ['load', 'unload', 'storage'], icon: '📦' },
  { name: 'Other', types: ['default'], icon: '📋' },
];

const NodePalette: React.FC<NodePaletteProps> = ({ availableNodes }) => {
  // Debug log to see what's being passed to the component
  console.log("NodePalette received availableNodes:", availableNodes);

  // State for search functionality
  const [searchQuery, setSearchQuery] = useState('');

  // Initialize filtered nodes with an effect instead of directly in useState
  const [filteredNodes, setFilteredNodes] = useState<NodeInfo[]>([]);

  // Initialize filtered nodes whenever availableNodes changes
  useEffect(() => {
    console.log("Initializing filtered nodes with:", availableNodes);
    setFilteredNodes(availableNodes);
  }, [availableNodes]);

  const searchInputRef = useRef<HTMLInputElement>(null);

  // Group nodes by category
  const categorizedNodes = useMemo(() => {
    console.log("Recalculating categorizedNodes with filteredNodes:", filteredNodes);
    const grouped: Record<string, NodeInfo[]> = {};

    // Initialize categories
    nodeCategories.forEach(category => {
      grouped[category.name] = [];
    });

    // Add nodes to appropriate categories
    filteredNodes.forEach(node => {
      let assigned = false;
      console.log("Processing node for categorization:", node);

      // Find the appropriate category for this node
      for (const category of nodeCategories) {
        if (category.types.some(type => node.nodeType.includes(type))) {
          console.log(`Node ${node.label} assigned to category ${category.name}`);
          grouped[category.name].push(node);
          assigned = true;
          break;
        }
      }

      // If no category matches, put it in Other
      if (!assigned) {
        console.log(`Node ${node.label} assigned to category Other (no match found)`);
        grouped['Other'].push(node);
      }
    });

    console.log("Final categorized nodes:", grouped);
    return grouped;
  }, [filteredNodes]);

  // Filter nodes when search query changes
  useEffect(() => {
    console.log("Search query changed or availableNodes updated:", searchQuery, availableNodes);
    if (!searchQuery.trim()) {
      setFilteredNodes(availableNodes);
    } else {
      const query = searchQuery.toLowerCase();
      const filtered = availableNodes.filter((node) => {
        return (
          node.id.toLowerCase().includes(query) ||
          node.label.toLowerCase().includes(query) ||
          node.nodeType.toLowerCase().includes(query)
        );
      });
      setFilteredNodes(filtered);
    }
  }, [searchQuery, availableNodes]);

  // Handle search input changes
  const handleSearchChange = (e: ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  // Clear search
  const clearSearch = () => {
    setSearchQuery('');
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  };

  // Direct drag handler without relying on react-dnd
  const onDragStart = (event: DragEvent<HTMLDivElement>, nodeData: NodeInfo) => {
    // Set data in multiple formats for maximum compatibility
    try {
      const jsonData = JSON.stringify(nodeData);
      event.dataTransfer.setData('application/json', jsonData);
      event.dataTransfer.setData('text/plain', jsonData);
      event.dataTransfer.setData('nodeId', nodeData.id);
      event.dataTransfer.setData('nodeType', nodeData.nodeType);
      event.dataTransfer.setData('nodeLabel', nodeData.label);

      // Store the node data in window for fallback
      window.__LAST_DRAGGED_ITEM = nodeData;

      // Create a custom drag image to avoid ghosting issues
      const dragImage = document.createElement('div');
      dragImage.innerHTML = `<div style="padding: 5px; background: white; border: 1px solid #ccc; border-radius: 4px; width: 200px;  display: flex; align-items: center; justify-content: center;">${nodeData.label}</div>`;
      document.body.appendChild(dragImage);
      event.dataTransfer.setDragImage(dragImage, 15, 15);
      setTimeout(() => document.body.removeChild(dragImage), 0);

      // Set visual effect
      event.dataTransfer.effectAllowed = 'move';
      console.log("Drag started with node:", nodeData);
    } catch (e) {
      console.error("Error setting drag data:", e);
    }
  };

  // Get icon for a specific node type
  const getNodeTypeIcon = (nodeType: string): string => {
    for (const [type, icon] of Object.entries(nodeTypeIcons)) {
      if (nodeType.toLowerCase().includes(type)) {
        return icon;
      }
    }
    return nodeTypeIcons.default;
  };

  // Transform node label for display (e.g., pb_testing to PB Testing)
  const getDisplayLabel = (label: string) => {
    return label
      .split('_')
      .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Get CSS class for node based on its type
  const getNodeTypeClass = (nodeType: string): string => {
    for (const category of nodeCategories) {
      for (const type of category.types) {
        if (nodeType.toLowerCase().includes(type)) {
          return type;
        }
      }
    }
    return 'default';
  };

  return (
    <div className="node-palette-container">
      {/* Node items - simplified list without categories */}
      <div className="node-items-grid">
        {filteredNodes.map((nodeInfo) => (
          <div
            key={nodeInfo.id}
            className={`node-item ${getNodeTypeClass(nodeInfo.nodeType)}`}
            draggable
            onDragStart={(event) => onDragStart(event, nodeInfo)}
            title={nodeInfo.id}
          >
            <span className="node-label">{getDisplayLabel(nodeInfo.label)}</span>
          </div>
        ))}
      </div>

      {/* Show message when no nodes match search */}
      {filteredNodes.length === 0 && (
        <div className="empty-search-message">
          No matching workstations found. Try a different search term.
        </div>
      )}
    </div>
  );
};

export default NodePalette;
