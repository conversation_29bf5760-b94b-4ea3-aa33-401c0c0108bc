import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useSnackbar } from '../../context/SnackBarContext';

interface ExportModalProps {
  isOpen: boolean;
  exportedJson: string;
  configName: string;
  configCode: string;
  onClose: () => void;
  onViewRoute?: () => void;
}

const ExportModal: React.FC<ExportModalProps> = ({
  isOpen,
  exportedJson,
  configName,
  configCode,
  onClose,
  onViewRoute
}) => {
  const navigate = useNavigate();
  const { showSnackbar } = useSnackbar();

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content json-export-modal">
        <h2>Generated JSON Schema</h2>
        <p className="modal-description">
          Copy the JSON schema below or save it to use in your application.
        </p>
        <textarea
          className="json-export-textarea"
          value={exportedJson}
          readOnly
          rows={10}
        />
        <div className="modal-actions">
          {/* <button
            onClick={onViewRoute || (() => {
              // Navigate to the route viewer with the JSON data
              navigate('/route-viewer', {
                state: {
                  jsonData: exportedJson,
                  configName,
                  configCode
                }
              });
              onClose();
            })}
            className="view-button"
          >
            View Route
          </button> */}
          <button
            onClick={() => {
              navigator.clipboard.writeText(exportedJson);
              onClose()
              showSnackbar("JSON copied to clipboard!", "success");
            }}
            className="copy-button"
          >
            Copy to Clipboard
          </button>
          <button
            onClick={onClose}
            className="close-button"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default ExportModal;
