import React from 'react';
import { useNavigate } from 'react-router-dom';
import ImportModal from './ImportModal';
import ExportModal from './ExportModal';

interface ImportModalComponentProps {
  isOpen: boolean;
  importedJson: string;
  setImportedJson: (json: string) => void;
  onImport: () => void;
  onClose: () => void;
  isLoading?: boolean;
  error?: string | null;
}

export const ImportModalComponent: React.FC<ImportModalComponentProps> = ({
  isOpen,
  importedJson,
  setImportedJson,
  onImport,
  onClose,
  isLoading,
  error
}) => {
  return (
    <ImportModal
      isOpen={isOpen}
      importedJson={importedJson}
      setImportedJson={setImportedJson}
      onImport={onImport}
      onClose={onClose}
      isLoading={isLoading}
      error={error}
    />
  );
};

interface ExportModalComponentProps {
  isOpen: boolean;
  exportedJson: string;
  configName: string;
  configCode: string;
  onClose: () => void;
}

export const ExportModalComponent: React.FC<ExportModalComponentProps> = ({
  isOpen,
  exportedJson,
  configName,
  configCode,
  onClose
}) => {
  const navigate = useNavigate();

  return (
    <ExportModal
      isOpen={isOpen}
      exportedJson={exportedJson}
      configName={configName}
      configCode={configCode}
      onClose={onClose}
      onViewRoute={() => {
        // Navigate to the route viewer with the JSON data
        navigate('/route-viewer', {
          state: {
            jsonData: exportedJson,
            configName,
            configCode
          }
        });
        onClose();
      }}
    />
  );
};
