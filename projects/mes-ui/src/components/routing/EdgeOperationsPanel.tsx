import React from 'react';
import { Panel } from 'reactflow';
import { Edge } from 'reactflow';

interface EdgeOperationsPanelProps {
  selectedEdge: Edge | null;
  onEdit?: () => void;
  onDelete?: () => void;
}

const EdgeOperationsPanel: React.FC<EdgeOperationsPanelProps> = ({
  selectedEdge,
  onEdit,
  onDelete
}) => {
  if (!selectedEdge) return null;

  return (
    <Panel position="top-right" className="edge-operations-panel">
      <div className="edge-operations">
        <p>Selected connection: {selectedEdge.source} → {selectedEdge.target}</p>
        <button onClick={onEdit} className="edit-button">
          Edit Connection
        </button>
        <button onClick={onDelete} className="delete-button">
          Delete Connection
        </button>
      </div>
    </Panel>
  );
};

export default EdgeOperationsPanel;
