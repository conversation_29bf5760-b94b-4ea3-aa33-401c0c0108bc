import React from 'react';
import { Button } from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import './RoutingHeader.scss';

interface RoutingHeaderProps {
  title: string;
  onAddWorkstation?: () => void;
}

const RoutingHeader: React.FC<RoutingHeaderProps> = ({
  title,
  onAddWorkstation
}) => {
  return (
    <div className="routing-header">
      <div className="routing-header-title">
        {title}
      </div>
      {onAddWorkstation && (
        <div className="routing-header-actions">
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={onAddWorkstation}
            size="small"
            sx={{
              backgroundColor: '#000',
              color: '#fff',
              borderRadius: '4px',
              textTransform: 'none',
              fontWeight: 500,
              boxShadow: 'none',
              padding: '8px 12px',
              minWidth: 'auto',
              height: '32px',
              '&:hover': {
                backgroundColor: '#333',
              }
            }}
          >
            Add Workstation
          </Button>
        </div>
      )}
    </div>
  );
};

export default RoutingHeader;
