// src/DraggableNode.js
import React, { useEffect } from 'react';
import { useDrag } from 'react-dnd';

interface NodeInfo {
  id: string;
  label: string;
  nodeType: string;
}

interface DraggableNodeProps {
  nodeInfo: NodeInfo;
}

// Make this accessible to window for drag and drop fallback
window.__LAST_DRAGGED_ITEM = null;

const DraggableNode: React.FC<DraggableNodeProps> = ({ nodeInfo }) => {
  const [{ isDragging }, drag] = useDrag({
    type: 'NODE',
    item: () => {
      // Store the dragged item globally as a fallback
      window.__LAST_DRAGGED_ITEM = nodeInfo;
      console.log('Dragging node:', nodeInfo);
      return nodeInfo;
    },
    end: (item, monitor) => {
      const dropResult = monitor.getDropResult();
      if (dropResult) {
        console.log(`Dropped ${item.label} successfully!`);
      } else {
        console.log('Drop unsuccessful');
      }
      // Clear the global reference after drop
      window.__LAST_DRAGGED_ITEM = null;
    },
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging(),
    }),
  });

  // Transform node label for display (e.g., pb_testing to PB Testing)
  const displayLabel = nodeInfo.label
    .split('_')
    .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');

  // Prepare the drag data
  const handleDragStart = (e: React.DragEvent<HTMLDivElement>) => {
    // Set multiple data formats for maximum compatibility
    try {
      e.dataTransfer.setData('application/json', JSON.stringify(nodeInfo));
      e.dataTransfer.setData('text', JSON.stringify(nodeInfo));
      e.dataTransfer.effectAllowed = 'move';
      console.log('Native drag start with data:', nodeInfo);
    } catch (err) {
      console.error('Error setting drag data:', err);
    }
  };

  return (
    <div
      ref={drag}
      className="draggable-node"
      style={{ 
        opacity: isDragging ? 0.5 : 1,
        cursor: 'grab',
        padding: '8px',
        border: '1px solid #ddd',
        borderRadius: '4px',
        marginBottom: '8px',
        backgroundColor: '#f8f8f8'
      }}
      draggable="true"
      onDragStart={handleDragStart}
    >
      {displayLabel} ({nodeInfo.nodeType})
    </div>
  );
};

export default DraggableNode;
