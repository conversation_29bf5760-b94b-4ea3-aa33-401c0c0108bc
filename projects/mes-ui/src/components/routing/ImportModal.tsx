import React from 'react';

interface ImportModalProps {
  isOpen: boolean;
  importedJson: string;
  setImportedJson: (json: string) => void;
  onImport: () => void;
  onClose: () => void;
  isLoading?: boolean;
  error?: string | null;
}

const ImportModal: React.FC<ImportModalProps> = ({
  isOpen,
  importedJson,
  setImportedJson,
  onImport,
  onClose,
  isLoading = false,
  error = null
}) => {
  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content json-import-modal">
        <h2>Import JSON Schema</h2>
        <p className="modal-description">
          Paste a valid routing schema JSON below to create a visual diagram.
        </p>
        <textarea
          className="json-import-textarea"
          value={importedJson}
          onChange={(e) => setImportedJson(e.target.value)}
          placeholder="Paste JSON here..."
          rows={10}
          disabled={isLoading}
        />
        {error && (
          <div className="error-message" style={{ color: 'red', marginTop: '10px' }}>
            {error}
          </div>
        )}
        <div className="modal-actions">
          <button
            onClick={onImport}
            className="import-action-button"
            disabled={isLoading || !importedJson.trim()}
          >
            {isLoading ? 'Submitting...' : 'Submit'}
          </button>
          <button
            onClick={onClose}
            className="cancel-button"
            disabled={isLoading}
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
};

export default ImportModal;
