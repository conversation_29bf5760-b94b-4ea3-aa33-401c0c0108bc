import React, { useState, useCallback, useRef, useEffect } from 'react';
import { getBezierPath, getStraightPath, getSmoothStepPath, Position, useReactFlow } from 'reactflow';
import './CustomEdge.scss';

interface CustomEdgeProps {
  id: string;
  sourceX: number;
  sourceY: number;
  targetX: number;
  targetY: number;
  sourcePosition: Position;
  targetPosition: Position;
  data?: {
    connectionType?: string;
    routeType?: string;
    edgeType?: string;
    isReadOnly?: boolean;
    connectionId?: string;
    conditionPath?: string;
    conditionValue?: string;
    operator?: string;
    sourceId?: string; // Added for editing connection
    targetId?: string; // Added for editing connection
    sourceHandle?: string; // Added for editing connection
    targetHandle?: string; // Added for editing connection
  };
  selected?: boolean;
  sourceHandle?: string;
  targetHandle?: string;
}

// Edge component with proper SVG marker for arrowhead
const CustomEdge: React.FC<CustomEdgeProps> = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  data = {},
  selected
}) => {
  // State for hover effect and panel visibility
  const [isHovered, setIsHovered] = useState(false);
  const [showTypePanel, setShowTypePanel] = useState(false);

  // Local state for edge type that can be updated immediately
  const [localEdgeType, setLocalEdgeType] = useState(data?.edgeType || 'bezier');

  // Update localEdgeType when data.edgeType changes
  useEffect(() => {
    if (data?.edgeType) {
      setLocalEdgeType(data.edgeType);
      console.log(`Edge ${id} type updated to: ${data.edgeType}`);
    }
  }, [data?.edgeType, id]);

  // Reference to the edge element
  const edgeRef = useRef<SVGGElement>(null);

  // Get React Flow instance for edge operations
  const { deleteElements, setEdges } = useReactFlow();

  // Get styling and configuration based on edge data
  const connectionType = data?.connectionType || 'default';
  const routeType = data?.routeType || 'main';
  const edgeType = localEdgeType; // Use the local state
  const isReadOnly = data?.isReadOnly || false;

  // Set stroke color based on route and connection type using variables from SCSS
  // These should match the variables in _variables.scss
  let stroke = '#2196f3'; // $primary-color - Default blue for main routes

  if (connectionType === 'conditional') {
    stroke = '#ff9800'; // $secondary-color - Orange for conditional connections
  }

  if (routeType === 'rework') {
    stroke = '#e91e63'; // $rework-color - Pink for rework routes
  }

  // Apply consistent edge thickness for all edge types
  const strokeWidth = selected ? 4 : 2; // Consistent width for all edge types
  let strokeDasharray = 'none';

  // Use dash patterns to distinguish different connection types
  if (connectionType === 'conditional') {
    strokeDasharray = '5 5';
  } else if (routeType === 'rework') {
    strokeDasharray = '3 2';
  }

  // Add glow effect for better visibility
  const glowColor = selected ? `rgba(${stroke.replace('#', '').match(/../g)?.map(hex => parseInt(hex, 16)).join(', ')}, 0.5)` : 'transparent';

  // Generate a unique marker ID based on edge ID and color
  // This ensures each edge has its own marker with the right color
  const markerId = `marker-${id}-${stroke.replace('#', '')}`;

  // Get path based on edge type
  const getEdgePath = () => {
    const params = {
      sourceX,
      sourceY,
      sourcePosition,
      targetX,
      targetY,
      targetPosition,
    };

    // Determine the edge type to use
    const effectiveEdgeType = edgeType || 'bezier';
    console.log(`Rendering edge ${id} with type: ${effectiveEdgeType}`);

    // Add the edge type as a class to the parent g element
    // This will be used for styling
    if (edgeRef.current) {
      edgeRef.current.classList.add(`edge-${effectiveEdgeType}`);
    }

    // Ensure perfect central alignment with handles
    // No offsets - all edges will be perfectly centered
    const offsetParams = {
      ...params,
      // No Y-offset to ensure perfect central alignment
      sourceY: params.sourceY,
      targetY: params.targetY
    };

    // Ensure all edge types have consistent rendering
    switch (effectiveEdgeType) {
      case 'straight':
        // Simple straight line
        return getStraightPath(offsetParams)[0];

      case 'step':
        // Use a more pronounced step path with zero border radius
        try {
          const path = getSmoothStepPath({
            ...offsetParams,
            borderRadius: 0
          })[0];
          return path;
        } catch (error) {
          console.error('Error generating step path:', error);
          // Fallback to bezier if step fails
          return getBezierPath(offsetParams)[0];
        }

      case 'smoothstep':
        // Smooth step with rounded corners
        try {
          // Force consistent path style for smoothstep with precise alignment
          // Using a smaller border radius for better precision
          const path = getSmoothStepPath({
            ...offsetParams,
            borderRadius: 8,
            // Ensure the path starts and ends exactly at the connection points
            sourceX: sourceX,
            sourceY: sourceY,
            targetX: targetX,
            targetY: targetY
          })[0];
          return path;
        } catch (error) {
          console.error('Error generating smoothstep path:', error);
          // Fallback to bezier if smoothstep fails
          return getBezierPath(offsetParams)[0];
        }

      case 'cross':
        // Create a custom cross path with a proper cross in the middle
        try {
          // Calculate exact midpoints without offset for perfect alignment
          const midX = (sourceX + targetX) / 2;
          const midY = (sourceY + targetY) / 2;

          // Calculate the cross size based on the distance between points
          const distance = Math.sqrt(Math.pow(targetX - sourceX, 2) + Math.pow(targetY - sourceY, 2));
          const crossSize = Math.min(distance * 0.15, 20); // Limit the cross size

          // Calculate cross points
          const crossX1 = midX - crossSize;
          const crossX2 = midX + crossSize;
          const crossY1 = midY - crossSize;
          const crossY2 = midY + crossSize;

          // Create a path with a proper cross in the middle - perfectly centered
          return `M${sourceX},${sourceY} L${midX},${midY} M${crossX1},${midY} L${crossX2},${midY} M${midX},${crossY1} L${midX},${crossY2} M${midX},${midY} L${targetX},${targetY}`;
        } catch (error) {
          console.error('Error generating cross path:', error);
          // Fallback to bezier if cross fails
          return getBezierPath(offsetParams)[0];
        }

      case 'bezier':
      default:
        // Use a consistent curvature for all bezier paths
        // This ensures perfect alignment and consistency
        const curvature = 0.4; // Fixed curvature for all bezier edges
        return getBezierPath({
          ...offsetParams,
          curvature,
          // Ensure the path starts and ends exactly at the connection points
          sourceX: sourceX,
          sourceY: sourceY,
          targetX: targetX,
          targetY: targetY
        })[0];
    }
  };

  const edgePath = getEdgePath();

  // Edit connection handler
  const onEditConnection = (event: React.MouseEvent) => {
    event.stopPropagation();
    // Open the connection modal for editing
    if (data?.connectionId) {
      // Dispatch an event to open the connection modal
      const customEvent = new CustomEvent('openConnectionModal', {
        detail: {
          connectionId: data.connectionId,
          edgeId: id,
          sourceId: data.sourceId,
          targetId: data.targetId,
          sourceHandle: data.sourceHandle,
          targetHandle: data.targetHandle,
          connectionData: data
        }
      });
      document.dispatchEvent(customEvent);
    }
  };

  // Delete edge handler
  const onDeleteEdge = (event: React.MouseEvent) => {
    event.stopPropagation();
    deleteElements({ edges: [{ id }] });
  };

  // Toggle edge settings panel
  const toggleTypePanel = useCallback((event: React.MouseEvent) => {
    event.stopPropagation();
    setShowTypePanel(prevState => !prevState);
  }, []);

  // Change edge type
  const changeEdgeType = useCallback((type: string) => (event: React.MouseEvent) => {
    event.stopPropagation();

    // Update the edge type in the edges state
    setEdges(edges =>
      edges.map(edge => {
        if (edge.id === id) {
          // Create a new data object with the updated edgeType
          const newData = { ...edge.data, edgeType: type };
          return { ...edge, data: newData };
        }
        return edge;
      })
    );

    // Also update our local state to reflect the change immediately
    setLocalEdgeType(type);

    // Close the panel after selection
    setShowTypePanel(false);
  }, [id, setEdges]);

  return (
    <>
      {/* Define the arrowhead marker */}
      <defs>
        <marker
          id={markerId}
          viewBox="0 0 12 12"
          refX="10"
          refY="6"
          markerWidth="10"
          markerHeight="10"
          orient="auto-start-reverse"
        >
          <path d="M 0 1 L 10 6 L 0 11 z" fill={stroke} stroke={stroke} strokeWidth="0.5" />
        </marker>
      </defs>

      {/* Edge group with hover detection */}
      <g
        ref={edgeRef}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        style={{ zIndex: selected ? 1000 : 100, pointerEvents: 'all' }}
        className={`edge-${edgeType} custom-edge`}
        onClick={(e) => {
          // Double click to show type panel if not in read-only mode
          if (selected && !isReadOnly && e.detail === 2) {
            toggleTypePanel(e);
          }
        }}
      >
        {/* Add a wider invisible path for easier selection */}
        <path
          d={edgePath}
          stroke="transparent"
          strokeWidth={10}
          fill="none"
          style={{ pointerEvents: 'all', cursor: 'pointer' }}
        />
        {/* The edge path with marker-end referencing our defined marker */}
        <path
          className={`custom-edge ${selected ? 'selected' : ''} ${connectionType} ${routeType}`}
          d={edgePath}
          stroke={stroke}
          strokeWidth={strokeWidth} // Consistent width for all edge types
          strokeDasharray={strokeDasharray}
          markerEnd={`url(#${markerId})`}
          fill="none"
          filter={selected ? `drop-shadow(0 0 3px ${stroke})` : 'none'}
          style={{
            pointerEvents: 'all',
            cursor: 'pointer',
            strokeLinecap: 'round',
            strokeLinejoin: 'round',
            filter: selected ? 'drop-shadow(0 0 3px rgba(33, 150, 243, 0.5))' : 'none'
          }}
        />

        {/* Edit button - always rendered but visibility controlled by CSS */}
        {!isReadOnly && (() => {
          // Get the midpoint of the path
          let midPoint = { x: (sourceX + targetX) / 2, y: (sourceY + targetY) / 2 };

          // Only try to use path methods if we have a valid path element
          if (typeof edgePath !== 'string' && edgePath) {
            try {
              // Try to use the SVG path methods if available
              const pathElement = edgePath as unknown as SVGPathElement;
              if (pathElement.getTotalLength) {
                const pathLength = pathElement.getTotalLength();
                if (pathLength > 0 && pathElement.getPointAtLength) {
                  midPoint = pathElement.getPointAtLength(pathLength / 2);
                }
              }
            } catch (e) {
              // Fallback to calculated midpoint if any error occurs
              console.warn('Error calculating path midpoint:', e);
            }
          }

          return (
            <g 
              className={`edge-button-container ${(isHovered || selected) ? 'visible' : ''}`}
              style={{
                transform: `translate(${midPoint.x}px, ${midPoint.y}px)`,
                transformOrigin: 'center center'
              }}
            >
              <circle
                cx={0}
                cy={0}
                r={14}
                fill="white"
                stroke="#2196f3"
                strokeWidth={2}
                onClick={onEditConnection}
                style={{ cursor: 'pointer' }}
                filter="drop-shadow(0 2px 3px rgba(0, 0, 0, 0.3))"
                className="edge-edit-button"
              />
              <text
                x={0}
                y={0}
                textAnchor="middle"
                dominantBaseline="central"
                fill="#2196f3"
                fontSize={14}
                fontWeight="bold"
                pointerEvents="none"
              >
                ✎
              </text>
            </g>
          );
        })()}

        {/* Edge type selector panel - shows when an edge is selected and double-clicked */}
        {showTypePanel && !isReadOnly && (
          <foreignObject
            width={320}
            height={120}
            x={(sourceX + targetX) / 2 - 160}
            y={(sourceY + targetY) / 2 - 70}
            style={{ overflow: 'visible' }}
          >
            <div className="edge-settings-panel">
              <div className="panel-header">
                <h4>Edge Style</h4>
                <button 
                  className="close-button" 
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowTypePanel(false);
                  }}
                >
                  ×
                </button>
              </div>
              <div className="connection-details">
                <span className="connection-label">From: <strong>{data.sourceId}</strong></span>
                <span className="connection-arrow">→</span>
                <span className="connection-label">To: <strong>{data.targetId}</strong></span>
              </div>
              <div className="edge-type-selector">
                <button
                  className={edgeType === 'bezier' ? 'active' : ''}
                  onClick={changeEdgeType('bezier')}
                >
                  <div className="edge-preview-icon bezier"></div>
                  <span>Bezier</span>
                </button>
                <button
                  className={edgeType === 'straight' ? 'active' : ''}
                  onClick={changeEdgeType('straight')}
                >
                  <div className="edge-preview-icon straight"></div>
                  <span>Straight</span>
                </button>
                <button
                  className={edgeType === 'step' ? 'active' : ''}
                  onClick={changeEdgeType('step')}
                >
                  <div className="edge-preview-icon step"></div>
                  <span>Step</span>
                </button>
                <button
                  className={edgeType === 'smoothstep' ? 'active' : ''}
                  onClick={changeEdgeType('smoothstep')}
                >
                  <div className="edge-preview-icon smoothstep"></div>
                  <span>Smooth Step</span>
                </button>
              </div>
              <div className="panel-footer">
                <button 
                  className="delete-edge-button" 
                  onClick={onDeleteEdge}
                >
                  Delete Connection
                </button>
              </div>
            </div>
          </foreignObject>
        )}
      </g>
    </>
  );
};

export default CustomEdge;
