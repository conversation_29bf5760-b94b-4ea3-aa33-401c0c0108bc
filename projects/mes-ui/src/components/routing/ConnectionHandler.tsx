import React from 'react';
import ConnectionModal from './ConnectionModal';
import { Connection, Edge } from 'reactflow';

interface ConnectionHandlerProps {
  modalOpen: boolean;
  currentEdgeParams: Connection | null;
  isEditingEdge: boolean;
  selectedEdge: Edge | null;
  onClose: () => void;
  onSave: (connectionData: {
    connectionType: string;
    routeType: string;
    edgeType: string;
    conditionPath?: string;
    conditionValue?: string;
    operator?: string;
    sourceHandle?: string;
    targetHandle?: string;
    connectionName?: string;
    priority?: number;
  }) => void;
}

const ConnectionHandler: React.FC<ConnectionHandlerProps> = ({
  modalOpen,
  currentEdgeParams,
  isEditingEdge,
  selectedEdge,
  onClose,
  onSave
}) => {
  if (!modalOpen) return null;

  return (
    <ConnectionModal
      isOpen={modalOpen}
      onClose={onClose}
      onSave={onSave}
      sourceNodeId={currentEdgeParams?.source ?? ''}
      targetNodeId={currentEdgeParams?.target ?? ''}
      initialData={isEditingEdge ? selectedEdge?.data : null}
      isEditing={isEditingEdge}
    />
  );
};

export default ConnectionHandler;
