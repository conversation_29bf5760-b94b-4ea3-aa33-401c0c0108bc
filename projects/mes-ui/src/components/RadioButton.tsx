import React from "react";
import {
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  FormHelperText,
} from "@mui/material";
import useScreen from "../hooks/useScreenSize";
import { ResponsiveSize } from "../interfaces/form-config.api.interface";

interface RadioFieldProps {
  label: string;
  name: string;
  value: string;
  options: { value: string; label: string }[];
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  error?: string | false | any;
  alignment?: "left" | "right" | "center";
  width?: ResponsiveSize;
  height?: ResponsiveSize;
  marginRight?: ResponsiveSize;
}

const RadioField: React.FC<RadioFieldProps> = ({
  label,
  name,
  value,
  options,
  onChange,
  error,
  width,
  marginRight,
  alignment,
}) => {
  const {
    device,
    width: deviceWidth,
    marginRight: deviceRightMargin,
  } = useScreen(width, marginRight);
  return (
    <FormControl
      component="fieldset"
      size="small"
      margin="normal"
      error={!!error}
      style={{
        width: deviceWidth || "48%",
        marginRight: deviceRightMargin || "0",
        textAlign: alignment || "left",
        marginBottom: device !== "mobile" ? "18px" : "10px",
      }}
    >
      <FormLabel component="legend" sx={{ color: "rgb(41, 44, 49)" }}>
        {label}
      </FormLabel>
      <RadioGroup name={name} value={value} onChange={onChange} row>
        {options.map((option) => (
          <FormControlLabel
            key={option.value}
            value={option.value}
            control={
              <Radio
                sx={{
                  color: "rgb(41, 44, 49)", // Unchecked color
                  "&.Mui-checked": {
                    color: "rgb(41, 44, 49)", // Checked color
                  },
                }}
              />
            }
            label={option.label}
          />
        ))}
      </RadioGroup>
    </FormControl>
  );
};

export default RadioField;
