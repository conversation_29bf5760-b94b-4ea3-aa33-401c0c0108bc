import React, { useState } from "react";
import {
  Box,
  Modal,
  Button,
  TextField,
  Typography,
  CircularProgress,
  ThemeProvider,
  IconButton,
} from "@mui/material";
import CloseIcon from '@mui/icons-material/Close';
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useSnackbar } from "../context/SnackBarContext";
import { theme } from "../utils/muiTheme";
import { getAccessToken } from "../utils/authUtils";
import { BASE_URL } from '../utils/axiosInstance';
import {
  textFieldStyle,
  multilineTextFieldStyle,
  outlinedButtonStyle,
  containedButtonStyle,
  modalHeaderStyle,
  modalContainerStyle
} from '../utils/commonStyles';

interface AddProductModalProps {
  open: boolean;
  onClose: () => void;
}

const token = getAccessToken();

interface ProductPayload {
  name: string;
  code: string;
  description: string;
  bom_id?: string;
}

const AddProductModal: React.FC<AddProductModalProps> = ({ open, onClose }) => {
  const [name, setName] = useState("");
  const [code, setCode] = useState("");
  const [description, setDescription] = useState("");
  const [productId, setProductId] = useState("");
  const [bomId, setBomId] = useState("");
  const [nameError, setNameError] = useState("");
  const [codeError, setCodeError] = useState("");
  const [productIdError, setProductIdError] = useState("");
  const { showSnackbar } = useSnackbar();
  const queryClient = useQueryClient();

  // Reset form fields and errors

  // Create product API mutation
  const createProduct = useMutation({
    mutationFn: async (payload: ProductPayload) => {
      const response = await fetch(`${BASE_URL}/mes_trace/catalog/api/parts/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        const errorMessage =
          errorData?.detail ||
          `Error: ${response.status} ${response.statusText}`;
        throw new Error(errorMessage);
      }

      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["parts"],
      });
      resetForm();
      onClose();
      showSnackbar("Product created successfully!", "success");
    },
    onError: (error: Error) => {
      showSnackbar("Error creating product: " + error.message, "error");
    },
  });

  // Reset form fields and errors
  const resetForm = () => {
    setName("");
    setCode("");
    setDescription("");
    setProductId("");
    setBomId("");
    setNameError("");
    setCodeError("");
    setProductIdError("");
  };

  // Validation logic for product ID
  const handleProductIdChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value;
    setProductId(value);

    if (!value.trim()) {
      setProductIdError("Product ID is required");
    } else {
      setProductIdError("");
    }
  };

  // Validation logic for name
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value;
    setName(value);

    if (!value.trim()) {
      setNameError("Name is required");
    } else {
      setNameError("");
    }
  };

  // Validation logic for code
  const handleCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value;
    setCode(value);

    if (!value.trim()) {
      setCodeError("Code is required");
    } else {
      setCodeError("");
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Initialize error messages
    let productIdErrorMsg = "";
    let nameErrorMsg = "";
    let codeErrorMsg = "";
    let hasValidationErrors = false;

    // Validate product ID field
    if (!productId.trim()) {
      productIdErrorMsg = "Product ID is required";
      hasValidationErrors = true;
    }

    // Validate name field
    if (!name.trim()) {
      nameErrorMsg = "Name is required";
      hasValidationErrors = true;
    }

    // Validate code field
    if (!code.trim()) {
      codeErrorMsg = "Code is required";
      hasValidationErrors = true;
    }

    // Update error states for each input field
    setProductIdError(productIdErrorMsg);
    setNameError(nameErrorMsg);
    setCodeError(codeErrorMsg);

    // If validation errors exist, do not proceed
    if (hasValidationErrors) {
      return;
    }

    // Submit to API if validation passes
    createProduct.mutate({
      name,
      code,
      description: description?.trim() ?? "",
      bom_id: bomId.trim() || undefined,
    });
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  return (
    <Modal open={open} onClose={handleClose}>
      <ThemeProvider theme={theme}>
        <Box sx={modalContainerStyle}>
          <Box sx={modalHeaderStyle}>
            <Typography variant="h6">
              Add New Product
            </Typography>
            <IconButton onClick={handleClose} aria-label="close">
              <CloseIcon />
            </IconButton>
          </Box>

          <Box component="form" onSubmit={handleSubmit} sx={{ p: 2.5 }}>
            <Box sx={{ display: 'flex', gap: 1.5, mb: 1.5 }}>
              <TextField
                variant="outlined"
                label="Product ID"
                fullWidth
                placeholder="PCB-XXX"
                value={productId}
                onChange={handleProductIdChange}
                error={Boolean(productIdError)}
                helperText={productIdError}
                sx={textFieldStyle}
                size="small"
              />
              <TextField
                variant="outlined"
                label="Product Code"
                fullWidth
                placeholder="Enter product code"
                value={code}
                onChange={handleCodeChange}
                error={Boolean(codeError)}
                helperText={codeError}
                sx={textFieldStyle}
                size="small"
              />
            </Box>

            <TextField
              variant="outlined"
              label="Product Name"
              fullWidth
              placeholder="Enter product name"
              value={name}
              onChange={handleNameChange}
              error={Boolean(nameError)}
              helperText={nameError}
              sx={{ ...textFieldStyle, mb: 1.5 }}
              size="small"
            />

            <TextField
              variant="outlined"
              label="BOM ID"
              fullWidth
              placeholder="BOM-XXX"
              value={bomId}
              onChange={(e) => setBomId(e.target.value)}
              sx={{ ...textFieldStyle, mb: 1.5 }}
              size="small"
            />

            <TextField
              variant="outlined"
              label="Description"
              multiline
              rows={3}
              fullWidth
              placeholder="Enter product description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              sx={{ ...multilineTextFieldStyle, mb: 2.5 }}
              size="small"
            />

            <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
              <Button
                variant="outlined"
                onClick={handleClose}
                sx={outlinedButtonStyle}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="contained"
                disabled={createProduct.isPending}
                sx={containedButtonStyle}
              >
                {createProduct.isPending ? (
                  <CircularProgress size={16} color="inherit" />
                ) : (
                  "Save Product"
                )}
              </Button>
            </Box>
          </Box>
        </Box>
      </ThemeProvider>
    </Modal>
  );
};

export default AddProductModal;
