import React, { useState } from "react";
import { TextField, ThemeProvider } from "@mui/material";
import { LocalizationProvider, DatePicker } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import useScreen from "../hooks/useScreenSize";
import { theme } from "../utils/muiTheme";

interface WorkOrderFormProps {
  workOrderData: Record<string, any>;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  validationErrors: Record<string, string>;
}

const WorkOrderForm: React.FC<WorkOrderFormProps> = ({
  workOrderData,
  onChange,
  validationErrors,
}) => {
  const [date, setDate] = useState<Date | null>(
    workOrderData.order_date ? new Date(workOrderData.order_date) : null
  );

  const handleDateChange = (newDate: Date | null) => {
    setDate(newDate);
    onChange({
      target: {
        name: "order_date",
        value: newDate ? newDate.toISOString().split("T")[0] : "",
      },
    } as any);
  };

  const { device } = useScreen();
  const isMobile = device === "mobile";

  return (
    <ThemeProvider theme={theme}>
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: isMobile ? "10px" : "10px",
        }}
      >
        <TextField
          label="Line"
          name="line"
          value={workOrderData.line}
          onChange={onChange}
          fullWidth
          error={!!validationErrors.line}
          helperText={validationErrors.line}
          size={isMobile ? "small" : "medium"}
        />
        <TextField
          label="Part No"
          name="part_no"
          value={workOrderData.part_no}
          onChange={onChange}
          fullWidth
          error={!!validationErrors.part_no}
          helperText={validationErrors.part_no}
          size={isMobile ? "small" : "medium"}
        />
        <TextField
          label="Customer"
          name="customer"
          value={workOrderData.customer}
          onChange={onChange}
          fullWidth
          error={!!validationErrors.customer}
          helperText={validationErrors.customer}
          size={isMobile ? "small" : "medium"}
        />
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <DatePicker
            label="Order Date"
            value={date}
            onChange={handleDateChange}
            slotProps={{
              textField: {
                fullWidth: true,
                size: isMobile ? "small" : "medium",
                error: !!validationErrors.order_date,
                helperText: validationErrors.order_date,
                InputProps: {
                  disabled: !isMobile,
                  readOnly: true,
                },
                sx: {
                  "& .MuiInputBase-input.Mui-disabled": {
                    opacity: 1, // Keep it fully visible
                    WebkitTextFillColor: "#000 !important", // Force text color to black
                  },
                },
              },
            }}
            format="yyyy-MM-dd"
            sx={{
              "& .MuiInputBase-root": {
                borderRadius: isMobile ? 1 : 2,
              },
            }}
          />
        </LocalizationProvider>
        <TextField
          label="Order No"
          name="order_no"
          value={workOrderData.order_no}
          onChange={onChange}
          fullWidth
          error={!!validationErrors.order_no}
          helperText={validationErrors.order_no}
          size={isMobile ? "small" : "medium"}
        />
        <TextField
          label="CF"
          name="cf"
          value={workOrderData.cf}
          type="number"
          onChange={onChange}
          fullWidth
          error={!!validationErrors.cf}
          helperText={validationErrors.cf}
          size={isMobile ? "small" : "medium"}
        />
        <TextField
          label="Plan"
          name="plan"
          type="number"
          value={workOrderData.plan}
          onChange={onChange}
          fullWidth
          error={!!validationErrors.plan}
          helperText={validationErrors.plan}
          size={isMobile ? "small" : "medium"}
        />
        <TextField
          label="Actual"
          type="number"
          name="actual"
          value={workOrderData.actual}
          onChange={onChange}
          fullWidth
          error={!!validationErrors.actual}
          helperText={validationErrors.actual}
          size={isMobile ? "small" : "medium"}
        />
      </div>
    </ThemeProvider>
  );
};

export default WorkOrderForm;
