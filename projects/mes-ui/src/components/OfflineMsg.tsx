import React from "react";

const OfflineMessage: React.FC = () => {
  const handleRetry = () => {
    window.location.reload();
  };

  return (
    <div
      style={{
        textAlign: "center",
        padding: "20px",
        backgroundColor: "#fff5f5",
        color: "#d32f2f",
        borderRadius: "12px",
        maxWidth: "400px",
        margin: "50px auto",
        boxShadow: "0px 4px 10px rgba(0,0,0,0.1)",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        gap: "15px",
      }}
    >
      <h2 style={{ fontWeight: "bold", fontSize: "1.5rem", margin: "0" }}>
        🔴 No Internet Connection
      </h2>
      <p style={{ margin: "5px 0", fontSize: "1rem", color: "#666" }}>
        Please check your internet connection and try again.
      </p>
    </div>
  );
};

export default OfflineMessage;
