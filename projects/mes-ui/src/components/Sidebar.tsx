import React from "react";
import { NavLink } from "react-router-dom";
import {
  Assessment,
  Assignment,
  Route,
  Settings,
  Description,
  ExitToApp,
  Dashboard,
  Upload,
  Engineering,
  GridView,
  QrCodeScanner
} from "@mui/icons-material";
import styles from "../styles/Sidebar.module.scss";
import { useAuth } from "../context/AuthContext";
import { Typography } from "@mui/material";

interface SideBarProps {
  isDrawerOpen: boolean;
  onLogout: () => void;
  sidebarRef: React.MutableRefObject<HTMLDivElement | null>;
}

const Sidebar = ({
  isDrawerOpen,
  onLogout,
  sidebarRef,
}: SideBarProps) => {
  // Use auth context to get user information
  const { authState } = useAuth();
  const menuItems = [
    {
      name: "Dashboard",
      icon: <Dashboard sx={{ fontSize: '22px' }} />,
      path: "/",
      isVisible: true,
    },
    {
      name: "Work Station",
      icon: <Engineering sx={{ fontSize: '22px' }} />,
      path: "/form-config-list",
      isVisible: true,
    },
    {
      name: "Workflows",
      icon: <Route sx={{ fontSize: '22px' }} />,
      path: "/routings",
      isVisible: true,
    },
    {
      name: "Products",
      icon: <GridView sx={{ fontSize: '24px' }} />,
      path: "/products",
      isVisible: true,
    },
    {
      name: "Serial Tracking",
      icon: <QrCodeScanner sx={{ fontSize: '22px' }} />,
      path: "/product-routes",
      isVisible: true,
    },
    {
      name: "Work Orders",
      icon: <Assignment sx={{ fontSize: '22px' }} />,
      path: "/work-order-table",
      isVisible: true,
    },
    {
      name: "AOI Upload",
      icon: <Upload sx={{ fontSize: '22px' }} />,
      path: "/aoi-sheet-upload",
      isVisible: true,
    },
    {
      name: "Reports",
      icon: <Assessment sx={{ fontSize: '22px' }} />,
      path: "/reports",
      isVisible: true,
    },
    {
      name: "Bill of Materials",
      icon: <Description sx={{ fontSize: '22px' }} />,
      path: "/boms",
      isVisible: true,
    },
    {
      name: "Settings",
      icon: <Settings sx={{ fontSize: '22px' }} />,
      path: "/settings",
      isVisible: true,
    },
  ];

  return (
    <div
      ref={sidebarRef}
      className={`${styles.drawer} ${isDrawerOpen ? styles.drawerOpen : styles.drawerClose
        }`}
    >
      {/* Branding */}
      <div className={styles.branding}>
        <Typography variant="h6" component="div" sx={{ fontWeight: 600, color: '#fff', py: 2 }}>
          Veridian
        </Typography>
      </div>

      {/* Station Information */}
      {/* <div className={styles.stationInfo}>
        <Typography variant="caption" sx={{ color: '#a0aec0', textTransform: 'uppercase', fontWeight: 500 }}>
          Station
        </Typography>
        <Typography variant="subtitle1" sx={{ color: '#fff', fontWeight: 500, mb: 2 }}>
          Pre Wave Station
        </Typography>
      </div> */}

      {/* Menu Items */}
      <ul className={styles.menu}>
        {menuItems.map((item) => (
          <li key={item.name} className={styles.menuItem}>
            {item.isVisible && (
              <NavLink
                to={item.path}
                className={({ isActive }) =>
                  `${styles.link} ${isActive ? styles.active : ""}`
                }
              >
                <span className={styles.icon}>{item.icon}</span>
                {isDrawerOpen && (
                  <span className={styles.name}>{item.name}</span>
                )}
              </NavLink>
            )}
          </li>
        ))}
      </ul>

      {/* User Info */}
      <div className={styles.operatorInfo}>
        <div className={styles.operatorAvatar}>
          {authState.user?.username ? authState.user.username.charAt(0).toUpperCase() : 'U'}
        </div>
        {isDrawerOpen && (
          <div className={styles.operatorDetails}>
            <Typography variant="subtitle2" >
              {authState.user?.username || 'User'}
            </Typography>
            <Typography variant="caption" sx={{ color: '#a0aec0' }}>
              {authState.user?.email || 'No email'}
            </Typography>
          </div>
        )}
        <div className={styles.logoutButton} onClick={onLogout}>
          <ExitToApp sx={{ color: '#a0aec0', fontSize: '22px' }} />
          {isDrawerOpen && (
            <Typography variant="body2" sx={{ color: '#a0aec0', ml: 1 }}>
              Logout
            </Typography>
          )}
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
