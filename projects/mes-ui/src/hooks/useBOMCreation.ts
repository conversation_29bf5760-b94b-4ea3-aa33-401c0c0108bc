import { useMutation, useQueryClient } from '@tanstack/react-query';
import axiosInstance from '../utils/axiosInstance';

// Define the BOM creation payload interface
export interface CreateBOMPayload {
  name: string;
  code: string;
  description: string;
  product: number;
  version: string;
  effective_date: string;
  created_by: number;
}

// Define the BOM response interface
export interface BOMResponse {
  id: number;
  name: string;
  code: string;
  description: string;
  product: number;
  version: string;
  effective_date: string;
  created_by: number;
  created_at: string;
  updated_at: string;
}

// Function to create a new BOM
export const createBOM = async (bomData: CreateBOMPayload): Promise<BOMResponse> => {
  try {
    const response = await axiosInstance.post('/bom/api/headers/', bomData);
    return response.data;
  } catch (error: any) {
    console.error('Error creating BOM:', error);
    throw new Error(`Error creating BOM: ${error.message}`);
  }
};

// Custom hook to use BOM creation mutation
export const useCreateBOM = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (bomData: CreateBOMPayload) => createBOM(bomData),
    onSuccess: () => {
      // Invalidate the BOMs list query to refetch the list with the new BOM
      queryClient.invalidateQueries({ queryKey: ['boms'] });
      // Also invalidate any product-specific BOM queries

    }
  });
};
