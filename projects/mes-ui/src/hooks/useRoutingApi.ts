import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import axiosInstance from '../utils/axiosInstance';

// Types
export interface Part {
  id: number;
  code: string;
  name: string;
  description: string;
  is_active: boolean;
  has_routing?: boolean;
  components: any[];
  past_events: any[];
  last_failed_event: any;
}

export interface RoutingSchema {
  components: Record<string, {
    node_type: string;
    name: string;
    position?: { x: number; y: number };
  }>;
  route: {
    start: string;
    end: string;
    connections: Record<
      string,
      {
        towards?: { default?: string; route_type?: string; conditions?: any[] };
        towards_many?: string[];
      }
    >;
  };
}

export interface Routing {
  id: number;
  name: string;
  code: string;
  schema?: {
    routing_schema: RoutingSchema;
  };
}

// API functions
const fetchParts = async (): Promise<Part[]> => {
  try {
    const response = await axiosInstance.get('/catalog/api/parts/');
    return response.data;
  } catch (error: any) {
    throw new Error(`Error fetching parts: ${error.message}`);
  }
};

const fetchRoutingByProduct = async (productCode: string): Promise<any> => {
  try {
    const response = await axiosInstance.get(`/workflow/api/routings/by-product/${productCode}/`);

    // Log the response for debugging
    console.log(`Routing data for product ${productCode}:`, response.data);

    // Return the data as is - the structure is different from the list endpoint
    return response.data;
  } catch (error: any) {
    console.error('Error fetching routing data:', error);
    throw new Error(`Error fetching routing data: ${error.message}`);
  }
};

const fetchRoutings = async (): Promise<Routing[]> => {
  try {
    // Fetch all routings with a large page size
    const response = await axiosInstance.get('/workflow/api/routings/?page=1&page_size=1000');

    // The API returns a paginated response with a 'results' array
    if (response.data && response.data.results) {
      console.log('Fetched routings:', response.data.results);
      return response.data.results;
    }

    // Fallback in case the structure changes
    console.warn('Unexpected API response structure:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching routings:', error);
    throw new Error(`Error fetching routings: ${error.message}`);
  }
};

const assignRoutingToProduct = async ({
  productId,
  routingId
}: {
  productId: number;
  routingId: number
}): Promise<any> => {
  try {
    // The URL format from the curl command
    const response = await axiosInstance.patch(`/catalog/api/parts/${productId}/`, {
      routing_id: routingId
    });
    return response.data;
  } catch (error: any) {
    console.error('Error assigning routing:', error);
    throw new Error(`Error assigning routing: ${error.message}`);
  }
};

// Hooks
export const useParts = () => {
  return useQuery({
    queryKey: ['parts'],
    queryFn: fetchParts,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useRoutingMutation = () => {
  return useMutation({
    mutationFn: fetchRoutingByProduct,
    onError: (error) => {
      console.error('Error fetching routing by product:', error);
    }
  });
};

export const useRoutings = () => {
  return useQuery({
    queryKey: ['routings'],
    queryFn: fetchRoutings,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useRoutingByProduct = (productCode: string | null) => {
  return useQuery({
    queryKey: ['routing-by-product', productCode],
    queryFn: () => {
      if (!productCode) throw new Error('Product code is required');
      return fetchRoutingByProduct(productCode);
    },
    enabled: !!productCode, // Only run the query if productCode is provided
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useAssignRouting = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (variables: { productId: number; routingId: number }) => {
      // First assign the routing
      const assignResult = await assignRoutingToProduct(variables);

      // Then fetch the updated routing data
      try {
        // Get the product code from the parts cache if available
        const partsCache = queryClient.getQueryData(['parts']) as any[];
        const part = partsCache?.find(p => p.id === variables.productId);
        const productCode = part?.code;

        if (productCode) {
          // Fetch the routing data for the product
          const routingData = await fetchRoutingByProduct(productCode);
          return routingData; // Return the routing data
        }

        return assignResult; // Return the original result if we can't fetch routing data
      } catch (error) {
        console.error('Error fetching routing data after assignment:', error);
        return assignResult; // Return the original result on error
      }
    },
    onSuccess: (_data, variables) => {


      console.log(`Successfully assigned routing ${variables.routingId} to product ${variables.productId}`);
    },
    onError: (error: Error, variables) => {
      console.error(`Failed to assign routing ${variables.routingId} to product ${variables.productId}:`, error);
    }
  });
};
