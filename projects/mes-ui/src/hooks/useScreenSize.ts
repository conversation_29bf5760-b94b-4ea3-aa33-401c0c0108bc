import { useState, useEffect, useMemo } from "react";

export type DeviceType = "mobile" | "tablet" | "desktop";

interface ScreenSize {
  device: DeviceType;
  width: string;
  height: string;
  marginRight: string;
}

// Default styles based on device type
const DEFAULTS = {
  width: { mobile: "100%", tablet: "50%", desktop: "32%" },
  height: { mobile: "40px", tablet: "45px", desktop: "65px" },
  marginRight: { mobile: "0px", tablet: "0px", desktop: "0px" },
};

// Function to determine the device type based on window width
const getDeviceType = (width: number): DeviceType => {
  if (width <= 767) return "mobile"; // Mobile: 0 - 767px
  if (width >= 768 && width <= 1024) return "tablet"; // Tablet: 768px - 1024px
  return "desktop"; // Desktop: 1025px+
};

const useScreen = (
  widthConfig?: Partial<Record<DeviceType, string>>,
  marginRightConfig?: Partial<Record<DeviceType, string>>,
  heightConfig?: Partial<Record<DeviceType, string>>
) => {
  // Initialize state with correct device type
  const [screen, setScreen] = useState<ScreenSize>(() => {
    const device = getDeviceType(window.innerWidth);
    return {
      device,
      width: widthConfig?.[device] || DEFAULTS.width[device],
      height: heightConfig?.[device] || DEFAULTS.height[device],
      marginRight: marginRightConfig?.[device] || DEFAULTS.marginRight[device],
    };
  });

  // Memoized values to avoid unnecessary re-renders
  const memoizedConfig = useMemo(() => {
    const width = window.innerWidth;
    const device = getDeviceType(width);
    return {
      device,
      width: widthConfig?.[device] || DEFAULTS.width[device],
      height: heightConfig?.[device] || DEFAULTS.height[device],
      marginRight: marginRightConfig?.[device] || DEFAULTS.marginRight[device],
    };
  }, [widthConfig, heightConfig, marginRightConfig]);

  useEffect(() => {
    const handleResize = () => {
      const newDevice = getDeviceType(window.innerWidth);
      setScreen((prevScreen) => {
        if (prevScreen.device === newDevice) return prevScreen; // Avoid unnecessary re-renders
        return {
          device: newDevice,
          width: widthConfig?.[newDevice] || DEFAULTS.width[newDevice],
          height: heightConfig?.[newDevice] || DEFAULTS.height[newDevice],
          marginRight:
            marginRightConfig?.[newDevice] || DEFAULTS.marginRight[newDevice],
        };
      });
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [memoizedConfig]);

  return screen;
};

export default useScreen;
