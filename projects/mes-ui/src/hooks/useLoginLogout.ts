import { useMutation } from "@tanstack/react-query";
import { useAuth } from "../context/AuthContext";
import { useSnackbar } from "../context/SnackBarContext";
import { useNavigate } from "react-router-dom";
import axiosInstance from "../utils/axiosInstance";

const loginApi = async (credentials: {
  username: string;
  password: string;
}) => {
  const response = await axiosInstance.post("/auth/api/login/", credentials, {
    headers: {
      "Content-Type": "application/json",
    },
  });
  return response.data;
};

const logoutApi = async () => {
  const response = await axiosInstance.post(
    "/auth/api/logout/",
    {},
    {
      headers: {
        "Content-Type": "application/json",
      },
    }
  );
  return response.data;
};

export const useLogin = () => {
  const { setAuthData } = useAuth();
  const { showSnackbar } = useSnackbar();
  const navigate = useNavigate();

  return useMutation<any, Error, any>({
    mutationFn: (data: any) => loginApi(data),
    onSuccess: (data: any) => {
      console.log("Form submitted successfully:", data);
      setAuthData(data.access, data.refresh, data.user);
      showSnackbar(`Welcome ! ${data?.user?.username}`, "success");
      navigate("/form-config-list");
      window.location.reload();
    },
    onError: (error: any) => {
      console.error(error);
      showSnackbar(
        `${error} || Error in login. Kindly try again later`,
        "error"
      );
    },
  });
};

export const useLogout = () => {
  const { clearAuthData } = useAuth();
  const navigate = useNavigate();

  const logout = () => {
    clearAuthData();
    navigate("/login");
  };

  return useMutation<any, Error, any>({
    mutationFn: () => logoutApi(),
    onSuccess: () => {
      logout();
    },
    onError: (error: any) => {
      console.error("Error submitting form:", error);
    },
  });
};
