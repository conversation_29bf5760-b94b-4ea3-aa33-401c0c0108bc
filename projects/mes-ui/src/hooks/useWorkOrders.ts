import { useQuery, useMutation } from "@tanstack/react-query";
import axiosInstance from "../utils/axiosInstance";

interface WorkOrder {
  line: string;
  part_no: string;
  customer: string;
  order_date: string;
  order_no: string;
  cf: number;
  plan: number;
  actual: number;
}

interface WorkOrderFilter {
  line?: string;
  order_date?: string;
  order_no?: string;
  part_no?: string;
  page?: number;
}

const fetchWorkOrders = async (filter: WorkOrderFilter = {}): Promise<any> => {
  const params: { [key: string]: any } = {};

  // Add parameters only if they are defined in the filter

  if (filter.order_date) params.ordering = filter.order_date;
  if (filter.line) params.line = filter.line;
  if (filter.part_no) params.part_no = filter.part_no;
  if (filter.order_no) params.order_no = filter.order_no;
  if (filter.page) params.page = filter.page;

  const response = await axiosInstance.get("/operation/api/work-orders/", {
    params,
  });
  return response.data;
};

export const useWorkOrders = (filter: WorkOrderFilter = {}) => {
  return useQuery({
    queryKey: ["workOrders", filter],
    queryFn: () => fetchWorkOrders(filter),
  });
};

export const createWorkOrder = async (workOrderData: WorkOrder) => {
  const response = await axiosInstance.post(
    "/operation/api/work-orders/",
    workOrderData,
    {}
  );
  return response.data;
};

export const useCreateWorkOrder = () => {
  return useMutation<any, Error, WorkOrder>({
    mutationFn: createWorkOrder,
    onSuccess: (data: any, ca) => {
      console.log("Form submitted successfully:", data);
    },
    onError: (error: any) => {
      console.log("Error submitting form:", error);
    },
  });
};
