import { useMutation } from "@tanstack/react-query";
import { useSnackbar } from "../context/SnackBarContext";
import axiosInstance from "../utils/axiosInstance";

const uploadAoiFile = async (formData: FormData) => {
  const response = await axiosInstance.post(
    "/workflow/api/import-aoi-excel/",
    formData,
    {
      headers: {
        "Content-Type": "",
      },
    }
  );
  return response.data;
};

export const useUploadAoiFile = () => {
  const { showSnackbar } = useSnackbar();
  return useMutation<any, Error, any>({
    mutationFn: (data: FormData) => uploadAoiFile(data),
    onSuccess: (data: any) => {
      showSnackbar(`File uploaded successfully`, "success");
    },
    onError: (error: any) => {
      console.error(error);
      showSnackbar(`Error in File upload. Kindly try again later`, "error");
    },
  });
};
