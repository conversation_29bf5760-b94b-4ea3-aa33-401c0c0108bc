import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import axiosInstance from '../utils/axiosInstance';

// Helper for TanStack Query
const keepPreviousData = <T>(previousData: T | undefined): T | undefined => previousData;

// Define the product interface
export interface ProductItem {
  id: number;
  name: string;
  code: string;
  description?: string;
  created_at: string;
  updated_at: string;
  routing_id?: number;
  routing_details?: {
    id: number;
    name: string;
    code: string;
  };
}

// Define the response interface
export interface ProductResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: ProductItem[];
}

// Define the filter interface
export interface ProductFilter {
  page?: number;
  page_size?: number;
  search?: string;
  name?: string;
  code?: string;
  created_at?: string;
  updated_at?: string;
  has_routing?: boolean;
}

// Function to fetch products with filters
const fetchProducts = async (filters: ProductFilter = {}): Promise<ProductResponse> => {
  try {
    // Build query parameters for API
    const params: Record<string, string | number | boolean> = {};

    // Add pagination parameters
    if (filters.page !== undefined) params.page = filters.page;
    if (filters.page_size !== undefined) params.page_size = filters.page_size;

    // Add filter parameters
    if (filters.search) params.search = filters.search;
    if (filters.name) params.name = filters.name;
    if (filters.code) params.code = filters.code;
    if (filters.created_at) params.created_at = filters.created_at;
    if (filters.updated_at) params.updated_at = filters.updated_at;
    if (filters.has_routing !== undefined) params.has_routing = filters.has_routing;

    // Make the API request
    const response = await axiosInstance.get('/catalog/api/products/', { params });

    // Log the response for debugging
    console.log('Product API response:', response.data);

    // Check if the response has the expected format
    if (!response.data || !Array.isArray(response.data.results)) {
      console.warn('API response does not have the expected format:', response.data);

      // If the response is an array, convert it to the expected format
      if (Array.isArray(response.data)) {
        return {
          count: response.data.length,
          next: null,
          previous: null,
          results: response.data
        };
      }
    }

    return response.data;
  } catch (error: any) {
    console.error('Error fetching products:', error);
    throw new Error(`Error fetching products: ${error.message}`);
  }
};

// Function to fetch a single product by ID
export const fetchProductById = async (id: number): Promise<ProductItem> => {
  try {
    // Make the API request
    const response = await axiosInstance.get(`/catalog/api/parts/${id}/`);
    return response.data;
  } catch (error: any) {
    console.error(`Error fetching product with ID ${id}:`, error);
    throw new Error(`Error fetching product: ${error.message}`);
  }
};

// Function to assign routing to a product
export const assignRoutingToProduct = async (
  productId: number,
  routingId: number
): Promise<{ success: boolean; message: string; product?: ProductItem }> => {
  try {
    console.log(`Assigning routing ${routingId} to product ${productId}`);

    // Make the API request to assign the routing
    const response = await axiosInstance.patch(`/catalog/api/parts/${productId}/`, {
      routing_id: routingId
    });

    console.log('Routing assignment response:', response.data);

    // Fetch the updated product data to ensure we have the latest information
    const updatedProductResponse = await axiosInstance.get(`/catalog/api/parts/${productId}/`);
    const updatedProduct = updatedProductResponse.data;

    console.log('Updated product data:', updatedProduct);

    return {
      success: true,
      message: 'Routing assigned successfully',
      product: updatedProduct
    };
  } catch (error: any) {
    console.error(`Error assigning routing to product ${productId}:`, error);
    throw new Error(`Error assigning routing: ${error.message}`);
  }
};

// Define the product creation interface
export interface CreateProductPayload {
  name: string;
  code: string;
  description?: string;
  routing_id?: number;
}

// Function to create a new product
export const createProduct = async (
  productData: CreateProductPayload
): Promise<ProductItem> => {
  try {
    // Make the API request
    const response = await axiosInstance.post('/catalog/api/parts/', productData);
    return response.data;
  } catch (error: any) {
    console.error('Error creating product:', error);
    throw new Error(`Error creating product: ${error.message}`);
  }
};

// Function to delete a product
export const deleteProduct = async (
  productId: number
): Promise<{ success: boolean; message: string }> => {
  try {
    // Make the API request
    await axiosInstance.delete(`/catalog/api/parts/${productId}/`);

    return {
      success: true,
      message: 'Product deleted successfully'
    };
  } catch (error: any) {
    console.error(`Error deleting product with ID ${productId}:`, error);
    throw new Error(`Error deleting product: ${error.message}`);
  }
};

// Function to update a product
export const updateProduct = async (
  productId: number,
  productData: Partial<CreateProductPayload>
): Promise<ProductItem> => {
  try {
    // Make the API request
    const response = await axiosInstance.patch(`/catalog/api/parts/${productId}/`, productData);
    return response.data;
  } catch (error: any) {
    console.error(`Error updating product with ID ${productId}:`, error);
    throw new Error(`Error updating product: ${error.message}`);
  }
};

// Custom hook to use product table
export const useProductTable = (filters: ProductFilter = {}) => {
  return useQuery({
    queryKey: ['products', filters],
    queryFn: () => fetchProducts(filters),
    placeholderData: keepPreviousData,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Custom hook to fetch a single product
export const useProductById = (id: number | null) => {
  return useQuery({
    queryKey: ['product', id],
    queryFn: () => (id ? fetchProductById(id) : Promise.reject('No product ID provided')),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Custom hook to use assign routing mutation
export const useAssignRouting = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ productId, routingId }: { productId: number; routingId: number }) =>
      assignRoutingToProduct(productId, routingId),
    onSuccess: (result, variables) => {
      console.log('Routing assigned successfully, updating cache');

      if (result.product) {
        // Update the product query cache with the updated product data
        queryClient.setQueryData(['product', variables.productId], result.product);

        // Update the products list query cache
        queryClient.setQueriesData({ queryKey: ['products'] }, (oldData: any) => {
          if (!oldData || !oldData.results) return oldData;

          // Find and update the product in the list
          const updatedResults = oldData.results.map((product: ProductItem) =>
            product.id === variables.productId ? result.product : product
          );

          return {
            ...oldData,
            results: updatedResults
          };
        });
      } else {
        // If we don't have the updated product data, invalidate the queries
        queryClient.invalidateQueries({ queryKey: ['product', variables.productId] });
        queryClient.invalidateQueries({ queryKey: ['products'] });
      }

      // Always invalidate routing queries to ensure fresh data
      queryClient.invalidateQueries({ queryKey: ['routing'] });
      queryClient.invalidateQueries({ queryKey: ['routings'] });
      queryClient.invalidateQueries({ queryKey: ['routing-by-product'] });
    }
  });
};

// Custom hook to use create product mutation
export const useCreateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (productData: CreateProductPayload) => createProduct(productData),
    onSuccess: () => {
      // Invalidate the products list query to refetch the list with the new product
      queryClient.invalidateQueries({ queryKey: ['products'] });
    }
  });
};

// Custom hook to use delete product mutation
export const useDeleteProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (productId: number) => deleteProduct(productId),
    onSuccess: (_, deletedProductId) => {
      // Remove the product from the cache
      queryClient.removeQueries({ queryKey: ['product', deletedProductId] });

      // Update the products list query cache to remove the deleted product
      queryClient.setQueriesData({ queryKey: ['products'] }, (oldData: any) => {
        if (!oldData || !oldData.results) return oldData;

        // Filter out the deleted product
        const updatedResults = oldData.results.filter((product: ProductItem) =>
          product.id !== deletedProductId
        );

        return {
          ...oldData,
          results: updatedResults,
          count: oldData.count - 1
        };
      });
    }
  });
};

// Custom hook to use update product mutation
export const useUpdateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ productId, productData }: { productId: number; productData: Partial<CreateProductPayload> }) =>
      updateProduct(productId, productData),
    onSuccess: (updatedProduct, variables) => {
      // Update the product query cache with the updated data
      queryClient.setQueryData(['product', variables.productId], updatedProduct);

      // Update the products list query cache
      queryClient.setQueriesData({ queryKey: ['products'] }, (oldData: any) => {
        if (!oldData || !oldData.results) return oldData;

        // Find and update the product in the list
        const updatedResults = oldData.results.map((product: ProductItem) =>
          product.id === variables.productId ? updatedProduct : product
        );

        return {
          ...oldData,
          results: updatedResults
        };
      });
    }
  });
};

// Utility functions
export const productUtils = {
  formatDate: (dateString: string) => {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  },

  getRoutingInfo: (routing: ProductItem['routing_details']) => {
    if (!routing) return 'No routing assigned';
    return `${routing.name} (${routing.code})`;
  },
};

