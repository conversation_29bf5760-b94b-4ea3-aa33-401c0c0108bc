import { useQuery } from "@tanstack/react-query";
import axiosInstance from "../utils/axiosInstance";

export interface ProcessBlock {
  id: string | number;
  name: string;
  code: string;
  description?: string;
  is_active?: boolean;
  area?: number;
  area_name?: string;
  line_loc?: number | null;
  line_name?: string | null;
  created_by?: number;
  created_by_name?: string;
  created_at?: string;
  updated_at?: string;
}

export interface ProcessBlocksResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: ProcessBlock[];
}

// Function to fetch process blocks from the API
const fetchProcessBlocks = async (): Promise<ProcessBlock[]> => {
  try {
    const response = await axiosInstance.get("/workflow/api/process-blocks/");
    const data: ProcessBlocksResponse = response.data;
    return data.results;
  } catch (error) {
    console.error("Error fetching process blocks:", error);
    throw error;
  }
};

// Custom hook to use process blocks with React Query
export const useProcessBlocks = (readOnly?: boolean) => {
  return useQuery({
    queryKey: ["process-blocks"],
    queryFn: fetchProcessBlocks,
    enabled: readOnly ? false : true,
    staleTime: 10000, // 10 seconds
    refetchOnWindowFocus: true
  });
};

// Transform process blocks to the format expected by the NodePalette component
export const transformToNodeInfo = (processBlocks: ProcessBlock[]) => {
  return processBlocks.map(block => ({
    id: block.code,
    label: block.name,
    code: block.code,
    nodeType: 'machine',
    description: block.description || '',
    is_active: block.is_active,
    area: block.area,
    area_name: block.area_name
  }));
};
