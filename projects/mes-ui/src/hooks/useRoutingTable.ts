import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import axiosInstance from '../utils/axiosInstance';

// Define the routing interface
export interface RoutingItem {
  id: number;
  name: string;
  code: string;
  schema?: {
    routing_schema: {
      id: string;
      name: string;
      type: string;
      version?: string;
      product_id?: string;
      components: Record<string, any>;
      route: {
        start: string;
        end: string;
        connections: Record<string, any>;
      };
    };
  };
  products_details?: Array<{
    id: number;
    name: string;
    code: string;
  }>;
  created_by?: {
    id: number;
    username: string;
  };
  created_at?: string;
  updated_at?: string;
}

// Define the response interface
export interface RoutingResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: RoutingItem[];
}

// Define the filter interface
export interface RoutingFilter {
  page?: number;
  page_size?: number;
  search?: string;
  name?: string;
  code?: string;
  created_at?: string;
  updated_at?: string;
}

// Function to fetch routings with filters
const fetchRoutings = async (filters: RoutingFilter = {}): Promise<RoutingResponse> => {
  try {
    // Build query parameters for API
    const params: Record<string, string | number> = {};

    // Add pagination parameters
    if (filters.page !== undefined) params.page = filters.page;
    if (filters.page_size !== undefined) params.page_size = filters.page_size;

    // Add filter parameters
    if (filters.search) params.routing_name = filters.search;
    if (filters.name) params.name = filters.name;
    if (filters.code) params.code = filters.code;
    if (filters.created_at) params.created_at = filters.created_at;
    if (filters.updated_at) params.updated_at = filters.updated_at;

    // Make the API request
    const response = await axiosInstance.get('/workflow/api/routings/', { params });

    // Log the response for debugging
    console.log('Routing API response:', response.data);

    return response.data;
  } catch (error: any) {
    console.error('Error fetching routings:', error);
    throw new Error(`Error fetching routings: ${error.message}`);
  }
};

// Custom hook to use routings with filters
export const useRoutingTable = (filters: RoutingFilter = {}) => {
  return useQuery({
    queryKey: ['routings', filters],
    queryFn: () => fetchRoutings(filters),
    placeholderData: (previousData) => previousData, // Keep previous data while fetching new data
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Function to fetch a single routing by ID
export const fetchRoutingById = async (id: number): Promise<RoutingItem> => {
  try {
    // Make the API request
    const response = await axiosInstance.get(`/workflow/api/routings/${id}/`);
    console.log('Routing by ID response:', response.data);
    return response.data;
  } catch (error: any) {
    console.error(`Error fetching routing with ID ${id}:`, error);
    throw new Error(`Error fetching routing: ${error.message}`);
  }
};

// Function to fetch routing by product code
export const fetchRoutingByProductCode = async (productCode: string): Promise<RoutingItem> => {
  try {
    // Make the API request
    const response = await axiosInstance.get(`/workflow/api/routings/by-product/${productCode}/`);
    return response.data;
  } catch (error: any) {
    console.error(`Error fetching routing for product code ${productCode}:`, error);
    throw new Error(`Error fetching routing: ${error.message}`);
  }
};

// Custom hook to use a single routing by ID
export const useRoutingById = (id: number | null, options = {}) => {
  return useQuery({
    queryKey: ['routing', id],
    queryFn: () => {
      if (!id) throw new Error('Routing ID is required');
      return fetchRoutingById(id);
    },
    enabled: !!id, // Only run the query if id is provided
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

// Custom hook to use routing by product code
export const useRoutingByProductCode = (productCode: string | null, options = {}) => {
  return useQuery({
    queryKey: ['routing-by-product', productCode],
    queryFn: () => {
      if (!productCode) throw new Error('Product code is required');
      return fetchRoutingByProductCode(productCode);
    },
    enabled: !!productCode, // Only run the query if productCode is provided
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

// Function to format date strings
export const formatDate = (dateString?: string): string => {
  if (!dateString) return '-';

  try {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  } catch (error) {
    console.error('Error formatting date:', error);
    return dateString;
  }
};

// Function to get product names from products_details
export const getProductNames = (products?: Array<{ name: string }>): string => {
  if (!products || products.length === 0) return '-';

  return products.map(product => product.name).join(', ');
};

// Function to get creator name
export const getCreatorName = (creator?: { username: string }): string => {
  if (!creator) return '-';
  return creator.username;
};

// Function to delete a routing
export const deleteRouting = async (id: number): Promise<{ success: boolean; message: string }> => {
  try {
    // Make the API request
    const response = await axiosInstance.delete(`/workflow/api/routings/${id}/`);
    return {
      success: true,
      message: response.data?.message || 'Routing deleted successfully'
    };
  } catch (error: any) {
    console.error(`Error deleting routing with ID ${id}:`, error);
    throw new Error(`Error deleting routing: ${error.message}`);
  }
};

// Custom hook to use delete routing mutation
export const useDeleteRouting = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteRouting,
    onSuccess: () => {
      // Invalidate the routings query to refetch the list
      queryClient.invalidateQueries({ queryKey: ['routings'] });
    }
  });
};

// Export utility functions
export const routingUtils = {
  formatDate,
  getProductNames,
  getCreatorName,
};
