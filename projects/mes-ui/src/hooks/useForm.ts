import { useMutation, useQuery } from "@tanstack/react-query";
import axiosInstance from "../utils/axiosInstance";

import {
  ApiFormConfig,
  FormListResponse,
} from "../interfaces/form-config.api.interface";

export const fetchFormConfig = async (
  formId?: number
): Promise<ApiFormConfig | FormListResponse | null> => {
  const url = formId
    ? `/workflow/api/forms/${formId}/`
    : `/workflow/api/forms/`;
  const response = await axiosInstance.get(url);
  return response.data;
};

export const fetchRouteConfig = async (id?: number) => {
  const url = id ? `/workflow/api/routings/${id}/` : `/workflow/api/routings/`;
  const response = await axiosInstance.get(url);
  return response.data;
};

export const fetchProductBySerialNumber = async (serialNumber: string) => {
  const params: { [key: string]: any } = {};

  // Add the serial_number query param only if it's provided
  if (serialNumber) {
    params.serial_number = serialNumber;
  }

  const response = await axiosInstance.get(
    "/catalog/api/parts/by_serial_number/",
    {
      params,
    }
  );

  return response.data;
};

export const fetchReferenceCategories = async () => {
  const url = `/workflow/api/reference-categories/?is_active=1`;
  try {
    const response = await axiosInstance.get(url);
    return response.data;
  } catch (e) {
    console.log(e);
    // return null;
  }
};

interface EventItem {
  id: number;
  timestamp: string;
  event_data: Record<string, any>;
  form_details: {
    name: string;
    code: string;
    description: string;
  };
  created_by: {
    username: string;
  };
  serial_number: string;
  work_order: string;
  validation_status: string;
}

export const fetchFormEvents = async (
  formCode?: string
): Promise<EventItem[]> => {
  const params: { [key: string]: any } = {};

  if (formCode) {
    params.form_code = formCode;
  }

  const response = await axiosInstance.get("/operation/api/events/", {
    params,
  });

  return response?.data?.results?.data || [];
};

export const fetchRoutingBySerialNumber = async (serialNumber: string) => {
  const response = await axiosInstance.get(
    `/workflow/api/routings/by-serial/${encodeURIComponent(serialNumber)}/`
  );

  return response?.data;
};

const submitFormData = async (formData: any): Promise<any> => {
  const response = await axiosInstance.post(`/operation/api/events/`, formData);
  return response.data;
};

export const useFormConfig = (enabled: boolean, formId?: number) => {
  return useQuery({
    queryKey: ["formConfig", formId],
    queryFn: () => fetchFormConfig(formId),
    enabled: enabled,
    retry: 0,
    retryDelay: (attempt) => Math.min(1000 * 2 ** attempt, 30000),
  });
};

export const useRouteConfig = (id?: number) => {
  return useQuery({
    queryKey: ["routeConfig", id],
    queryFn: () => fetchRouteConfig(id),
    retry: 0,
    retryDelay: (attempt) => Math.min(1000 * 2 ** attempt, 30000),
  });
};

export const useSubmitForm = () => {
  return useMutation<any, Error, any>({
    mutationFn: (formData: any) => submitFormData(formData),
    onSuccess: (data: any) => {
      console.log("Form submitted successfully:", data);
    },
    onError: (error: any) => {
      console.log("Error submitting form:", error);
    },
  });
};

export const useProductBySerialNumber = (serialNumber: string) => {
  return useQuery({
    queryKey: ["products", serialNumber],
    queryFn: () => fetchProductBySerialNumber(serialNumber),
    retry: 0,
    retryDelay: (attempt) => Math.min(1000 * 2 ** attempt, 30000),
  });
};

export const useFetchFormEvents = (formCode?: string) => {
  return useQuery({
    queryKey: ["events", formCode],
    queryFn: () => fetchFormEvents(formCode),
    retry: 0,
    enabled: !!formCode,
    retryDelay: (attempt) => Math.min(1000 * 2 ** attempt, 30000),
  });
};

export const useRoutinBySerialNumber = (serialNumber: string, isEnabled: boolean) => {
  return useQuery({
    queryKey: ["serial_number", serialNumber],
    enabled: !!serialNumber && isEnabled,
    queryFn: () => fetchRoutingBySerialNumber(serialNumber),
    retry: 0,
  });
};
