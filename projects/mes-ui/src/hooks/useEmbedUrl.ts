import { useQuery } from "@tanstack/react-query";
import axiosInstance from "../utils/axiosInstance";

export const fetchEmbedUrl = async () => {
  const response = await axiosInstance.get("/analytics/api/reports/embed_url/");

  return response.data;
};

export const useEmbedUrl = () => {
  return useQuery({
    queryKey: ["embedUrl"],
    enabled: true,
    queryFn: () => fetchEmbedUrl(),
    retry: 0,
    retryDelay: (attempt) => Math.min(1000 * 2 ** attempt, 30000),
  });
};
