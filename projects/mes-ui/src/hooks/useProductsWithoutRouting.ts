import { useQuery } from "@tanstack/react-query";
import axiosInstance from "../utils/axiosInstance";

export interface Product {
  id: number;
  code: string;
  name: string;
  description?: string;
  is_active: boolean;
}

// Function to fetch products without routing
const fetchProductsWithoutRouting = async (): Promise<Product[]> => {
  try {
    // Fetch all products
    const productsResponse = await axiosInstance.get("/catalog/api/parts/");
    const products = productsResponse.data;

    // Fetch products with routing
    const productsWithRoutingResponse = await axiosInstance.get("/workflow/api/product-routings/");
    const productsWithRouting = productsWithRoutingResponse.data.results || [];
    
    // Extract product codes with routing
    const productCodesWithRouting = productsWithRouting.map((item: any) => item.product_code);
    
    // Filter products without routing
    const productsWithoutRouting = products.filter(
      (product: Product) => !productCodesWithRouting.includes(product.code)
    );
    
    return productsWithoutRouting;
  } catch (error) {
    console.error("Error fetching products without routing:", error);
    throw error;
  }
};

// Custom hook to use products without routing
export const useProductsWithoutRouting = () => {
  return useQuery({
    queryKey: ["productsWithoutRouting"],
    queryFn: fetchProductsWithoutRouting,
  });
};
