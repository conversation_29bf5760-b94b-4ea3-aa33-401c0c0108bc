import { useQuery } from "@tanstack/react-query";
import axios from "../utils/axiosInstance";

interface ChartResponse {
  charts: any[];
  layout: {
    columns: number;
    gap: string;
    items: {
      chartId: number;
      row: string;
      column: string;
      height: string;
    }[];
  };
} // Replace 'any' with proper echarts option type

const sampleLayout = {
  columns: 12,
  gap: "16px",
  items: [
    {
      chartId: 35,
      row: "1 span 2",
      column: "1 / span 3",
      height: "250px",
    },
    {
      // chartId: 36,
      chartId: 35,
      row: "1 span 2",
      column: "4 / span 3",
      height: "250px",
    },
    {
      // chartId: 38,
      chartId: 35,
      row: "1 span 2",
      column: "7 / span 3",
      height: "250px",
    },
    {
      // chartId: 37,
      chartId: 35,
      row: "1 span 2",
      column: "10 / span 3",
      height: "250px",
    },
    {
      chartId: 41,
      row: "2 / span 1",
      column: "1 / span 6",
      height: "400px",
    },
    {
      chartId: 43,
      row: "2 / span 1",
      column: "7 / span 6",
      height: "400px",
    },
    {
      chartId: 42,
      row: "3 / span 1",
      column: "1 / span 5",
      height: "400px",
    },
    {
      chartId: 40,
      row: "3 / span 1",
      column: "6 / span 7",
      height: "400px",
    },
    // {
    //   chartId: 39,
    //   row: "1 / span 2",
    //   column: "1 / span 6",
    //   height: "400px",
    // },
    // {
    //   chartId: 45,
    //   row: "1 / span 2",
    //   column: "7 / span 6",
    //   height: "400px",
    // },
    // {
    //   chartId: 44,
    //   row: "1 / span 2",
    //   column: "1 / span 6",
    //   height: "400px",
    // },
  ],
};
const fetchDashboardData = async (
  dashboardId?: number
): Promise<ChartResponse | null> => {
  if (!dashboardId) {
    return null;
  }
  try {
    const response = await axios.get(
      `/analytics/api/dashboards/${dashboardId}`
    );
    console.log(`Dashboard ${dashboardId} response:`, response.data);
    response.data.layout = sampleLayout;
    return response.data;
  } catch (error) {
    console.error(`Error fetching dashboard ${dashboardId} data:`, error);
    throw error;
  }
};

const fetchChartData = async (
  chartId: number,
  filters: Record<string, any>
): Promise<any> => {
  try {
    const response = await axios.get(`/analytics/api/charts/${chartId}/data`, {
      params: filters,
    });
    console.log(`Chart ${chartId} response:`, response.data);
    return response.data;
  } catch (error) {
    console.error(`Error fetching chart ${chartId} data:`, error);
    throw error;
  }
};

const fetchTabsData = async (): Promise<any> => {
  try {
    const response = await axios.get("/analytics/api/dashboards");
    console.log("Tabs response:", response.data);
    return response.data?.results;
  } catch (error) {
    console.error("Error fetching tabs data:", error);
    throw error;
  }
};

export const useTabsData = () => {
  return useQuery({
    queryKey: ["tabs"],
    queryFn: () => fetchTabsData(),
    staleTime: 5 * 60 * 1000, // Data is considered fresh for 5 minutes
  });
};

export const useChartData = (chartId: number, filters: Record<string, any>) => {
  return useQuery({
    queryKey: ["chart", chartId, filters],
    queryFn: () => fetchChartData(chartId, filters),
    staleTime: 5 * 60 * 1000, // Data is considered fresh for 5 minutes
  });
};

export const useDashboardData = (dashboardId: number) => {
  return useQuery({
    queryKey: ["dashboard", dashboardId],
    queryFn: () => fetchDashboardData(dashboardId),
    staleTime: 5 * 60 * 1000, // Data is considered fresh for 5 minutes
  });
};
