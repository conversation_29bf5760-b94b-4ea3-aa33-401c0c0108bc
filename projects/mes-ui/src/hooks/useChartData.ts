import { useQuery } from "@tanstack/react-query";
import axios from "../utils/axiosInstance";

interface LayoutItem {
  id: number;
  col?: number;
  row?: number;
  colspan?: number;
  rowspan?: number;
  chartgroup?: number;
}

interface Layout {
  desktopColumns: number;
  mobileColumns: number;
  mobile: LayoutItem[];
  desktop: LayoutItem[];
}

interface ChartResponse {
  charts: any[];
  layout: Layout;
}

const fetchDashboardData = async (
  dashboardId?: number
): Promise<ChartResponse | null> => {
  if (!dashboardId) {
    return null;
  }
  try {
    const response = await axios.get(
      `/analytics/api/dashboards/${dashboardId}`
    );
    console.log(`Dashboard ${dashboardId} response:`, response.data);
    return response.data;
  } catch (error) {
    console.error(`Error fetching dashboard ${dashboardId} data:`, error);
    throw error;
  }
};

const fetchChartData = async (
  chartId: number,
  filters: Record<string, any>
): Promise<any> => {
  try {
    const response = await axios.get(`/analytics/api/charts/${chartId}/data`, {
      params: filters,
    });
    console.log(`Chart ${chartId} response:`, response.data);
    return response.data;
  } catch (error) {
    console.error(`Error fetching chart ${chartId} data:`, error);
    throw error;
  }
};

const fetchChartGroupData = async (
  chartGroupId: number | null,
): Promise<any> => {
  try {
    if(!chartGroupId) return null
    const response = await axios.get(
      `/analytics/api/chart-groups/${chartGroupId}`,
    );
    console.log(`ChartGroup ${chartGroupId} response:`, response.data);
    return response.data;
  }
  catch (error) {
    console.error(`Error fetching chartGroup ${chartGroupId} data:`, error);
    throw error;
  }
};

const fetchTabsData = async (): Promise<any> => {
  try {
    const response = await axios.get("/analytics/api/dashboards");
    console.log("Tabs response:", response.data);
    return response.data?.results;
  } catch (error) {
    console.error("Error fetching tabs data:", error);
    throw error;
  }
};

export const useTabsData = () => {
  return useQuery({
    queryKey: ["tabs"],
    queryFn: () => fetchTabsData(),
    staleTime: 5 * 60 * 1000, // Data is considered fresh for 5 minutes
  });
};

export const useChartData = (chartId: number, filters: Record<string, any>) => {
  return useQuery({
    queryKey: ["chart", chartId, filters],
    queryFn: () => fetchChartData(chartId, filters),
    staleTime: 5 * 60 * 1000, // Data is considered fresh for 5 minutes
  });
};

export const useChartGroupData = (
  chartGroupId: number,
) => {
  return useQuery({
    queryKey: ["chartGroup", chartGroupId],
    queryFn: () => fetchChartGroupData(chartGroupId),
    staleTime: 5 * 60 * 1000, // Data is considered fresh for 5 minutes
  });
};

export const useDashboardData = (dashboardId: number) => {
  return useQuery({
    queryKey: ["dashboard", dashboardId],
    queryFn: () => fetchDashboardData(dashboardId),
    staleTime: 5 * 60 * 1000, // Data is considered fresh for 5 minutes
  });
};
