import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import axiosInstance from '../utils/axiosInstance';

export interface BOMFilter {
  page?: number;
  page_size?: number;
  name?: string;
  code?: string;
  product?: string;
  version?: string;
  status?: string;
  effective_date?: string;
  created_at?: string;
  updated_at?: string;
  search?: string;
}

export interface BOMItemPayload {
  bom_header: number;
  component: number;
  parent_item: number | null;
  quantity: number;
}

export interface UpdateBOMItemPayload {
  component?: number;
  parent_item?: number | null;
  quantity?: number;
}

// Fetch BOM headers list
const fetchBOMs = async (filters: BOMFilter = {}): Promise<any> => {
  const params: { [key: string]: any } = {};

  if (filters.name) params.name = filters.name;
  if (filters.code) params.code = filters.code;
  if (filters.product) params.product = filters.product;
  if (filters.version) params.version = filters.version;
  if (filters.status) params.status = filters.status;
  if (filters.effective_date) params.effective_date = filters.effective_date;
  if (filters.created_at) params.created_at = filters.created_at;
  if (filters.updated_at) params.updated_at = filters.updated_at;
  if (filters.search) params.search = filters.search;
  if (filters.page) params.page = filters.page;
  if (filters.page_size) params.page_size = filters.page_size;

  const response = await axiosInstance.get('/bom/api/headers/', { params });
  return response.data;
};

// Fetch single BOM with items
const fetchBOMById = async (bomId: string): Promise<any> => {
  const response = await axiosInstance.get(`/bom/api/headers/${bomId}`);
  return response.data;
};

// Create BOM item
const createBOMItem = async (payload: BOMItemPayload): Promise<any> => {
  const response = await axiosInstance.post('/bom/api/items/', payload);
  return response.data;
};

// Update BOM item
const updateBOMItem = async ({ id, payload }: { id: number; payload: UpdateBOMItemPayload }): Promise<any> => {
  const response = await axiosInstance.patch(`/bom/api/items/${id}/`, payload);
  return response.data;
};

// Delete BOM item
const deleteBOMItem = async (itemId: number): Promise<void> => {
  await axiosInstance.delete(`/bom/api/items/${itemId}/`);
};

// Hook for BOM table listing
export const useBOMTable = (filters: BOMFilter = {}) => {
  return useQuery({
    queryKey: ['boms', filters],
    queryFn: () => fetchBOMs(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook for single BOM details
export const useBOMDetails = (bomId: string) => {
  return useQuery({
    queryKey: ['bom', bomId],
    queryFn: () => fetchBOMById(bomId),
    enabled: !!bomId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Hook for creating BOM item
export const useCreateBOMItem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createBOMItem,
    onSuccess: (_, variables) => {
      // Invalidate and refetch the specific BOM
      queryClient.invalidateQueries({ queryKey: ['bom', variables.bom_header.toString()] });
      // Also invalidate the BOM list
      queryClient.invalidateQueries({ queryKey: ['boms'] });
    },
  });
};

// Hook for updating BOM item
export const useUpdateBOMItem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateBOMItem,
    onSuccess: () => {
      // Invalidate all BOM queries to ensure fresh data
      queryClient.invalidateQueries({ queryKey: ['bom'] });
      queryClient.invalidateQueries({ queryKey: ['boms'] });
    },
  });
};

// Hook for deleting BOM item
export const useDeleteBOMItem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteBOMItem,
    onSuccess: () => {
      // Invalidate all BOM queries to ensure fresh data
      queryClient.invalidateQueries({ queryKey: ['bom'] });
      queryClient.invalidateQueries({ queryKey: ['boms'] });
    },
  });
};