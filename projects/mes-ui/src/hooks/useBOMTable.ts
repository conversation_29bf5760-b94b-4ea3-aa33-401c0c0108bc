import { useQuery } from '@tanstack/react-query';
import axiosInstance from '../utils/axiosInstance';

export interface BOMFilter {
  page?: number;
  page_size?: number;
  name?: string;
  code?: string;
  product?: string;
  version?: string;
  status?: string;
  effective_date?: string;
  created_at?: string;
  updated_at?: string;
  search?: string;
}

const fetchBOMs = async (filters: BOMFilter = {}): Promise<any> => {
  const params: { [key: string]: any } = {};

  if (filters.name) params.name = filters.name;
  if (filters.code) params.code = filters.code;
  if (filters.product) params.product = filters.product;
  if (filters.version) params.version = filters.version;
  if (filters.status) params.status = filters.status;
  if (filters.effective_date) params.effective_date = filters.effective_date;
  if (filters.created_at) params.created_at = filters.created_at;
  if (filters.updated_at) params.updated_at = filters.updated_at;
  if (filters.page) params.page = filters.page;
  if (filters.page_size) params.page_size = filters.page_size;

  const response = await axiosInstance.get('/bom/api/headers/');
  return response.data;
};

export const useBOMTable = (filters: BOMFilter = {}) => {
  return useQuery({
    queryKey: ['boms', filters],
    queryFn: () => fetchBOMs(filters),
  });
}; 