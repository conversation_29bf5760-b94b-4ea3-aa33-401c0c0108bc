import { useMutation, UseMutationResult } from "@tanstack/react-query";
import axiosInstance from "../utils/axiosInstance";

// Interface for the routing schema
export interface RoutingSchema {
  id?: string;
  name?: string;
  type?: string;
  version?: string;
  route: {
    start: string;
    end: string;
    connections: Record<string, any>;
  };
  components: Record<string, any>;
}

// Interface for the request payload
export interface SaveRoutingPayload {
  name: string;
  code: string;
  schema: {
    routing_schema: RoutingSchema;
  };
}

// Interface for the response
export interface SaveRoutingResponse {
  id: number;
  name: string;
  code: string;
  schema: {
    routing_schema: RoutingSchema;
  };
  created_at?: string;
  updated_at?: string;
}

// Function to save a routing schema
const saveRouting = async (payload: SaveRoutingPayload, id?: number): Promise<SaveRoutingResponse> => {
  try {
    let response;

    if (id) {
      // Update existing routing
      response = await axiosInstance.put(`/workflow/api/routings/${id}/`, payload);
    } else {
      // Create new routing
      response = await axiosInstance.post('/workflow/api/routings/', payload);
    }

    console.log('API response for saving routing:', response.data);
    return response.data;
  } catch (error) {
    console.error("Error saving routing schema:", error);
    throw error;
  }
};

// Custom hook for saving routing schemas
export const useSaveRouting = (): UseMutationResult<
  SaveRoutingResponse,
  Error,
  { payload: SaveRoutingPayload; id?: number }
> => {
  return useMutation({
    mutationFn: ({ payload, id }) => saveRouting(payload, id),
    onSuccess: (data) => {
      console.log("Routing schema saved successfully:", data);
    },
    onError: (error) => {
      console.error("Error saving routing schema:", error);
    }
  });
};

// Helper function to create a payload from a routing schema
export const createRoutingPayload = (
  routingSchema: RoutingSchema,
  name: string,
  code: string
): SaveRoutingPayload => {
  // Make sure the schema has the required structure
  const schemaToSave: RoutingSchema = {
    ...routingSchema,
    route: routingSchema.route || {
      start: '',
      end: '',
      connections: {}
    },
    components: routingSchema.components || {}
  };

  return {
    name,
    code,
    schema: {
      routing_schema: schemaToSave
    }
  };
};
