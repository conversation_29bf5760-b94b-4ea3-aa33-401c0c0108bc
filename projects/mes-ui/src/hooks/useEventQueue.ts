import { useEffect, useState } from "react";

export interface EventItem {
  id: string;
  data: any;
  status: "success" | "failed";
  timestamp: number;
}

export const useEventQueue = () => {
  const [eventQueue, setEventQueue] = useState<EventItem[]>([]);

  // ✅ Fetch events from localStorage
  const fetchEvents = (formCode: string) => {
    const storedEvents = JSON.parse(
      localStorage.getItem(`events-${formCode}`) || "[]"
    );
    setEventQueue(storedEvents); // ✅ Ensure state updates
  };

  // ✅ Add event and force refresh
  const addEvent = (
    formCode: string,
    eventData: any,
    status: "success" | "failed"
  ) => {
    const newEvent: EventItem = {
      id: Date.now().toString(),
      data: eventData,
      status,
      timestamp: Date.now(),
    };

    const storedEvents = JSON.parse(
      localStorage.getItem(`events-${formCode}`) || "[]"
    );
    const updatedQueue = [newEvent, ...storedEvents].slice(0, 30);
    setEventQueue([...updatedQueue]); // ✅ Force component update

    localStorage.setItem(`events-${formCode}`, JSON.stringify(updatedQueue));
  };

  return { eventQueue, fetchEvents, addEvent };
};
