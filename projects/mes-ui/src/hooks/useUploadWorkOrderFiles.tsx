import { useMutation } from "@tanstack/react-query";
import { useSnackbar } from "../context/SnackBarContext";
import axiosInstance from "../utils/axiosInstance";

const uploadWorkOrderFile = async (formData: FormData) => {
  const response = await axiosInstance.post(
    "/operation/api/import-wo-excel/",
    formData,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }
  );
  return response.data;
};

export const useUploadWorkOrderFile = () => {
  const { showSnackbar } = useSnackbar();
  return useMutation<any, Error, any>({
    mutationFn: (data: FormData) => uploadWorkOrderFile(data),
    onSuccess: (data: any) => {
      showSnackbar(`Work Order file uploaded successfully`, "success");
    },
    onError: (error: any) => {
      console.error(error);
      showSnackbar(`Error in Work Order file upload. Kindly try again later`, "error");
    },
  });
};
