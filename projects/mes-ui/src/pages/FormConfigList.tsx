import React from "react";
import styles from "../styles/FormList.module.scss"; // Import the SCSS module
import { useNavigate } from "react-router-dom";
import { useFormConfig } from "../hooks/useForm";
import ErrorMessage from "../components/ErrorMessage";
import { Button } from "@mui/material";
import { useAuth } from "../context/AuthContext";

const FormConfigList: React.FC = () => {
  const { data, isLoading, error } = useFormConfig(true);
  const { setIsOneFormUser } = useAuth();
  const navigate = useNavigate();

  const handleFormClick = (id: number) => {
    navigate(`/form/${id}`);
  };

  if (isLoading)
    return <div style={{ textAlign: "center" }}>Loading list...</div>;

  if (error) {
    console.error(error);
    // const isUnauthorized = error?.response?.status === 403;
    const errorMessage = true
      ? "You are not authorized to access the resource."
      : "Oops! Something went wrong! Please try again later.";

    return <ErrorMessage message={errorMessage} />;
  }

  if ((data as any)?.results?.length === 1) {
    const formId = (data as any)?.results[0].id;
    if (formId) {
      setIsOneFormUser(true);
      navigate(`/form/${formId}`);
      return;
    }
  }

  if ((data as any)?.results?.length)
    return (
      <>
        <div className={styles.formListContainer}>
          <h2 className={styles.formListTitle}>Work Stations</h2>
          <ul className={styles.formList}>
            {(data as any)?.results?.map((form: any, index: number) => (
              <li key={form.id} className={styles.formListItem}>
                <Button
                  variant="contained" // Material UI variant
                  color="primary" // Material UI color
                  onClick={() => handleFormClick(form.id)}
                  sx={{
                    textTransform: "none",
                    backgroundColor: "#3e4146",
                    fontWeight: "bold",
                    padding: "8px 16px",
                    fontSize: "1rem",
                    marginBottom: "10px",
                    display: "flex", // Use flexbox for alignment
                    textAlign: "center",
                    "&:hover": {
                      backgroundColor: "#1f2022", // Darker black on hover
                    },
                  }}
                >
                  <p
                    style={{
                      minWidth: "150px",
                      display: "flex",
                      justifyContent: "space-between",
                    }}
                  >
                    {index + 1}. &nbsp;
                    {form.name}
                  </p>
                </Button>
              </li>
            ))}
          </ul>
        </div>
      </>
    );
  return <h4 style={{ textAlign: "center" }}>No form config available</h4>;
};

export default FormConfigList;
