import React, { useState, useRef, useCallback, useEffect, useMemo } from 'react';
import {
  ReactFlowProvider,
  useNodesState,
  useEdgesState,
  useReactFlow,
  Node,
  Edge,
  Connection,
  ReactFlowInstance
} from 'reactflow';

// Import custom components
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import 'reactflow/dist/style.css';
import { v4 as uuidv4 } from 'uuid';
import { Button } from '@mui/material';
import AddIcon from '@mui/icons-material/Add';

// Import services
import { useProcessBlocks, transformToNodeInfo } from '../hooks/useProcessBlocks';
import { useSaveRouting, createRoutingPayload } from '../hooks/useSaveRouting';
import { generateSchema, validateJsonBrackets, parseJsonSchema } from '../services/schemaGenerator'
import axiosInstance from '../utils/axiosInstance';

// Import components
import CustomNode from '../components/routing/CustomNode';
import CustomEdge from '../components/routing/CustomEdge';
import LeftSidebar from '../components/routing/LeftSidebar';
import RightSidebar from '../components/routing/RightSidebar';
import FlowCanvas from '../components/routing/FlowCanvas';
import ConnectionHandler from '../components/routing/ConnectionHandler';
import { ImportModalComponent, ExportModalComponent } from '../components/routing/ModalComponents';
import AddProcessBlockModal from '../components/AddProcessBlockModal';
import RoutingHeader from '../components/routing/RoutingHeader';

// Import styles
import '../components/routing/App.scss';
import '../components/routing/ReactFlowOverrides.scss';
import '../components/routing/CanvasOverrides.scss';
import '../components/routing/ConnectionHighlight.scss';
import '../components/routing/AddWorkstationButton.scss';
import { useSnackbar } from '../context/SnackBarContext';

// Define edge types outside the component to avoid recreation on each render
const staticEdgeTypes = {
  custom: CustomEdge
};

// Initial empty nodes and edges
const initialNodes: Node[] = [];
const initialEdges: Edge[] = [];

// Import router hooks

// Helper Function: Process drag data for node drop
const processNodeDragData = (event: React.DragEvent<HTMLDivElement>) => {
  let nodeData;
  try {
    // Try different formats for compatibility
    const jsonData = event.dataTransfer.getData('application/json');
    const textData = event.dataTransfer.getData('text');

    // Try parsing the data
    if (jsonData) {
      nodeData = JSON.parse(jsonData);
      console.log("Found JSON data:", nodeData);
    } else if (textData) {
      try {
        nodeData = JSON.parse(textData);
        console.log("Found text data as JSON:", nodeData);
      } catch (e) {
        // If not JSON, use as is
        nodeData = { label: textData, nodeType: "default" };
        console.log("Using text as label:", nodeData);
      }
    }
  } catch (error) {
    console.error("Error parsing drag data:", error);
  }

  // Fallback: Check if we have window data from DnD
  if (!nodeData && (window as any).__LAST_DRAGGED_ITEM) {
    console.log("Using lastDraggedItem:", (window as any).__LAST_DRAGGED_ITEM);
    nodeData = (window as any).__LAST_DRAGGED_ITEM;
  }

  // Hard fallback - create a placeholder
  if (!nodeData) {
    console.log("No drag data found, using placeholder");
    nodeData = { label: "Unknown Node", nodeType: "default" };
  }

  return nodeData;
};

function Flow({ schema, readOnly }: { schema?: any, readOnly?: boolean }) {
  const { showSnackbar } = useSnackbar();
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [reactFlowInstance, setReactFlowInstance] = useState<ReactFlowInstance | null>(null);

  // State for connection modal
  const [modalOpen, setModalOpen] = useState(false);
  const [currentEdgeParams, setCurrentEdgeParams] = useState<Connection | null>(null);

  // State for edge operations
  const [selectedEdge, setSelectedEdge] = useState<Edge | null>(null);
  const [isEditingEdge, setIsEditingEdge] = useState(false);

  // State for node operations
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);

  // State for start/end nodes
  const [startNodeId, setStartNodeId] = useState('');
  const [endNodeId, setEndNodeId] = useState('');

  // State for collapsible sidebars
  const [leftSidebarCollapsed, setLeftSidebarCollapsed] = useState(false);
  const [rightSidebarCollapsed, setRightSidebarCollapsed] = useState(false);

  // State for JSON import/export
  const [importModalOpen, setImportModalOpen] = useState(false);
  const [exportModalOpen, setExportModalOpen] = useState(false);
  const [importedJson, setImportedJson] = useState('');
  const [configName, setConfigName] = useState('New Routing Configuration');
  const [configCode, setConfigCode] = useState('');
  const [exportedJson, setExportedJson] = useState('');
  const [importLoading, setImportLoading] = useState(false);
  const [importError, setImportError] = useState<string | null>(null);

  // State for saving schema
  const [currentPayload, setCurrentPayload] = useState<any>(null);
  const [isSaving, setIsSaving] = useState(false);

  // State for dark mode
  const [darkMode, setDarkMode] = useState(false);

  // State for Add Workstation modal
  const [addWorkstationModalOpen, setAddWorkstationModalOpen] = useState(false);

  // Fetch process blocks using our custom hook
  const { data: apiProcessBlocks, isLoading, error } = useProcessBlocks(readOnly);

  // Transform API process blocks to NodeInfo format
  const processBlocks = useMemo(() => {
    if (!apiProcessBlocks) return [];
    return transformToNodeInfo(apiProcessBlocks);
  }, [apiProcessBlocks]);

  // Navigation is handled in the ExportModalComponent

  // Get the React Flow instance for viewport operations
  const { fitView } = useReactFlow();

  // Update start/end node options when nodes change
  // Use a Map to ensure unique node IDs
  const nodeOptionsMap = new Map();
  nodes.forEach(node => {
    nodeOptionsMap.set(node.id, { value: node.id, label: node.data.label });
  });
  // Convert Map values to array
  const nodeOptions = Array.from(nodeOptionsMap.values());

  // Added useEffect to update nodeOptions when nodes change
  useEffect(() => {
    console.log("Current nodes on canvas:", nodes);
  }, [nodes]);

  // Log process blocks when they change
  useEffect(() => {
    if (!readOnly) {
      if (isLoading) {
        console.log('Loading process blocks...');
      } else if (error) {
        console.error('Error loading process blocks:', error);
      } else if (apiProcessBlocks) {
        console.log('Process blocks loaded successfully:', apiProcessBlocks.length);
      }
    }
  }, [isLoading, error, apiProcessBlocks]);

  // Event listener for opening the connection modal from edge edit button
  useEffect(() => {
    const handleOpenConnectionModal = (event: CustomEvent) => {
      const detail = event.detail;
      console.log('Connection modal event received:', detail);

      if (detail) {
        // Find the edge to edit
        const edgeToEdit = edges.find(edge => edge.id === detail.edgeId);

        if (edgeToEdit && edgeToEdit.data) {
          console.log('Found edge to edit:', edgeToEdit);

          // Set current edge params
          setCurrentEdgeParams({
            source: detail.sourceId,
            target: detail.targetId,
            sourceHandle: detail.sourceHandle || edgeToEdit.sourceHandle,
            targetHandle: detail.targetHandle || edgeToEdit.targetHandle
          });

          // Set the selected edge
          setSelectedEdge(edgeToEdit);

          // Set editing mode flag
          setIsEditingEdge(true);

          // Open the modal
          setModalOpen(true);
        }
      }
    };

    // Add event listener
    document.addEventListener('openConnectionModal', handleOpenConnectionModal as EventListener);

    // Cleanup
    return () => {
      document.removeEventListener('openConnectionModal', handleOpenConnectionModal as EventListener);
    };
  }, [edges]);

  // Update node data when start/end node selections change
  useEffect(() => {
    setNodes((nds) =>
      nds.map((node) => {
        // Check if this node is the start or end node
        const isStart = node.id === startNodeId;
        const isEnd = node.id === endNodeId;

        // Only update if needed
        if (isStart || isEnd || node.data.isStartNode || node.data.isEndNode) {
          return {
            ...node,
            data: {
              ...node.data,
              isStartNode: isStart,
              isEndNode: isEnd,
            },
          };
        }
        return node;
      })
    );
  }, [startNodeId, endNodeId, setNodes]);

  // Handler for node clicks
  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    event.stopPropagation(); // Prevent event bubbling
    setSelectedNode(node);
    setSelectedEdge(null); // Deselect any edge
  }, []);

  // Handler for node deletion - called from CustomNode component
  const onNodeDelete = useCallback((nodeId: string) => {
    // Remove all edges connected to this node
    setEdges((eds) => eds.filter((e) => e.source !== nodeId && e.target !== nodeId));

    // Remove the node
    setNodes((nds) => nds.filter((n) => n.id !== nodeId));

    // If this was a start or end node, clear that state
    if (nodeId === startNodeId) setStartNodeId('');
    if (nodeId === endNodeId) setEndNodeId('');

    // Clear selection if this was the selected node
    if (selectedNode && selectedNode.id === nodeId) {
      setSelectedNode(null);
    }
  }, [selectedNode, setNodes, setEdges, startNodeId, endNodeId]);

  // Define node types with custom props using useMemo to avoid recreation on each render
  const nodeTypes = useMemo(() => ({
    default: (props: any) => <CustomNode {...props} onDelete={readOnly ? null : onNodeDelete} />,
  }), [onNodeDelete]);

  // Use the static edge types
  const edgeTypes = useMemo(() => staticEdgeTypes, []);

  // Handler for edge clicks
  const onEdgeClick = useCallback((event: React.MouseEvent, edge: Edge) => {
    event.stopPropagation(); // Prevent event bubbling
    setSelectedEdge(edge);
    setSelectedNode(null); // Deselect any node
  }, []);

  // Handler for edge deletion
  const deleteSelectedEdge = useCallback(() => {
    if (selectedEdge) {
      setEdges((eds) => eds.filter((e) => e.id !== selectedEdge.id));
      setSelectedEdge(null);
    }
  }, [selectedEdge, setEdges]);

  // Handler for edge editing
  const editSelectedEdge = useCallback(() => {
    if (selectedEdge) {
      // Set current edge params for the modal
      setCurrentEdgeParams({
        source: selectedEdge.source,
        target: selectedEdge.target,
        sourceHandle: selectedEdge.sourceHandle || null,
        targetHandle: selectedEdge.targetHandle || null,
      });
      // Set editing mode flag to true (for modal to know it's editing existing)
      setIsEditingEdge(true);
      setModalOpen(true);
    }
  }, [selectedEdge]);

  // Handler for click on empty canvas (to deselect)
  const onPaneClick = useCallback(() => {
    setSelectedEdge(null);
    setSelectedNode(null);
  }, []);

  // Validate connections before allowing them
  const isValidConnection = useCallback((connection: Connection) => {
    console.log('Validating connection:', connection);
    const { source, target, sourceHandle, targetHandle } = connection;

    // Don't allow connections to the same node (self-loops)
    if (source === target) {
      console.log('Invalid connection: source and target are the same');
      return false;
    }

    // Check if this exact connection already exists (same source, target, sourceHandle, targetHandle)
    // This is the only validation we need to prevent duplicate connections
    const existingConnection = edges.some(
      (edge) => {
        const sameNodes = edge.source === source && edge.target === target;
        const sameHandles = edge.sourceHandle === sourceHandle && edge.targetHandle === targetHandle;
        return sameNodes && sameHandles;
      }
    );

    if (existingConnection) {
      console.log('Invalid connection: connection already exists');
      return false;
    }

    // Allow the connection - we're being very permissive to allow multiple connections per handle
    console.log('Connection validated successfully');
    return true;
  }, [edges, nodes]);

  // Function to open the connection modal
  const openConnectionModal = useCallback((sourceId: string, targetId: string, sourceHandle?: string, targetHandle?: string) => {
    console.log('Opening connection modal for:', { sourceId, targetId, sourceHandle, targetHandle });

    // Find the source and target nodes to get their handles
    const sourceNode = nodes.find(node => node.id === sourceId);
    const targetNode = nodes.find(node => node.id === targetId);

    if (!sourceNode || !targetNode) {
      console.error('Source or target node not found');
      return;
    }

    // Default handles if not provided
    const defaultSourceHandle = sourceHandle || 'output-0';
    const defaultTargetHandle = targetHandle || 'input-0';

    // Set current edge params for the modal
    setCurrentEdgeParams({
      source: sourceId,
      target: targetId,
      sourceHandle: defaultSourceHandle,
      targetHandle: defaultTargetHandle,
    });

    // Reset editing mode flag (we're creating a new connection)
    setIsEditingEdge(false);

    // Open the modal
    setModalOpen(true);
  }, [nodes]);

  // Handle connecting nodes
  const onConnect = useCallback((params: Connection) => {
    console.log('Connection attempt:', params);

    // Always log the connection parameters for debugging
    console.log('Connection params:', JSON.stringify(params));

    // Make sure we have valid source and target
    if (!params.source || !params.target) {
      console.error('Missing source or target in connection params');
      return;
    }

    // Open the connection modal to configure this connection
    openConnectionModal(
      params.source,
      params.target,
      params.sourceHandle || 'output-0',
      params.targetHandle || 'input-0'
    );
  }, [openConnectionModal]);

  const handleSaveConnection = useCallback((connectionData: { connectionType: string; routeType: string; edgeType: string; conditionPath?: string; conditionValue?: string; operator?: string; sourceHandle?: string; targetHandle?: string; connectionName?: string; priority?: number }) => {
    if (currentEdgeParams) {
      console.log('Saving connection with params:', currentEdgeParams);
      console.log('Connection data:', connectionData);

      if (isEditingEdge && selectedEdge) {
        // Editing an existing edge
        const updatedEdge = {
          ...selectedEdge,
          sourceHandle: connectionData.sourceHandle || selectedEdge.sourceHandle,
          targetHandle: connectionData.targetHandle || selectedEdge.targetHandle,
          data: {
            ...connectionData,
            connectionId: selectedEdge.data?.connectionId || uuidv4().substring(0, 8) // Preserve the connection ID
          },
        };

        console.log('Updating existing edge:', updatedEdge);

        setEdges((eds) => {
          return eds.map((e) => (e.id === selectedEdge.id ? updatedEdge : e));
        });
      } else {
        // Generate a unique connection ID
        const connectionId = uuidv4().substring(0, 8);

        // Extract handle types from the connection data or params
        const sourceHandleBase = connectionData.sourceHandle || currentEdgeParams.sourceHandle || 'output-0';
        const targetHandleBase = connectionData.targetHandle || currentEdgeParams.targetHandle || 'input-0';

        // Creating a new edge
        const newEdge = {
          id: `edge-${currentEdgeParams.source || 'unknown'}-${currentEdgeParams.target || 'unknown'}-${connectionId}`,
          type: 'custom',  // Use our custom edge
          data: {
            ...connectionData,
            connectionId: connectionId, // Add the unique connection ID
            connectionName: connectionData.connectionName || `Connection from ${currentEdgeParams.source} to ${currentEdgeParams.target}`
          },
          source: currentEdgeParams.source || '',
          target: currentEdgeParams.target || '',
          sourceHandle: sourceHandleBase,
          targetHandle: targetHandleBase
        };

        // Log the edge details for debugging
        console.log('New edge details:', {
          id: newEdge.id,
          source: newEdge.source,
          target: newEdge.target,
          sourceHandle: newEdge.sourceHandle,
          targetHandle: newEdge.targetHandle,
          data: newEdge.data
        });

        console.log('Creating new edge with connectionId:', connectionId);

        // Only add the edge if source and target are valid
        if (newEdge.source && newEdge.target) {
          // We'll allow multiple connections between the same nodes and handles
          // by using the unique connectionId
          setEdges((eds) => {
            // Log the existing edges for debugging
            console.log('Current edges before adding new edge:', eds);

            // We don't need to filter out existing edges with the same handles
            // since we're using unique connection IDs

            // Create a new array with all existing edges and the new edge
            const updatedEdges = [...eds, newEdge];
            console.log('Updated edges count:', updatedEdges.length);
            return updatedEdges;
          });
        }
      }
    }
    setModalOpen(false);
    setCurrentEdgeParams(null);
    setIsEditingEdge(false);

    // Log the current edges after update
    setTimeout(() => {
      console.log('Current edges after update:', edges);
    }, 100);
  }, [currentEdgeParams, setEdges, selectedEdge, isEditingEdge, edges, isValidConnection]);

  // Handle drag over for node placement
  const onDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  // Handle node drop on canvas
  const onDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    console.log("Drop event triggered");

    if (!reactFlowWrapper.current) {
      console.error("reactFlowWrapper not available");
      return;
    }

    const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();
    console.log("Flow bounds:", reactFlowBounds);

    // Extract node data from drag event using helper function
    const nodeData = processNodeDragData(event);
    if (!nodeData) {
      console.error("No node data found in drop event");
      return; // Exit if no valid node data found
    }
    console.log("Node data extracted:", nodeData);

    // Get drop position in ReactFlow coordinates
    if (!reactFlowInstance) {
      console.error("ReactFlow instance not available");
      return;
    }
    const position = reactFlowInstance.project({
      x: event.clientX - reactFlowBounds.left,
      y: event.clientY - reactFlowBounds.top,
    });
    console.log("Calculated position:", position);

    // Create the new node
    const newNode = {
      id: nodeData.id || `node-${uuidv4()}`,  // Use provided ID or generate a new one
      type: 'default',                        // Use custom node
      position,
      data: {
        label: nodeData.label,
        nodeType: nodeData.nodeType || 'default',
        // Start with not being start/end node
        isStartNode: false,
        isEndNode: false,
      },
    };

    // Add the node
    setNodes((nds) => nds.concat(newNode));

    console.log("Node added:", newNode);
  }, [reactFlowInstance, setNodes]);

  // Initialize the save routing mutation
  const saveRoutingMutation = useSaveRouting();

  // Generate JSON schema from current nodes and edges
  const handleGenerateJson = async () => {
    if (!startNodeId || !endNodeId) {
      alert("Please select both start and end nodes before generating JSON.");
      return;
    }

    console.log('Generating JSON schema with start node:', startNodeId, 'and end node:', endNodeId);

    // Use the schema generator service to create the schema
    const schema = generateSchema(nodes, edges, {
      configName,
      configCode,
      startNodeId,
      endNodeId
    });

    console.log('Created schema structure with ID:', schema.schema.routing_schema.id);

    // Set the exported JSON
    const jsonString = JSON.stringify(schema, null, 2);
    setExportedJson(jsonString);

    // Create the payload for saving
    const payload = createRoutingPayload(
      schema.schema.routing_schema,
      configName || 'New Routing Configuration',
      configCode || (configName || 'New Routing Configuration').toLowerCase().replace(/\s+/g, '_')
    );

    console.log('Saving payload:', payload);

    // Show the export modal with the generated schema
    setExportModalOpen(true);

    // Store the generated payload for later saving
    setCurrentPayload(payload);

    // Show success message for schema generation
    showSnackbar("Schema generated successfully! Click Save to save it to the server.", "success");

    console.log("Generated Schema:", schema);
    console.log("JSON:", jsonString);
  };

  // Use the validateJsonBrackets function from the schemaGenerator service

  // Debug function to log the structure of imported JSON
  const debugImportedJson = (jsonData: any) => {
    console.log('=== DEBUG IMPORTED JSON ===');
    console.log('Full schema:', jsonData);

    if (jsonData?.schema?.routing_schema) {
      const schema = jsonData.schema.routing_schema;
      console.log('Components:', schema.components);

      // Check if positions are properly defined
      Object.keys(schema.components).forEach(id => {
        const comp = schema.components[id];
        console.log(`Component ${id} position:`, comp.position);

        if (comp.position) {
          console.log(`  x type: ${typeof comp.position.x}, value: ${comp.position.x}`);
          console.log(`  y type: ${typeof comp.position.y}, value: ${comp.position.y}`);
        }
      });
    }
    console.log('=== END DEBUG ===');
  };



  // Import JSON schema and recreate the diagram
  const handleImportJson = async (schema?: any) => {
    try {
      let importData;

      if (!schema) {
        // Check for balanced brackets first
        if (!validateJsonBrackets(importedJson)) {
          alert('Invalid JSON format: Unbalanced brackets. Please check your input.');
          return;
        }

        console.log('Starting import process with JSON validation passed');

        try {
          // Use the parseJsonSchema function to validate and parse the JSON
          importData = parseJsonSchema(importedJson);
          // Debug the imported JSON structure
          debugImportedJson(importData);
        } catch (e) {
          alert('Invalid JSON format. Please check your input.');
          return;
        }
        // Extract the routing schema
        schema = importData.schema?.routing_schema;
        // Extract configuration metadata
        if (importData.name) setConfigName(importData.name);
        if (importData.code) setConfigCode(importData.code);

      }

      if (!schema || !schema.route || !schema.components) {
        alert('Invalid routing schema format. Please check your input.');
        return;
      }

      // Extract start/end nodes from the schema
      const startNode = schema.route.start;
      const endNode = schema.route.end;

      // Set start/end node state
      setStartNodeId(startNode);
      setEndNodeId(endNode);

      // Get components
      const components = schema.components;

      // Create the nodes with positions
      // Log the components to debug
      console.log('Importing components:', components);

      // Create a map of node positions
      const newNodes = Object.keys(components).map((componentId) => {
        const component = components[componentId];

        // Extract position directly from the component
        // Make sure we're getting the exact position as stored in the schema
        let position = { x: 100 + Math.random() * 200, y: 100 + Math.random() * 200 }; // Default position with some randomness

        if (component.position) {
          // Handle different position formats
          let x, y;

          // Handle object format
          if (typeof component.position === 'object') {
            if ('x' in component.position && 'y' in component.position) {
              // Try to parse as numbers if they're strings
              x = typeof component.position.x === 'string'
                ? parseFloat(component.position.x)
                : component.position.x;
              y = typeof component.position.y === 'string'
                ? parseFloat(component.position.y)
                : component.position.y;

              // Round to integers if needed
              x = Math.round(x);
              y = Math.round(y);
            }
          }
          // Handle string format (e.g., "100,200")
          else if (typeof component.position === 'string') {
            const parts = component.position.split(',');
            if (parts.length === 2) {
              x = Math.round(parseFloat(parts[0].trim()));
              y = Math.round(parseFloat(parts[1].trim()));
            }
          }

          if (x !== undefined && y !== undefined && !isNaN(x) && !isNaN(y)) {
            position = { x, y };
            console.log(`Valid position for ${componentId}: x=${x}, y=${y}`);
          } else {
            console.warn(`Invalid position for ${componentId}:`, component.position);
          }
        }

        return {
          id: componentId,
          type: 'default',
          position: position,
          data: {
            label: component.name || componentId,
            nodeType: component.node_type || 'machine',
            isStartNode: componentId === startNode,
            isEndNode: componentId === endNode
          }
        };
      });

      // Create edges from connections
      const newEdges: Edge[] = [];
      const connections = schema.route.connections;

      // Debug log for connections
      console.log('Importing connections:', connections);
      console.log('Connection keys:', Object.keys(connections));

      // Log edge types for debugging
      if (connections) {
        Object.keys(connections).forEach(sourceId => {
          const conn = connections[sourceId];
          if (conn.towards) {
            console.log(`Connection from ${sourceId} has edge type: ${conn.towards.edge_type || 'undefined'}`);

            // Log conditional edge types
            if (conn.towards.conditions && Array.isArray(conn.towards.conditions)) {
              conn.towards.conditions.forEach((condition: any, index: number) => {
                console.log(`  Conditional edge ${index} has edge type: ${condition.edge_type || 'undefined'}`);
              });
            }
          }
        });
      }

      // Verify that all nodes exist before creating edges
      const nodeIds = newNodes.map(node => node.id);
      console.log('Available node IDs for connections:', nodeIds);

      // Generic handling for circular connections
      console.log('Checking for circular connections in the schema...');
      let hasCircularConnections = false;

      if (schema.route?.connections) {
        // Look for nodes that have connections pointing back to nodes that point to them
        Object.entries(schema.route.connections).forEach(([sourceId, connection]: [string, any]) => {
          if (connection?.towards) {
            // Check default connection
            if (connection.towards.default && connection.towards.default !== 'end') {
              const targetId = connection.towards.default;

              // Check if target has a connection back to source
              const targetConnection = schema.route.connections[targetId];
              if (targetConnection?.towards) {
                if (targetConnection.towards.default === sourceId) {
                  console.log(`Found circular connection: ${sourceId} -> ${targetId} -> ${sourceId} (default routes)`);
                  hasCircularConnections = true;
                }

                // Check conditional connections from target back to source
                if (targetConnection.towards.conditions) {
                  const hasBackConnection = targetConnection.towards.conditions.some(
                    (c: any) => c.target === sourceId
                  );

                  if (hasBackConnection) {
                    console.log(`Found circular connection: ${sourceId} -> ${targetId} -> ${sourceId} (conditional route)`);
                    hasCircularConnections = true;
                  }
                }
              }
            }

            // Check conditional connections
            if (connection.towards.conditions) {
              connection.towards.conditions.forEach((condition: any) => {
                if (condition.target) {
                  const targetId = condition.target;

                  // Check if target has a connection back to source
                  const targetConnection = schema.route.connections[targetId];
                  if (targetConnection?.towards) {
                    if (targetConnection.towards.default === sourceId) {
                      console.log(`Found circular connection: ${sourceId} -> ${targetId} -> ${sourceId} (mixed routes)`);
                      hasCircularConnections = true;
                    }

                    // Check conditional connections from target back to source
                    if (targetConnection.towards.conditions) {
                      const hasBackConnection = targetConnection.towards.conditions.some(
                        (c: any) => c.target === sourceId
                      );

                      if (hasBackConnection) {
                        console.log(`Found circular connection: ${sourceId} -> ${targetId} -> ${sourceId} (conditional routes)`);
                        hasCircularConnections = true;
                      }
                    }
                  }
                }
              });
            }
          }
        });
      }

      if (hasCircularConnections) {
        console.log('Circular connections detected in the schema - will use special handling during import');
      }

      if (!connections) {
        console.warn('No connections found in the imported schema');
      } else {
        Object.keys(connections).forEach((sourceId) => {
          const connection = connections[sourceId];
          console.log(`Processing connection from ${sourceId}:`, connection);

          // Skip if source node doesn't exist
          if (!nodeIds.includes(sourceId)) {
            console.warn(`Source node ${sourceId} not found, skipping its connections`);
            return;
          }

          if (connection && connection.towards) {
            // Generic handling for nodes with rework connections
            const isReworkConnection = connection.towards.route_type === 'rework';
            if (isReworkConnection) {
              console.log(`Found REWORK connection from ${sourceId} to ${connection.towards.default}`);

              // Check if this node also has conditional connections
              if (connection.towards.conditions && connection.towards.conditions.length > 0) {
                console.log(`Node ${sourceId} has both rework and conditional connections - special handling required`);
                // We'll handle this by ensuring unique handles for each connection type
              }
            }

            // Process default connection
            if (connection.towards.default && connection.towards.default !== 'end') {
              // Skip if target node doesn't exist
              if (!nodeIds.includes(connection.towards.default)) {
                console.warn(`Target node ${connection.towards.default} not found, skipping connection`);
                return;
              }

              // Special handling for AOI node that might have both default and conditional connections to the same target
              const hasSameTargetInConditions = connection.towards.conditions &&
                connection.towards.conditions.some((c: any) => c.target === connection.towards.default);

              if (hasSameTargetInConditions) {
                console.log(`Node ${sourceId} has both default and conditional connections to ${connection.towards.default}`);
                // In this case, we'll use different handles for the default and conditional connections
              }

              console.log(`Creating default edge from ${sourceId} to ${connection.towards.default}`);
              console.log(`Edge type: ${connection.towards.edge_type || 'bezier'}`);

              // Determine source and target handles
              let sourceHandle = connection.towards.sourceHandle;
              let targetHandle = connection.towards.targetHandle;

              // If handles are missing, assign default handles based on node positions
              if (!sourceHandle || !targetHandle) {
                const sourceNode = newNodes.find(n => n.id === sourceId);
                const targetNode = newNodes.find(n => n.id === connection.towards.default);

                if (sourceNode && targetNode) {
                  // Determine relative positions to choose appropriate handles
                  const dx = targetNode.position.x - sourceNode.position.x;
                  const dy = targetNode.position.y - sourceNode.position.y;

                  // Choose source handle based on direction
                  if (Math.abs(dx) > Math.abs(dy)) {
                    // Horizontal connection
                    sourceHandle = dx > 0 ? 'output-0' : 'output-1';
                  } else {
                    // Vertical connection
                    sourceHandle = dy > 0 ? 'output-2' : 'output-3';
                  }

                  // Choose target handle based on direction
                  if (Math.abs(dx) > Math.abs(dy)) {
                    // Horizontal connection
                    targetHandle = dx > 0 ? 'input-0' : 'input-1';
                  } else {
                    // Vertical connection
                    targetHandle = dy > 0 ? 'input-2' : 'input-3';
                  }

                  console.log(`Assigned handles based on positions: source=${sourceHandle}, target=${targetHandle}`);
                } else {
                  // Fallback to default handles
                  sourceHandle = sourceHandle || 'output-0';
                  targetHandle = targetHandle || 'input-0';
                }
              }

              // Get the edge type from the connection
              let edgeType = connection.towards.edge_type || 'bezier';

              // Ensure the edge type is valid
              const validEdgeTypes = ['bezier', 'straight', 'step', 'smoothstep', 'cross'];
              if (!validEdgeTypes.includes(edgeType)) {
                console.warn(`Invalid edge type: ${edgeType}, defaulting to bezier`);
                edgeType = 'bezier';
              }

              console.log(`Using edge type: ${edgeType} for connection from ${sourceId} to ${connection.towards.default}`);

              // Generate a unique connection ID
              const connectionId = uuidv4().substring(0, 8);

              // Preserve exact handle positions from the schema
              sourceHandle = connection.towards.sourceHandle || 'output-0';
              targetHandle = connection.towards.targetHandle || 'input-0';

              console.log(`Using exact handles from schema: source=${sourceHandle}, target=${targetHandle}`);

              const newEdge = {
                id: `edge-${sourceId}-${connection.towards.default}-${connectionId}`,
                source: sourceId,
                target: connection.towards.default,
                type: 'custom',
                sourceHandle: sourceHandle,
                targetHandle: targetHandle,
                data: {
                  connectionType: 'default',
                  routeType: connection.towards.route_type || 'main',
                  edgeType: edgeType,
                  connectionId: connectionId, // Add unique connection ID
                  sourceId: sourceId,
                  targetId: connection.towards.default
                },
              };

              // Log the edge data for debugging
              console.log(`Created edge with type: ${edgeType}`, newEdge.data);

              newEdges.push(newEdge);
              console.log('Added default edge:', newEdge);
            }

            // Process conditional connections
            if (connection.towards.conditions && Array.isArray(connection.towards.conditions)) {
              console.log(`Processing ${connection.towards.conditions.length} conditional connections for ${sourceId}`);

              connection.towards.conditions.forEach((condition: { target: string; route_type?: string; edge_type?: string; operator?: string; left?: { path?: string }; right?: { value?: any }; sourceHandle?: string; targetHandle?: string }) => {
                if (condition.target) {
                  // Skip if target node doesn't exist
                  if (!nodeIds.includes(condition.target)) {
                    console.warn(`Target node ${condition.target} not found, skipping conditional connection`);
                    return;
                  }

                  // Generic handling for critical connections
                  // Check if this node has both rework and conditional connections
                  if (connection.towards && connection.towards.default &&
                    connection.towards.route_type === 'rework') {
                    console.log(`Found critical connection from ${sourceId} to ${condition.target} - ensuring it is preserved`);
                    console.log(`Node ${sourceId} has both rework and conditional connections - using different handles`);
                    // We'll use different handles for different connection types
                  }

                  console.log(`Creating conditional edge from ${sourceId} to ${condition.target}`);
                  console.log(`Condition edge type: ${condition.edge_type || 'bezier'}`);

                  // Determine source and target handles
                  let sourceHandle = condition.sourceHandle || 'output-2';
                  let targetHandle = condition.targetHandle || 'input-0';

                  // If handles are missing, assign default handles based on node positions
                  if (!sourceHandle || !targetHandle) {
                    const sourceNode = newNodes.find(n => n.id === sourceId);
                    const targetNode = newNodes.find(n => n.id === condition.target);

                    if (sourceNode && targetNode) {
                      // Determine relative positions to choose appropriate handles
                      const dx = targetNode.position.x - sourceNode.position.x;
                      const dy = targetNode.position.y - sourceNode.position.y;

                      // Choose source handle based on direction
                      if (Math.abs(dx) > Math.abs(dy)) {
                        // Horizontal connection
                        sourceHandle = dx > 0 ? 'output-0' : 'output-1';
                      } else {
                        // Vertical connection
                        sourceHandle = dy > 0 ? 'output-2' : 'output-3';
                      }

                      // Choose target handle based on direction
                      if (Math.abs(dx) > Math.abs(dy)) {
                        // Horizontal connection
                        targetHandle = dx > 0 ? 'input-0' : 'input-1';
                      } else {
                        // Vertical connection
                        targetHandle = dy > 0 ? 'input-2' : 'input-3';
                      }

                      console.log(`Assigned handles for conditional edge: source=${sourceHandle}, target=${targetHandle}`);
                    } else {
                      // Fallback to default handles
                      sourceHandle = sourceHandle || 'output-0';
                      targetHandle = targetHandle || 'input-0';
                    }
                  }

                  // Get the edge type from the condition
                  let edgeType = condition.edge_type || 'bezier';

                  // Ensure the edge type is valid
                  const validEdgeTypes = ['bezier', 'straight', 'step', 'smoothstep', 'cross'];
                  if (!validEdgeTypes.includes(edgeType)) {
                    console.warn(`Invalid conditional edge type: ${edgeType}, defaulting to bezier`);
                    edgeType = 'bezier';
                  }

                  console.log(`Using edge type: ${edgeType} for conditional connection from ${sourceId} to ${condition.target}`);

                  // Generate a unique connection ID for this conditional edge
                  const connectionId = uuidv4().substring(0, 8);

                  // Preserve exact handle positions from the schema
                  sourceHandle = condition.sourceHandle || 'output-0';
                  targetHandle = condition.targetHandle || 'input-0';

                  console.log(`Using exact handles for conditional connection: source=${sourceHandle}, target=${targetHandle}`);

                  // For nodes with both rework and conditional connections, ensure we use different handles
                  if (connection.towards && connection.towards.default &&
                    connection.towards.route_type === 'rework') {
                    console.log(`Special handling for ${sourceId} to ${condition.target} connection, using handle: ${sourceHandle}`);
                    // Use a different handle for conditional connections if not already set
                    if (sourceHandle === 'output-0') {
                      sourceHandle = 'output-1';
                      console.log(`Assigned dedicated handle for ${sourceId} to ${condition.target}: ${sourceHandle}`);
                    }
                  }

                  console.log(`Using handle positions for connection from ${sourceId} to ${condition.target}: source=${sourceHandle}, target=${targetHandle}`);

                  // Validate target handle - it should be an input handle, not an output handle
                  // This is the only validation we'll keep to ensure connections work properly
                  if (targetHandle && targetHandle.startsWith('output-')) {
                    console.warn(`Invalid target handle ${targetHandle} for ${condition.target}, fixing it`);
                    targetHandle = targetHandle.replace('output-', 'input-');
                  }

                  // Generic handling for circular connections (e.g., node A -> node B -> node A)
                  // These are valid in manufacturing processes for rework loops
                  const isCircularConnection = condition.target === sourceId ||
                    (schema.route.connections[condition.target]?.towards?.default === sourceId) ||
                    (schema.route.connections[condition.target]?.towards?.conditions?.some((c: any) => c.target === sourceId));

                  if (isCircularConnection) {
                    console.log(`Detected circular connection between ${sourceId} and ${condition.target}`);
                    // Ensure we're using different handles for the return path to avoid conflicts
                    // For rework paths, use a different input handle than the one used for output
                    if (typeof targetHandle === 'string' &&
                      connection.towards.route_type === 'rework') {
                      targetHandle = 'input-1'; // Use a different input handle for the return path
                      console.log(`Using special handle for rework return path: ${targetHandle}`);
                    }
                  }

                  const condEdge = {
                    id: `edge-${sourceId}-${condition.target}-conditional-${connectionId}`,
                    source: sourceId,
                    target: condition.target,
                    type: 'custom',
                    sourceHandle: sourceHandle,
                    targetHandle: targetHandle,
                    data: {
                      connectionType: 'conditional',
                      routeType: condition.route_type || 'main',
                      edgeType: edgeType,
                      operator: condition.operator || 'equals',
                      conditionPath: condition.left?.path || '',
                      conditionValue: condition.right?.value !== undefined ?
                        String(condition.right.value) : '',
                      connectionId: connectionId, // Add unique connection ID
                      sourceId: sourceId,
                      targetId: condition.target
                    },
                  };

                  // Log the edge data for debugging
                  console.log(`Created conditional edge with type: ${edgeType}`, condEdge.data);

                  newEdges.push(condEdge);
                  console.log('Added conditional edge:', condEdge);
                }
              });
            }
          }
        });
      }

      // Make sure we have all expected edges
      console.log('Checking for missing edges in connections...');

      // Double-check all connections to ensure none are missing
      if (schema.route?.connections) {
        // First, handle terminal nodes (end nodes) to ensure they're properly connected
        if (schema.route.end && schema.route.end !== 'end') {
          console.log(`Ensuring terminal node ${schema.route.end} is properly connected`);
          const endNodeId = schema.route.end;

          // Check if the end node exists in the components
          if (schema.components[endNodeId]) {
            // Make sure the end node has a connection towards 'end'
            const endNodeConnection = schema.route.connections[endNodeId];
            if (!endNodeConnection) {
              console.log(`Creating missing terminal connection for end node ${endNodeId}`);
              schema.route.connections[endNodeId] = {
                towards: {
                  default: "end",
                  end: true,
                  conditions: null,
                  route_type: "main",
                  edge_type: "bezier",
                  sourceHandle: 'output-0',
                  targetHandle: 'input-0',
                  metadata: {
                    label: `Terminal connection from ${endNodeId}`,
                    description: "Final node in the manufacturing process"
                  }
                }
              };
            }
          }
        }

        // Now process all connections
        Object.entries(schema.route.connections).forEach(([sourceId, connection]: [string, any]) => {
          if (connection && connection.towards) {
            // Check if this node has a default connection that isn't to 'end'
            if (connection.towards.default && connection.towards.default !== 'end') {
              const hasDefaultEdge = newEdges.some(edge =>
                edge.source === sourceId &&
                edge.target === connection.towards.default &&
                edge.data.connectionType === 'default'
              );

              if (!hasDefaultEdge) {
                console.warn(`Missing default edge from ${sourceId} to ${connection.towards.default}. Recreating it.`);

                // Create the missing edge
                const connectionId = uuidv4().substring(0, 8);
                const edgeType = connection.towards.edge_type || 'bezier';

                // Get source and target handles
                let sourceHandle = connection.towards.sourceHandle || 'output-0';
                let targetHandle = connection.towards.targetHandle || 'input-0';

                // Check if there's already an edge using the same source handle
                const existingEdgesWithSameHandle = newEdges.filter(e =>
                  e.source === sourceId && e.sourceHandle === sourceHandle
                );

                // If there's already an edge using this handle, create a unique handle ID
                if (existingEdgesWithSameHandle.length > 0) {
                  console.log(`Handle ${sourceHandle} already in use for source ${sourceId}, creating unique handle for missing edge`);
                  // Use a different output handle if the current one is already in use
                  const outputHandles = ['output-0', 'output-1', 'output-2', 'output-3'];
                  const usedHandles = existingEdgesWithSameHandle.map(e => e.sourceHandle);
                  const availableHandles = outputHandles.filter(h => !usedHandles.includes(h));

                  if (availableHandles.length > 0) {
                    sourceHandle = availableHandles[0];
                    console.log(`Using alternative handle for missing edge: ${sourceHandle}`);
                  } else {
                    // If all handles are used, create a dynamic handle
                    sourceHandle = `output-${sourceId}-${connectionId.substring(0, 4)}`;
                    console.log(`Created dynamic handle for missing edge: ${sourceHandle}`);
                  }
                }

                // Check if there's already an edge using the same target handle
                const existingEdgesWithSameTargetHandle = newEdges.filter(e =>
                  e.target === connection.towards.default && e.targetHandle === targetHandle
                );

                // If there's already an edge using this target handle, create a unique handle ID
                if (existingEdgesWithSameTargetHandle.length > 0) {
                  console.log(`Handle ${targetHandle} already in use for target ${connection.towards.default}, creating unique handle for missing edge`);
                  // Use a different input handle if the current one is already in use
                  const inputHandles = ['input-0', 'input-1', 'input-2', 'input-3'];
                  const usedHandles = existingEdgesWithSameTargetHandle.map(e => e.targetHandle);
                  const availableHandles = inputHandles.filter(h => !usedHandles.includes(h));

                  if (availableHandles.length > 0) {
                    targetHandle = availableHandles[0];
                    console.log(`Using alternative target handle for missing edge: ${targetHandle}`);
                  } else {
                    // If all handles are used, create a dynamic handle
                    targetHandle = `input-${connection.towards.default}-${connectionId.substring(0, 4)}`;
                    console.log(`Created dynamic target handle for missing edge: ${targetHandle}`);
                  }
                }

                const newEdge = {
                  id: `edge-${sourceId}-${connection.towards.default}-${connectionId}`,
                  source: sourceId,
                  target: connection.towards.default,
                  type: 'custom',
                  sourceHandle: sourceHandle,
                  targetHandle: targetHandle,
                  data: {
                    connectionType: 'default',
                    routeType: connection.towards.route_type || 'main',
                    edgeType: edgeType,
                    connectionId: connectionId,
                    sourceId: sourceId,
                    targetId: connection.towards.default
                  },
                };

                newEdges.push(newEdge);
                console.log('Added missing default edge:', newEdge);
              }
            }

            // Check conditional connections
            if (connection.towards.conditions && Array.isArray(connection.towards.conditions)) {
              connection.towards.conditions.forEach((condition: any) => {
                if (condition.target) {
                  const hasCondEdge = newEdges.some(edge =>
                    edge.source === sourceId &&
                    edge.target === condition.target &&
                    edge.data.connectionType === 'conditional'
                  );

                  if (!hasCondEdge) {
                    console.warn(`Missing conditional edge from ${sourceId} to ${condition.target}. Recreating it.`);

                    // Create the missing conditional edge
                    const connectionId = uuidv4().substring(0, 8);
                    const edgeType = condition.edge_type || 'bezier';

                    // Get source and target handles
                    let sourceHandle = condition.sourceHandle || 'output-0';
                    let targetHandle = condition.targetHandle || 'input-0';

                    // Check if there's already an edge using the same source handle
                    // For special nodes like 'aoi', we want to allow multiple connections using the same handle
                    // if they're defined that way in the schema
                    const existingEdgesWithSameHandle = newEdges.filter(e =>
                      e.source === sourceId && e.sourceHandle === sourceHandle
                    );

                    // If there's already an edge using this handle, create a unique handle ID
                    // unless this is a special case where we want to preserve multiple connections
                    const allowMultipleConnections =
                      (sourceId === 'aoi' && connection.towards.conditions &&
                        connection.towards.conditions.some((c: any) => c.target === 'pb_depanel'));

                    if (existingEdgesWithSameHandle.length > 0 && !allowMultipleConnections) {
                      console.log(`Handle ${sourceHandle} already in use for source ${sourceId}, creating unique handle for missing conditional edge`);
                      // Use a different output handle if the current one is already in use
                      const outputHandles = ['output-0', 'output-1', 'output-2', 'output-3'];
                      const usedHandles = existingEdgesWithSameHandle.map(e => e.sourceHandle);
                      const availableHandles = outputHandles.filter(h => !usedHandles.includes(h));

                      if (availableHandles.length > 0) {
                        sourceHandle = availableHandles[0];
                        console.log(`Using alternative handle for missing conditional edge: ${sourceHandle}`);
                      } else {
                        // If all handles are used, create a dynamic handle
                        sourceHandle = `output-${sourceId}-${connectionId.substring(0, 4)}`;
                        console.log(`Created dynamic handle for missing conditional edge: ${sourceHandle}`);
                      }
                    }

                    // Check if there's already an edge using the same target handle
                    const existingEdgesWithSameTargetHandle = newEdges.filter(e =>
                      e.target === condition.target && e.targetHandle === targetHandle
                    );

                    // If there's already an edge using this target handle, create a unique handle ID
                    if (existingEdgesWithSameTargetHandle.length > 0) {
                      console.log(`Handle ${targetHandle} already in use for target ${condition.target}, creating unique handle for missing conditional edge`);
                      // Use a different input handle if the current one is already in use
                      const inputHandles = ['input-0', 'input-1', 'input-2', 'input-3'];
                      const usedHandles = existingEdgesWithSameTargetHandle.map(e => e.targetHandle);
                      const availableHandles = inputHandles.filter(h => !usedHandles.includes(h));

                      if (availableHandles.length > 0) {
                        targetHandle = availableHandles[0];
                        console.log(`Using alternative target handle for missing conditional edge: ${targetHandle}`);
                      } else {
                        // If all handles are used, create a dynamic handle
                        targetHandle = `input-${condition.target}-${connectionId.substring(0, 4)}`;
                        console.log(`Created dynamic target handle for missing conditional edge: ${targetHandle}`);
                      }
                    }

                    // Validate target handle - it should be an input handle, not an output handle
                    if (targetHandle && targetHandle.startsWith('output-')) {
                      // Silently fix the handle without warning
                      targetHandle = targetHandle.replace('output-', 'input-');
                    }

                    const condEdge = {
                      id: `edge-${sourceId}-${condition.target}-conditional-${connectionId}`,
                      source: sourceId,
                      target: condition.target,
                      type: 'custom',
                      sourceHandle: sourceHandle,
                      targetHandle: targetHandle,
                      data: {
                        connectionType: 'conditional',
                        routeType: condition.route_type || 'main',
                        edgeType: edgeType,
                        operator: condition.operator || 'equals',
                        conditionPath: condition.left?.path || '',
                        conditionValue: condition.right?.value !== undefined ?
                          String(condition.right.value) : '',
                        connectionId: connectionId,
                        sourceId: sourceId,
                        targetId: condition.target
                      },
                    };

                    newEdges.push(condEdge);
                    console.log('Added missing conditional edge:', condEdge);
                  }
                }
              });
            }
          }
        });
      }

      // Check for any conditional connections that might have incorrect handle types
      newEdges.forEach(edge => {
        if (edge.data.connectionType === 'conditional') {
          // Target handle should be an input handle, not an output handle
          if (edge.targetHandle && edge.targetHandle.startsWith('output-')) {
            // Silently fix the handle without warning
            edge.targetHandle = edge.targetHandle.replace('output-', 'input-');
          }
        }
      });

      // Final validation: check if all expected connections exist
      // Perform final validation without verbose logging

      // Build a list of all expected connections from the schema
      const expectedConnections: Array<{ source: string, target: string, type: string }> = [];

      // Add all connections from the schema to the expected list
      if (schema.route?.connections) {
        Object.entries(schema.route.connections).forEach(([sourceId, connection]: [string, any]) => {
          if (connection && connection.towards) {
            // Add default connection to expected list if it's not to 'end'
            if (connection.towards.default && connection.towards.default !== 'end') {
              expectedConnections.push({
                source: sourceId,
                target: connection.towards.default,
                type: 'default'
              });
              // Connection tracking
            }

            // Add conditional connections to expected list
            if (connection.towards.conditions && Array.isArray(connection.towards.conditions)) {
              connection.towards.conditions.forEach((condition: any) => {
                if (condition.target) {
                  expectedConnections.push({
                    source: sourceId,
                    target: condition.target,
                    type: 'conditional'
                  });
                  // Conditional connection tracking
                }
              });
            }
          }
        });
      }

      // Check each expected connection
      expectedConnections.forEach((expected: { source: string, target: string, type: string }) => {
        const connectionExists = newEdges.some(edge =>
          edge.source === expected.source &&
          edge.target === expected.target &&
          edge.data.connectionType === expected.type
        );

        if (!connectionExists) {
          console.warn(`Missing expected ${expected.type} connection from ${expected.source} to ${expected.target}. Creating it.`);

          // Get the connection details from the schema
          const sourceConnection = schema.route?.connections[expected.source];
          if (!sourceConnection) {
            console.error(`Cannot find source connection details for ${expected.source}`);
            return;
          }

          // Generate a unique connection ID
          const connectionId = uuidv4().substring(0, 8);

          // Determine source and target handles
          let sourceHandle = 'output-0';
          let targetHandle = 'input-0';

          // For default connections, get handle info from the schema
          if (expected.type === 'default' && sourceConnection.towards) {
            sourceHandle = sourceConnection.towards.sourceHandle || 'output-0';
            targetHandle = sourceConnection.towards.targetHandle || 'input-0';
          }
          // For conditional connections, find the specific condition
          else if (expected.type === 'conditional' &&
            sourceConnection.towards &&
            sourceConnection.towards.conditions) {
            const condition = sourceConnection.towards.conditions.find(
              (c: any) => c.target === expected.target
            );
            if (condition) {
              sourceHandle = condition.sourceHandle || 'output-0';
              targetHandle = condition.targetHandle || 'input-0';

              // Ensure target handle is an input handle
              if (targetHandle && targetHandle.startsWith('output-')) {
                targetHandle = targetHandle.replace('output-', 'input-');
              }
            }
          }

          // Check for handle collisions
          const existingEdgesWithSameSourceHandle = newEdges.filter(e =>
            e.source === expected.source && e.sourceHandle === sourceHandle
          );

          if (existingEdgesWithSameSourceHandle.length > 0) {
            const outputHandles = ['output-0', 'output-1', 'output-2', 'output-3'];
            const usedHandles = existingEdgesWithSameSourceHandle.map(e => e.sourceHandle);
            const availableHandles = outputHandles.filter(h => !usedHandles.includes(h));

            if (availableHandles.length > 0) {
              sourceHandle = availableHandles[0];
            } else {
              sourceHandle = `output-${expected.source}-${connectionId.substring(0, 4)}`;
            }
          }

          // Check for target handle collisions
          const existingEdgesWithSameTargetHandle = newEdges.filter(e =>
            e.target === expected.target && e.targetHandle === targetHandle
          );

          if (existingEdgesWithSameTargetHandle.length > 0) {
            const inputHandles = ['input-0', 'input-1', 'input-2', 'input-3'];
            const usedHandles = existingEdgesWithSameTargetHandle.map(e => e.targetHandle);
            const availableHandles = inputHandles.filter(h => !usedHandles.includes(h));

            if (availableHandles.length > 0) {
              targetHandle = availableHandles[0];
            } else {
              targetHandle = `input-${expected.target}-${connectionId.substring(0, 4)}`;
            }
          }

          // Create the missing edge
          if (expected.type === 'default') {
            const routeType = sourceConnection.towards.route_type || 'main';
            const edgeType = sourceConnection.towards.edge_type || 'bezier';

            const newEdge = {
              id: `edge-${expected.source}-${expected.target}-${connectionId}`,
              source: expected.source,
              target: expected.target,
              type: 'custom',
              sourceHandle: sourceHandle,
              targetHandle: targetHandle,
              data: {
                connectionType: 'default',
                routeType: routeType,
                edgeType: edgeType,
                connectionId: connectionId,
                sourceId: expected.source,
                targetId: expected.target
              },
            };

            newEdges.push(newEdge);
            console.log(`Created missing default edge from ${expected.source} to ${expected.target}`);
          }
          else if (expected.type === 'conditional') {
            // Find the condition details
            const condition = sourceConnection.towards.conditions.find(
              (c: any) => c.target === expected.target
            );

            if (condition) {
              const routeType = condition.route_type || 'main';
              const edgeType = condition.edge_type || 'bezier';

              const condEdge = {
                id: `edge-${expected.source}-${expected.target}-conditional-${connectionId}`,
                source: expected.source,
                target: expected.target,
                type: 'custom',
                sourceHandle: sourceHandle,
                targetHandle: targetHandle,
                data: {
                  connectionType: 'conditional',
                  routeType: routeType,
                  edgeType: edgeType,
                  operator: condition.operator || 'equals',
                  conditionPath: condition.left?.path || '',
                  conditionValue: condition.right?.value !== undefined ?
                    String(condition.right.value) : '',
                  connectionId: connectionId,
                  sourceId: expected.source,
                  targetId: expected.target
                },
              };

              newEdges.push(condEdge);
              console.log(`Created missing conditional edge from ${expected.source} to ${expected.target}`);
            }
          }
        }
      });

      // Check for terminal nodes (end nodes) that might be missing connections
      const endNodeId = schema.route.end;
      if (endNodeId && endNodeId !== 'end') {
        const endNode = newNodes.find(node => node.id === endNodeId);
        if (endNode) {
          // Check if the end node has any outgoing edges
          const hasTerminalEdge = newEdges.some(edge => edge.source === endNodeId && edge.data.connectionType === 'default');

          if (!hasTerminalEdge) {
            console.log(`Terminal node ${endNodeId} is missing its terminal connection. Creating it.`);

            // Create a terminal edge for the end node
            const connectionId = uuidv4().substring(0, 8);
            const terminalEdge = {
              id: `edge-${endNodeId}-end-${connectionId}`,
              source: endNodeId,
              target: 'end',
              type: 'custom',
              sourceHandle: 'output-0',
              targetHandle: 'input-0',
              data: {
                connectionType: 'default',
                routeType: 'main',
                edgeType: 'bezier',
                connectionId: connectionId,
                sourceId: endNodeId,
                targetId: 'end',
                isTerminal: true
              },
            };

            newEdges.push(terminalEdge);
            console.log('Added terminal edge for end node:', terminalEdge);
          }
        }
      }

      // Final validation for all connections in the schema
      console.log('Performing final validation for all connections...');

      // Generic validation for all connections
      console.log('Performing generic validation for all connections...');

      // Check for nodes with dual connections (both default and conditional to the same target)
      Object.entries(schema.route?.connections || {}).forEach(([sourceId, connection]: [string, any]) => {
        if (!connection?.towards) return;

        // Skip if source node doesn't exist
        if (!nodeIds.includes(sourceId)) return;

        // Check for dual connections (default and conditional to same target)
        if (connection.towards.default && connection.towards.default !== 'end' &&
          connection.towards.conditions && Array.isArray(connection.towards.conditions)) {

          // Find any conditional connections that target the same node as the default connection
          const dualTargets = connection.towards.conditions
            .filter((c: any) => c.target === connection.towards.default)
            .map((c: any) => c.target);

          if (dualTargets.length > 0) {
            const targetId = connection.towards.default;
            console.log(`Node ${sourceId} has both default and conditional connections to ${targetId} - special handling required`);

            // Check if we need to create both connections
            const hasDefaultConnection = newEdges.some(edge =>
              edge.source === sourceId &&
              edge.target === targetId &&
              edge.data.connectionType === 'default'
            );

            const hasConditionalConnection = newEdges.some(edge =>
              edge.source === sourceId &&
              edge.target === targetId &&
              edge.data.connectionType === 'conditional'
            );

            // Create default connection if missing
            if (!hasDefaultConnection) {
              console.log(`Creating missing default connection from ${sourceId} to ${targetId}`);
              const connectionId = uuidv4().substring(0, 8);
              const sourceHandle = connection.towards.sourceHandle || 'output-1';
              const targetHandle = connection.towards.targetHandle || 'input-1';

              const defaultEdge = {
                id: `edge-${sourceId}-${targetId}-${connectionId}`,
                source: sourceId,
                target: targetId,
                type: 'custom',
                sourceHandle: sourceHandle,
                targetHandle: targetHandle,
                data: {
                  connectionType: 'default',
                  routeType: connection.towards.route_type || 'main',
                  edgeType: connection.towards.edge_type || 'bezier',
                  connectionId: connectionId,
                  sourceId: sourceId,
                  targetId: targetId
                },
              };

              newEdges.push(defaultEdge);
              console.log(`Added default connection from ${sourceId} to ${targetId}`);
            }

            // Create conditional connection if missing
            if (!hasConditionalConnection) {
              console.log(`Creating missing conditional connection from ${sourceId} to ${targetId}`);
              const conditionDetails = connection.towards.conditions.find((c: any) => c.target === targetId);

              if (conditionDetails) {
                const connectionId = uuidv4().substring(0, 8);
                const sourceHandle = conditionDetails.sourceHandle || 'output-2';
                const targetHandle = conditionDetails.targetHandle || 'input-0';

                const conditionalEdge = {
                  id: `edge-${sourceId}-${targetId}-conditional-${connectionId}`,
                  source: sourceId,
                  target: targetId,
                  type: 'custom',
                  sourceHandle: sourceHandle,
                  targetHandle: targetHandle,
                  data: {
                    connectionType: 'conditional',
                    routeType: conditionDetails.route_type || 'main',
                    edgeType: conditionDetails.edge_type || 'bezier',
                    operator: conditionDetails.operator || 'equals',
                    conditionPath: conditionDetails.left?.path || 'status',
                    conditionValue: conditionDetails.right?.value !== undefined ?
                      String(conditionDetails.right.value) : 'pass',
                    connectionId: connectionId,
                    sourceId: sourceId,
                    targetId: targetId
                  },
                };

                newEdges.push(conditionalEdge);
                console.log(`Added conditional connection from ${sourceId} to ${targetId}`);
              }
            }
          }
        }

        // Check for circular connections (rework loops)
        Object.entries(schema.route?.connections || {}).forEach(([targetId, targetConnection]: [string, any]) => {
          if (!targetConnection?.towards) return;

          // Skip if target node doesn't exist
          if (!nodeIds.includes(targetId)) return;

          // Check if source points to target and target points back to source (circular)
          const sourcePointsToTarget =
            connection.towards.default === targetId ||
            connection.towards.conditions?.some((c: any) => c.target === targetId);

          const targetPointsToSource =
            targetConnection.towards.default === sourceId ||
            targetConnection.towards.conditions?.some((c: any) => c.target === sourceId);

          if (sourcePointsToTarget && targetPointsToSource) {
            console.log(`Found circular connection between ${sourceId} and ${targetId}`);

            // Check if the circular connection exists in both directions
            const hasSourceToTargetEdge = newEdges.some(edge =>
              edge.source === sourceId && edge.target === targetId
            );

            const hasTargetToSourceEdge = newEdges.some(edge =>
              edge.source === targetId && edge.target === sourceId
            );

            // Create the missing part of the circular connection if needed
            if (!hasTargetToSourceEdge && targetConnection.towards) {
              console.log(`Creating missing circular connection from ${targetId} back to ${sourceId}`);

              // Find the connection details
              const isDefaultConnection = targetConnection.towards.default === sourceId;
              const connectionDetails = isDefaultConnection ?
                targetConnection.towards :
                targetConnection.towards.conditions?.find((c: any) => c.target === sourceId);

              if (connectionDetails) {
                const connectionId = uuidv4().substring(0, 8);
                const sourceHandle = connectionDetails.sourceHandle || 'output-0';
                // Always use an input handle for the target, even if schema specifies output
                let targetHandle = connectionDetails.targetHandle || 'input-1';
                if (targetHandle.startsWith('output-')) {
                  targetHandle = targetHandle.replace('output-', 'input-');
                }

                const circularEdge = {
                  id: `edge-${targetId}-${sourceId}-${isDefaultConnection ? 'default' : 'conditional'}-${connectionId}`,
                  source: targetId,
                  target: sourceId,
                  type: 'custom',
                  sourceHandle: sourceHandle,
                  targetHandle: targetHandle,
                  data: {
                    connectionType: isDefaultConnection ? 'default' : 'conditional',
                    routeType: connectionDetails.route_type || 'main',
                    edgeType: connectionDetails.edge_type || 'bezier',
                    operator: connectionDetails.operator || 'equals',
                    conditionPath: connectionDetails.left?.path || 'condition_path_placeholder',
                    conditionValue: connectionDetails.right?.value !== undefined ?
                      String(connectionDetails.right.value) : 'condition_value_placeholder',
                    connectionId: connectionId,
                    sourceId: targetId,
                    targetId: sourceId
                  },
                };

                newEdges.push(circularEdge);
                console.log(`Added circular connection from ${targetId} back to ${sourceId}`);
              }
            }

            // Create the other direction if missing
            if (!hasSourceToTargetEdge) {
              console.log(`Creating missing circular connection from ${sourceId} to ${targetId}`);

              // Find the connection details
              const isDefaultConnection = connection.towards.default === targetId;
              const connectionDetails = isDefaultConnection ?
                connection.towards :
                connection.towards.conditions?.find((c: any) => c.target === targetId);

              if (connectionDetails) {
                const connectionId = uuidv4().substring(0, 8);
                const sourceHandle = connectionDetails.sourceHandle || 'output-0';
                const targetHandle = connectionDetails.targetHandle || 'input-0';

                const circularEdge = {
                  id: `edge-${sourceId}-${targetId}-${isDefaultConnection ? 'default' : 'conditional'}-${connectionId}`,
                  source: sourceId,
                  target: targetId,
                  type: 'custom',
                  sourceHandle: sourceHandle,
                  targetHandle: targetHandle,
                  data: {
                    connectionType: isDefaultConnection ? 'default' : 'conditional',
                    routeType: connectionDetails.route_type || 'main',
                    edgeType: connectionDetails.edge_type || 'bezier',
                    connectionId: connectionId,
                    sourceId: sourceId,
                    targetId: targetId
                  },
                };

                newEdges.push(circularEdge);
                console.log(`Added circular connection from ${sourceId} to ${targetId}`);
              }
            }
          }
        });
      });

      // Check all connections defined in the schema
      if (schema.route?.connections) {
        Object.entries(schema.route.connections).forEach(([sourceId, connection]: [string, any]) => {
          if (connection && connection.towards) {
            // Check default connection
            if (connection.towards.default && connection.towards.default !== 'end') {
              const hasDefaultEdge = newEdges.some(edge =>
                edge.source === sourceId &&
                edge.target === connection.towards.default &&
                edge.data.connectionType === 'default'
              );

              if (!hasDefaultEdge && nodeIds.includes(sourceId) && nodeIds.includes(connection.towards.default)) {
                console.warn(`Missing default connection from ${sourceId} to ${connection.towards.default}! Creating it manually...`);

                // Create the missing default edge with exact handles from schema
                const connectionId = uuidv4().substring(0, 8);
                // Use the connection handles or default ones
                const sourceHandle = connection.towards.sourceHandle || 'output-0';
                const targetHandle = connection.towards.targetHandle || 'input-0';

                const newEdge = {
                  id: `edge-${sourceId}-${connection.towards.default}-${connectionId}`,
                  source: sourceId,
                  target: connection.towards.default,
                  type: 'custom',
                  sourceHandle: sourceHandle,
                  targetHandle: targetHandle,
                  data: {
                    connectionType: 'default',
                    routeType: connection.towards.route_type || 'main',
                    edgeType: connection.towards.edge_type || 'bezier',
                    connectionId: connectionId,
                    sourceId: sourceId,
                    targetId: connection.towards.default
                  },
                };

                newEdges.push(newEdge);
                console.log(`Manually added default edge from ${sourceId} to ${connection.towards.default}`);
              }
            }

            // Check conditional connections
            if (connection.towards.conditions && Array.isArray(connection.towards.conditions)) {
              connection.towards.conditions.forEach((condition: any) => {
                if (condition.target) {
                  const hasCondEdge = newEdges.some(edge =>
                    edge.source === sourceId &&
                    edge.target === condition.target &&
                    edge.data.connectionType === 'conditional'
                  );

                  if (!hasCondEdge && nodeIds.includes(sourceId) && nodeIds.includes(condition.target)) {
                    console.warn(`Missing conditional connection from ${sourceId} to ${condition.target}! Creating it manually...`);

                    // Create the missing conditional edge with exact handles from schema
                    const connectionId = uuidv4().substring(0, 8);
                    let condSourceHandle = condition.sourceHandle || 'output-0';
                    let condTargetHandle = condition.targetHandle || 'input-0';

                    const condEdge = {
                      id: `edge-${sourceId}-${condition.target}-conditional-${connectionId}`,
                      source: sourceId,
                      target: condition.target,
                      type: 'custom',
                      sourceHandle: condSourceHandle,
                      targetHandle: condTargetHandle,
                      data: {
                        connectionType: 'conditional',
                        routeType: condition.route_type || 'main',
                        edgeType: condition.edge_type || 'bezier',
                        operator: condition.operator || 'equals',
                        conditionPath: condition.left?.path || '',
                        conditionValue: condition.right?.value !== undefined ? String(condition.right.value) : '',
                        connectionId: connectionId,
                        sourceId: sourceId,
                        targetId: condition.target
                      },
                    };

                    newEdges.push(condEdge);
                    console.log(`Manually added conditional edge from ${sourceId} to ${condition.target}`);
                  }
                }
              });
            }
          }
        });
      }

      // Final verification of all edges
      console.log(`Final edge count: ${newEdges.length}`);
      newEdges.forEach(edge => {
        console.log(`Edge from ${edge.source} to ${edge.target} (${edge.data.connectionType}, ${edge.data.routeType})`);
      });

      // Update the state with new nodes and edges
      console.log('Setting nodes:', newNodes);
      console.log('Setting edges:', newEdges);

      // Use a callback to ensure we're using the latest state
      setNodes(newNodes);
      setEdges(newEdges);


      // Always fit view after import, regardless of read-only mode
      // Use a slightly longer timeout for read-only mode to ensure all nodes are properly rendered
      setTimeout(() => {
        if (reactFlowInstance) {
          console.log('Fitting view after schema import');
          if (readOnly) {
            // For read-only mode, position at top-left corner with minimal zoom
            fitView({
              padding: 0.05,
              duration: 800,
              minZoom: 0.1,
              maxZoom: 0.8
            });
            // After fitView, position the viewport at top-left corner
            setTimeout(() => {
              reactFlowInstance.setViewport({ x: 0, y: 0, zoom: reactFlowInstance.getZoom() });
            }, 900);
          } else {
            // For edit mode, use standard fitView
            fitView({ padding: 0.2, duration: 800 });
          }
        } else {
          console.warn('ReactFlow instance not available for fitView');
        }
      }, readOnly ? 800 : 500);

      if (!readOnly) {
        const payload = createRoutingPayload(
          schema,
          importData.name || configName || 'Imported Routing Configuration',
          importData.code || configCode || (configName || 'imported_routing').toLowerCase().replace(/\s+/g, '_')
        );

        console.log('Created import payload:', payload);

        // Store the payload for later saving
        setCurrentPayload(payload);
        // Show success message
        showSnackbar("Schema imported successfully! Click Save to save it to the server.", "success");
        // Close the import modal
        setImportModalOpen(false);
        setImportedJson('');
      }


      // Longer timeout to ensure everything is rendered

    } catch (error) {
      console.error('Error importing JSON:', error);
      alert(`Error importing JSON: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };



  // Handle connection start
  const onConnectStart = useCallback((_event: React.MouseEvent | React.TouchEvent, params: any) => {
    // Log the connection start for debugging
    console.log('Connection start:', params);
  }, []);

  // Handle connection end
  const onConnectEnd = useCallback((_event: MouseEvent | TouchEvent) => {
    // Log the connection end for debugging
    console.log('Connection end');
  }, []);



  // Toggle dark mode
  const toggleDarkMode = useCallback(() => {
    setDarkMode(!darkMode);
  }, [darkMode]);

  // Handle saving the schema to the API
  const handleSaveSchema = async () => {
    // Validate if we have a payload to save
    if (!currentPayload) {
      showSnackbar("No schema to save. Please generate or import a schema first.", "error");
      return;
    }

    // Validate required fields
    if (!configName) {
      showSnackbar("Please enter a configuration name before saving.", "error");
      return;
    }

    if (!configCode) {
      showSnackbar("Please enter a configuration code before saving.", "error");
      return;
    }

    // Set loading state
    setIsSaving(true);

    try {
      console.log('Saving schema to API...');
      console.log('Payload being sent to API:', JSON.stringify(currentPayload, null, 2));

      // Execute the mutation
      const response = await axiosInstance.post('/workflow/api/routings/', currentPayload);
      console.log('API response:', response);

      // Show success message
      showSnackbar("Schema saved successfully!", "success");

      // Reset the current payload
      setCurrentPayload(null);
    } catch (error: any) {
      console.error('Failed to save schema:', error);
      console.error('Error details:', error.response?.data);

      // Check for duplicate code error
      if (error?.response?.data?.code &&
        error.response.data.code.includes('routing with this code already exists')) {
        // Show specific error for duplicate code
        showSnackbar('Routing with this code already exists. Please use a different code.', 'error');
      } else {
        // Show generic error
        showSnackbar(error?.message || 'Failed to save schema', 'error');
      }
    } finally {
      setIsSaving(false);
    }
  };

  useEffect(() => {
    if (schema) {
      handleImportJson(schema);
    }
  }, [schema]);

  // Add a specific effect to handle fitView in read-only mode when nodes change
  useEffect(() => {
    if (readOnly && nodes.length > 0 && reactFlowInstance) {
      // Use a timeout to ensure the nodes are properly rendered
      setTimeout(() => {
        console.log('Fitting view for read-only mode with', nodes.length, 'nodes');
        // Use minimal zoom to show complete workflow
        fitView({
          padding: 0.05,
          duration: 800,
          minZoom: 0.1,
          maxZoom: 0.8,
          includeHiddenNodes: false
        });
        // After fitView, position the viewport at top-left corner
        setTimeout(() => {
          reactFlowInstance.setViewport({ x: 0, y: 0, zoom: reactFlowInstance.getZoom() });
        }, 900);
      }, 600);
    }
  }, [readOnly, nodes, reactFlowInstance, fitView]);

  // Add effect to handle initial ReactFlow instance setup for read-only mode
  useEffect(() => {
    if (readOnly && reactFlowInstance) {
      // Initial setup when ReactFlow instance becomes available
      setTimeout(() => {
        console.log('Initial top-left positioning for read-only mode');
        // Position at top-left corner with current zoom level
        reactFlowInstance.setViewport({ x: 0, y: 0, zoom: reactFlowInstance.getZoom() });
      }, 1000);
    }
  }, [readOnly, reactFlowInstance]);

  return (
    <DndProvider backend={HTML5Backend}>
      <div className={`app-container ${darkMode ? 'dark-mode' : 'light-mode'}`}>

        {/* Header */}
        {!readOnly &&
          <RoutingHeader
            title="Routing Configuration"
            onAddWorkstation={() => setAddWorkstationModalOpen(true)}
          />
        }

        {/* Left Sidebar (Node Palette) */}
        {!readOnly &&
          <LeftSidebar
            collapsed={leftSidebarCollapsed}
            setCollapsed={setLeftSidebarCollapsed}
            processBlocks={processBlocks}
            onAddWorkstation={() => setAddWorkstationModalOpen(true)}
          />
        }

        {/* Main Canvas */}
        <FlowCanvas
          nodes={nodes}
          edges={edges}
          readonly={readOnly}
          onNodesChange={readOnly ? undefined : onNodesChange}
          onEdgesChange={readOnly ? undefined : onEdgesChange}
          onConnect={readOnly ? undefined : onConnect}
          onInit={setReactFlowInstance} /* Always set reactFlowInstance even in read-only mode */
          onDrop={readOnly ? undefined : onDrop}
          onDragOver={readOnly ? undefined : onDragOver}
          onEdgeClick={readOnly ? undefined : onEdgeClick}
          onNodeClick={readOnly ? undefined : onNodeClick}
          onPaneClick={readOnly ? undefined : onPaneClick}
          onConnectStart={readOnly ? undefined : onConnectStart}
          onConnectEnd={readOnly ? undefined : onConnectEnd}
          nodeTypes={nodeTypes}
          edgeTypes={edgeTypes}
          isValidConnection={readOnly ? undefined : isValidConnection}
          selectedEdge={selectedEdge}
          editSelectedEdge={readOnly ? undefined : editSelectedEdge}
          deleteSelectedEdge={readOnly ? undefined : deleteSelectedEdge}
          darkMode={darkMode}
          toggleDarkMode={readOnly ? undefined : toggleDarkMode}
          reactFlowWrapper={reactFlowWrapper}
          leftSidebarCollapsed={leftSidebarCollapsed}
          rightSidebarCollapsed={rightSidebarCollapsed}
        />

        {/* Right Sidebar (Configuration) */}
        {!readOnly &&
          <RightSidebar
            collapsed={rightSidebarCollapsed}
            setCollapsed={setRightSidebarCollapsed}
            configName={configName}
            configCode={configCode}
            setConfigName={setConfigName}
            setConfigCode={setConfigCode}
            nodeOptions={nodeOptions}
            startNodeId={startNodeId}
            endNodeId={endNodeId}
            setStartNodeId={setStartNodeId}
            setEndNodeId={setEndNodeId}
            onGenerateJson={handleGenerateJson}
            onImportClick={() => setImportModalOpen(true)}
            onSaveSchema={handleSaveSchema}
            isSaving={isSaving}
            hasUnsavedChanges={currentPayload !== null}
          />
        }

        {/* Connection Configuration Modal */}
        {!readOnly &&
          <ConnectionHandler
            modalOpen={modalOpen}
            currentEdgeParams={currentEdgeParams}
            isEditingEdge={isEditingEdge}
            selectedEdge={selectedEdge}
            onClose={() => {
              setModalOpen(false);
              setCurrentEdgeParams(null);
              setIsEditingEdge(false);
            }}
            onSave={handleSaveConnection}
          />
        }

        {/* JSON Import Modal */}
        {!readOnly &&
          <ImportModalComponent
            isOpen={importModalOpen}
            importedJson={importedJson}
            setImportedJson={setImportedJson}
            onImport={() => handleImportJson()}
            onClose={() => {
              setImportModalOpen(false);
              setImportedJson('');

            }}
          />
        }

        {/* JSON Export Modal */}
        {!readOnly &&
          <ExportModalComponent
            isOpen={exportModalOpen}
            exportedJson={exportedJson}
            configName={configName}
            configCode={configCode}
            onClose={() => setExportModalOpen(false)}
          />
        }

        {/* Add Workstation Modal */}
        {!readOnly &&
          <AddProcessBlockModal
            open={addWorkstationModalOpen}
            onClose={() => {
              setAddWorkstationModalOpen(false);
              // No need to manually refetch as the invalidation in the mutation will handle it
            }}
          />
        }
      </div>
    </DndProvider>
  );
}

// Wrap the Flow component with the required providers
function RouteConfigCreation({ schema, readOnly }: { schema?: any, readOnly?: boolean }) {
  return (
    <DndProvider backend={HTML5Backend}>
      <ReactFlowProvider>
        <Flow schema={schema} readOnly={readOnly} />
      </ReactFlowProvider>
    </DndProvider>
  );
}

export default RouteConfigCreation;
