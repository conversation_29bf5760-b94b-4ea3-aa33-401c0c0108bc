import React, { useEffect, useMemo, useState } from "react";
import styles from "../styles/Dashboard.module.scss"; // Adjust the path as needed
import { useDashboardData, useTabsData } from "../hooks/useChartData";
import {
  CircularProgress,
  Alert,
  Box,
  Typography,
  Button,
} from "@mui/material";
import Chart from "../components/Dashboard/Chart";
import GlobalFilters from "../components/Dashboard/GlobalFilters";
import GlobalProgressBar from "../components/GlobalProgressBar";

function Dashboard() {
  const [tabValue, setTabValue] = useState(0);
  const {
    data: tabsData,
    isLoading: isLoadingTabs,
    error: errorTabs,
  } = useTabsData();

  const [filterState, setFilterState] = useState({});

  const handleApplyFilters = (newFilters: any) => {
    setFilterState(newFilters);
    // Add your filter logic here
  };

  const handleResetFilters = () => {
    setFilterState({});
    // Add your reset logic here
  };

  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);




  const {
    data: dashboardData,
    isLoading: isLoadingDashboard,
    error: errorDashboard,
  } = useDashboardData(tabsData?.[tabValue]?.id);

  const gridStyles = {
    display: "grid",
    gridTemplateColumns: isMobile 
      ? `repeat(${dashboardData?.layout?.mobileColumns || 1}, 1fr)` 
      : `repeat(${dashboardData?.layout?.desktopColumns || 4}, 1fr)`,
  };

  const layout = useMemo(() => {
    if (!dashboardData?.layout) return [];

    return isMobile
      ? dashboardData.layout.mobile || []
      : dashboardData.layout.desktop || [];
  }, [isMobile, dashboardData]);

  useEffect(() => {
    if (tabsData) {
      setTabValue(0);
    }
  }, [tabsData]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const renderLoading = () => (
    <div className={styles.loadingContainer}>
      <CircularProgress />
    </div>
  );

  const renderError = (error: any) => (
    <div className={styles.errorContainer}>
      <Alert severity="error">
        Error loading Dashboard:{" "}
        {error instanceof Error ? error.message : "Unknown error"}
      </Alert>
    </div>
  );

  if (isLoadingTabs) return renderLoading();

  if (errorTabs || errorDashboard) return renderError(errorTabs);

  return (
    <div className={styles.dashboard}>
      <GlobalProgressBar />
      <Box sx={{ display: "flex" }}>
        <Box
          sx={{
            borderBottom: 1,
            borderColor: "divider",
            display: "flex",
            alignItems: "center",
            gap: 2,
            pb: 1,
            position: "relative",
            "&::after": {
              content: '""',
              position: "absolute",
              right: 0,
              top: 0,
              height: "100%",
              width: "32px",
              background: "linear-gradient(to right, transparent, #ffffff)",
              pointerEvents: "none",
            },
          }}
        >
          <Typography
            variant="h5"
            sx={{
              minWidth: "fit-content",
              fontWeight: 600,
              pr: 3,
              borderRight: "1px solid",
              borderColor: "divider",
            }}
          >
            Manufacturing Dashboards
          </Typography>

          <Box
            sx={{
              display: "flex",
              gap: 1,
              overflowX: "auto",
              flexGrow: 1,
              paddingRight: "16px", // Adjust this value to match the width of the ::after pseudo-element
              "::-webkit-scrollbar": {
                height: "0px", // Increased height for better visibility
              },
              "::-webkit-scrollbar-track": {
                background: "transparent", // Removed background when not selected
              },
              "::-webkit-scrollbar-thumb": {
                background: "#e0e0e0", // Lighter color for thumb
                borderRadius: "2px", // Minimal rounded borders
              },
              "::-webkit-scrollbar-thumb:hover": {
                background: "#bdbdbd", // Slightly darker on hover
              },
            }}
          >
            {tabsData?.map((tab: any, ind: number) => (
              <Button
                key={tab.id}
                variant="outlined"
                size="small"
                sx={{
                  minWidth: "fit-content",
                  borderRadius: "5px",
                  textTransform: "none",
                  px: 2,
                  whiteSpace: "nowrap",
                  backgroundColor:
                    tabValue === ind ? "primary.main" : "transparent",
                  color: tabValue === ind ? "white" : "primary.main",
                  "&:hover": {
                    backgroundColor: "primary.dark",
                    color: "white",
                  },
                }}
                onClick={(e) => handleTabChange(e, ind)}
              >
                {tab.name}
              </Button>
            ))}
          </Box>
        </Box>
        <GlobalFilters
          filterState={filterState}
          onApplyFilters={handleApplyFilters}
          onResetFilters={handleResetFilters}
        />
      </Box>

      {isLoadingDashboard && renderLoading()}
      {errorDashboard && renderError(errorDashboard)}

      <div style={gridStyles}>
        {layout &&
          layout.map((item) => {
            return (
              <div
                key={item.id}
                style={{
                  gridColumnStart: item.col,
                  gridColumnEnd: `span ${item.colspan || 1}`,
                  gridRowStart: item.row,
                  gridRowEnd: `span ${item.rowspan || 1}`,
                  // gridColumn: `span ${item?.colspan || 1}`,
                  backgroundColor: "#fff",
                  padding: 10,
                  borderRadius: 8,
                  minHeight: 100,
                }}
              >
                <Chart id={item.id} />
              </div>
            );
          })}
      </div>
    </div>
  );
}

export default Dashboard;
