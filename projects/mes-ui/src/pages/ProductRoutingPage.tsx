import React, { useState, useEffect } from "react";
import React<PERSON>low, {
  Background,
  Node,
  Edge,
  Controls,
} from "reactflow";
import "reactflow/dist/style.css";
import Style from "../styles/FlowCopmponent.module.scss";
import {
  <PERSON><PERSON>,
  <PERSON>nackbar,
  Alert,
  CircularProgress,
  Typography,
} from "@mui/material";
import AddProductModal from "../components/AddProductModal";
import AddWatchdogModal from "../components/AddProcessBlockModal";
import PartDropdownButton from "../components/SelectFlowDropDown";
import CustomNode from "../components/CustomNode";
import AssignRoutingForm from "../components/AssignRoutingForm";
import { convertRoutingToFlow } from "../utils/routingUtils";
import { RoutingSchema, useRoutings } from "../hooks/useRoutingApi";
import RouteConfigCreation from './RouteConfigCreation'

// Define node types for ReactFlow
const nodeTypes = { customNode: CustomNode };

const ProductRoutingPage: React.FC = () => {
  // State for modals and flow data
  const [openProductModal, setOpenProductModal] = useState(false);
  const [openWatchdogModal, setOpenWatchdogModal] = useState(false);
  const [schema, setSchema] = useState<RoutingSchema | null>(null);
  const [hasRoutingData, setHasRoutingData] = useState(false);
  const [routingError, setRoutingError] = useState<string | null>(null);
  const [selectedProductId, setSelectedProductId] = useState<string>("");
  // We store the product code for future use when implementing routing assignment
  const [selectedProductCode, setSelectedProductCode] = useState<string>("");
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [snackbarSeverity, setSnackbarSeverity] = useState<"success" | "error" | "info" | "warning">("info");
  // Fetch routing configurations
  const { data: routings, isLoading: isLoadingRoutings } = useRoutings();

  // Log routings data when it changes
  useEffect(() => {
    if (routings) {
      console.log('Fetched routings:', routings);
    }
  }, [routings]);

  // Helper function to show snackbar
  const showSnackbar = (message: string, severity: "success" | "error" | "info" | "warning") => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setSnackbarOpen(true);
  };

  // Handle closing snackbar
  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
  };

  // State to track loading state
  const [isLoadingRoutingData, setIsLoadingRoutingData] = useState<boolean>(false);

  // Handle successful routing assignment
  const handleRoutingAssignmentSuccess = (routingData: any) => {
    showSnackbar(`Successfully assigned routing to product`, "success");
    console.log('Routing assignment success data:', routingData);

    // Process the routing data directly from the mutation response
    if (routingData && routingData.schema && routingData.schema.routing_schema) {
      try {
        // Update the flow with the new data
        setSchema(routingData.schema.routing_schema);
        setHasRoutingData(true);
        setRoutingError(null);
      } catch (error) {
        console.error('Error processing routing data:', error);
      }
    } else if (routingData && routingData.routing) {
      // If we have routing info but no schema, trigger a refresh
      console.log('Routing assigned successfully, refreshing data...');

      // Refresh the product selection to get the full routing data
      if (selectedProductCode) {
        // Simulate selecting the product again to trigger a data refresh
        const currentCode = selectedProductCode;
        setSelectedProductCode('');

        // Use setTimeout to ensure state updates have propagated
        setTimeout(() => {
          setSelectedProductCode(currentCode);
        }, 100);
      }
    } else {
      console.log('No routing data in response, will refresh on next selection');
    }
  };

  return (
    <div className={Style.processRouteList}>
      <div className={Style.routeContainer}>
        {/* Top bar with dropdown and buttons */}
        <div
          style={{
            display: "flex",
            gap: "1rem",
            marginBottom: "1rem",
            justifyContent: "space-between",
          }}
        >
          <PartDropdownButton
            onProductSelect={(productId, productCode) => {
              setSelectedProductId(productId);
              setSelectedProductCode(productCode);
              setIsLoadingRoutingData(true); // Set loading state when product is selected
              console.log(`Product selected: ID=${productId}, Code=${productCode}`);
            }}
            onRoutingDataChange={(data, error) => {
              setIsLoadingRoutingData(false); // Clear loading state when data is received

              if (data) {
                try {
                  console.log("Routing data received:", data);

                  // Check if data has the expected schema structure
                  if (data.schema && data.schema.routing_schema) {
                    setSchema(data.schema.routing_schema);
                    setHasRoutingData(true);
                    setRoutingError(null);
                  } else {
                    // Handle case where data doesn't have the expected structure
                    console.error(
                      "API response doesn't have expected schema structure:",
                      data
                    );
                    setHasRoutingData(false);
                    setRoutingError(null); // Don't show error to user
                  }
                } catch (err) {
                  console.error("Error processing routing data:", err);
                  setHasRoutingData(false);
                  // Don't show error message to user, just treat as no routing
                  setRoutingError(null);
                }
              } else if (error) {
                // Handle error - treat as no routing
                console.error("API error:", error);
                setHasRoutingData(false);
                // Don't set error message, just treat as no routing
                setRoutingError(null);
              } else {
                // No selection
                setHasRoutingData(false);
                setRoutingError(null);
              }
            }}
          />

          <div style={{ display: "flex", gap: "1rem" }}>
            <Button
              variant="contained"
              style={{ backgroundColor: "#3E4146", height: "45px" }}
              onClick={() => setOpenProductModal(true)}
            >
              Add Product
            </Button>
            <Button
              variant="contained"
              style={{ backgroundColor: "#3E4146", height: "45px" }}
              onClick={() => setOpenWatchdogModal(true)}
            >
              Add Block
            </Button>
          </div>
        </div>

        {isLoadingRoutingData ? (
          // Show loading indicator while fetching routing data
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            padding: '100px 0',
            textAlign: 'center'
          }}>
            <CircularProgress size={40} sx={{ mb: 3 }} />
            <Typography variant="h6" gutterBottom>
              Loading Routing Configuration
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Please wait while we load the routing data...
            </Typography>
          </div>
        ) : hasRoutingData ? (
          // If routing data is available, show the flow
          <>
            <div className={Style.routeHeader}>
              Routing Flow
              {isLoadingRoutings && (
                <CircularProgress size={20} style={{ marginLeft: 10 }} />
              )}
            </div>
            <div className={Style.flowContainer}>
              <RouteConfigCreation schema={schema} readOnly />
            </div>
          </>
        ) : (
          // If no routing data, show assignment UI or message
          <div style={{ width: '100%', padding: '20px 0' }}>
            {selectedProductId ? (
              // Product selected but no routing - show assignment UI
              <div style={{ maxWidth: '800px', margin: '0 auto' }}>
                {/* Show assignment form in a simple card */}
                <div style={{
                  backgroundColor: 'white',
                  borderRadius: '8px',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.08)',
                  padding: '24px'
                }}>

                  <AssignRoutingForm
                    productId={selectedProductId}
                    productName={selectedProductCode}
                    onSuccess={handleRoutingAssignmentSuccess}
                  />
                </div>
              </div>
            ) : (
              // No product selected - show message
              <div style={{
                textAlign: 'center',
                padding: '60px 20px',
                color: '#666',
                backgroundColor: '#f5f5f5',
                borderRadius: '8px',
                border: '1px dashed #ccc',
                maxWidth: '800px',
                margin: '40px auto'
              }}>
                <div style={{ fontSize: '48px', marginBottom: '20px' }}>🔍</div>
                <h3 style={{ margin: '0 0 10px 0', color: '#333' }}>No Product Selected</h3>
                <p style={{ margin: 0 }}>Please select a product from the dropdown to view its routing flow or assign a routing.</p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Modals */}
      <AddProductModal
        open={openProductModal}
        onClose={() => setOpenProductModal(false)}
      />
      <AddWatchdogModal
        open={openWatchdogModal}
        onClose={() => setOpenWatchdogModal(false)}
      />


      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbarSeverity}
          sx={{ width: "100%" }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </div>
  );
};

export default ProductRoutingPage;
