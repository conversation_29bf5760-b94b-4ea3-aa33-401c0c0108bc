// import React from "react";
// import DynamicTable from "../components/DynamicTable";

// const FormConfigTable: React.FC = () => {
//   // const existingConfigs = JSON.parse(
//   //   localStorage.getItem("formConfigs") || "[]"
//   // );

//   const userTableData = [
//     { ID: 1, Name: "<PERSON>", Email: "<EMAIL>", Age: 28 },
//     { ID: 2, Name: "<PERSON>", Email: "<EMAIL>", Age: 32 },
//     { ID: 3, Name: "<PERSON>", Email: "<EMAIL>", Age: 25 },
//     { ID: 3, Name: "<PERSON>", Email: "<EMAIL>", Age: 25 },
//     { ID: 3, Name: "<PERSON>", Email: "<EMAIL>", Age: 25 },
//     { ID: 3, Name: "<PERSON>", Email: "<EMAIL>", Age: 25 },
//   ];

//   const productTableData = [
//     { "Product ID": 101, "Product Name": "Laptop", Price: "$1000", Stock: 10 },
//     { "Product ID": 102, "Product Name": "Phone", Price: "$800", Stock: 15 },
//     {
//       "Product ID": 103,
//       "Product Name": "Headphones",
//       Price: "$100",
//       Stock: 50,
//     },
//   ];

//   return (
//     <div style={{ padding: "20px" }}>
//       <DynamicTable
//         title="User Table"
//         headers={[
//           { label: "ID", key: "ID" },
//           { label: "Name", key: "Name" },
//           { label: "Email", key: "Email" },
//           { label: "Age", key: "Age" },
//         ]}
//         data={userTableData}
//       />
//       <DynamicTable
//         title="Product Table"
//         headers={[
//           { label: "Product ID", key: "Product ID" },
//           { label: "Product Name", key: "Product Name" },
//           { label: "Price", key: "Price" },
//           { label: "Stock", key: "Stock" },
//         ]}
//         data={productTableData}
//       />
//     </div>
//   );
// };

// export default FormConfigTable;
