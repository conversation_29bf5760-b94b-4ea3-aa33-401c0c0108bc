import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Button,
  Stack,
  useTheme,
  IconButton,
  Tooltip,
  Paper,
  Grid,
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import RouteIcon from '@mui/icons-material/Route';
import { useProductById, productUtils } from '../hooks/useProductTable';
import { useSnackbar } from '../context/SnackBarContext';
import RouteConfigCreation from './RouteConfigCreation';
import { useRoutingById, useRoutingByProductCode } from '../hooks/useRoutingTable';
import { sectionHeaderStyle } from '../utils/commonStyles';

// Import our reusable components
import DetailField from '../components/DetailField';
import LoadingState from '../components/LoadingState';
import ErrorState from '../components/ErrorState';
import AssignRoutingForm from '../components/AssignRoutingForm';
import EditProductModal from '../components/EditProductModal';
import DeleteProductDialog from '../components/DeleteProductDialog';

const ProductViewPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const productId = id ? parseInt(id, 10) : null;
  const navigate = useNavigate();
  const theme = useTheme();
  const { showSnackbar } = useSnackbar();

  // State for routing assignment
  const [showAssignRouting, setShowAssignRouting] = useState(false);

  // State for edit product modal
  const [showEditModal, setShowEditModal] = useState(false);

  // State for delete product dialog
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  // Remove tab state - using single view layout

  // Fetch product data
  const { data: product, isLoading, error } = useProductById(productId);

  // Fetch routing data if product has a routing ID
  const { data: routingById, isLoading: isRoutingByIdLoading } = useRoutingById(
    product?.routing_id || null,
    { enabled: !!product?.routing_id }
  );

  // Fetch routing data by product code if no routing ID is available
  const { data: routingByCode, isLoading: isRoutingByCodeLoading } = useRoutingByProductCode(
    product?.code || null,
    {
      enabled: !!product?.code && !product?.routing_id,
      retry: false, // Don't retry if not found
      onError: () => { } // Silently handle errors
    }
  );

  // Combine the routing data
  const routing = routingById || routingByCode;
  const isRoutingLoading = isRoutingByIdLoading || isRoutingByCodeLoading;

  // Debug: Log the routing data
  console.log('Product:', product);
  console.log('Routing by ID:', routingById);
  console.log('Routing by Code:', routingByCode);
  console.log('Combined Routing:', routing);

  // Handle errors
  useEffect(() => {
    if (error) {
      showSnackbar(`Error loading product: ${error instanceof Error ? error.message : 'Unknown error'}`, 'error');
    }
  }, [error, showSnackbar]);

  // Handle back button
  const handleBack = () => {
    navigate('/products');
  };

  // Handle edit button
  const handleEdit = () => {
    setShowEditModal(true);
  };

  // Handle edit success
  const handleEditSuccess = (updatedProduct: any) => {
    showSnackbar('Product updated successfully', 'success');

    // No need to refetch the product data, as the cache has already been updated
    // by the useUpdateProduct hook
    console.log('Product updated:', updatedProduct);
  };

  // Handle delete button
  const handleDelete = () => {
    setShowDeleteDialog(true);
  };

  // Handle delete success
  const handleDeleteSuccess = () => {
    showSnackbar('Product deleted successfully', 'success');
    navigate('/products');
  };

  // Handle assign routing button
  const handleAssignRouting = () => {
    setShowAssignRouting(true);
  };

  // Handle routing assignment success
  const handleRoutingAssigned = () => {
    setShowAssignRouting(false);
    showSnackbar('Routing assigned successfully', 'success');

    // No need to reload the page, as the cache has already been updated
    // by the useAssignRouting hook
    console.log('Routing assigned successfully, UI will update automatically');
  };

  // Handle routing assignment cancel
  const handleAssignRoutingCancel = () => {
    setShowAssignRouting(false);
  };

  // If loading, show loading indicator
  if (isLoading) {
    return (
      <Container maxWidth="xl" sx={{ py: 3, px: 0 }}>
        <LoadingState message="Loading product information..." size={60} />
      </Container>
    );
  }

  // If error, show error message
  if (error || !product) {
    return (
      <Container maxWidth="xl" sx={{ py: 3, px: 0 }}>
        <ErrorState
          title="Error Loading Product"
          message={error instanceof Error ? error.message : 'Failed to load product information.'}
          actionButton={{
            label: 'Back to Products',
            onClick: handleBack
          }}
        />
      </Container>
    );
  }

  return (
    <Box sx={{
      height: '100vh',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden',
      bgcolor: '#fafafa'
    }}>
      {/* Compact Header */}
      <Box sx={{
        px: 3,
        py: 2,
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        borderBottom: '1px solid #e2e8f0',
        bgcolor: 'white',
        boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
        zIndex: 10
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', flex: 1 }}>
          <Button
            variant="text"
            onClick={handleBack}
            sx={{ mr: 2, color: '#0f172a', minWidth: 'auto', p: 1 }}
            startIcon={<Box component="span" sx={{ transform: 'rotate(180deg)' }}>→</Box>}
          >
            Back
          </Button>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant="h5" component="h1" sx={{ fontWeight: 600, fontSize: '18px', color: '#0f172a' }}>
              {product.name}
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
              <Typography variant="body2" sx={{ color: '#64748b', fontSize: '13px' }}>
                Code: {product.code}
              </Typography>
              {product.routing_details && (
                <>
                  <Box sx={{ width: 4, height: 4, borderRadius: '50%', bgcolor: '#64748b' }} />
                  <Typography variant="body2" sx={{ color: '#64748b', fontSize: '13px' }}>
                    Routing: {product.routing_details.name}
                  </Typography>
                </>
              )}
            </Box>
          </Box>
        </Box>

        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<DeleteIcon sx={{ fontSize: 16 }} />}
            onClick={handleDelete}
            size="small"
            sx={{
              color: '#dc2626',
              borderColor: '#dc2626',
              height: '32px',
              fontSize: '12px',
              textTransform: 'none',
              borderRadius: '6px',
              '&:hover': {
                bgcolor: '#fef2f2',
                borderColor: '#dc2626',
              }
            }}
          >
            Delete
          </Button>

          <Button
            variant="contained"
            startIcon={<EditIcon sx={{ fontSize: 16 }} />}
            onClick={handleEdit}
            size="small"
            sx={{
              bgcolor: '#0f172a',
              color: 'white',
              height: '32px',
              fontSize: '12px',
              textTransform: 'none',
              borderRadius: '6px',
              '&:hover': {
                bgcolor: '#1e293b',
              }
            }}
          >
            Edit
          </Button>
        </Box>
      </Box>

      {/* Main Content Area */}
      <Box sx={{
        flex: 1,
        display: 'flex',
        overflow: 'hidden',
        gap: 2,
        p: 2
      }}>
        {/* Compact Product Information Sidebar */}
        <Box sx={{
          width: 280,
          flexShrink: 0,
          display: 'flex',
          flexDirection: 'column',
          gap: 2
        }}>
          <Card sx={{
            boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
            borderRadius: 2,
            overflow: 'hidden',
            bgcolor: 'white'
          }}>
            <Box sx={{
              bgcolor: '#f8fafc',
              px: 3,
              py: 2,
              borderBottom: '1px solid #e2e8f0'
            }}>
              <Typography variant="h6" sx={{ fontSize: '14px', fontWeight: 600, color: '#0f172a' }}>
                Product Details
              </Typography>
            </Box>
            <CardContent sx={{ p: 3 }}>
              <Stack spacing={2.5}>
                <Box>
                  <Typography variant="body2" sx={{ fontSize: '11px', fontWeight: 600, color: '#64748b', textTransform: 'uppercase', letterSpacing: '0.5px', mb: 0.5 }}>
                    Name
                  </Typography>
                  <Typography variant="body1" sx={{ fontSize: '13px', fontWeight: 500, color: '#0f172a' }}>
                    {product.name}
                  </Typography>
                </Box>

                <Box>
                  <Typography variant="body2" sx={{ fontSize: '11px', fontWeight: 600, color: '#64748b', textTransform: 'uppercase', letterSpacing: '0.5px', mb: 0.5 }}>
                    Code
                  </Typography>
                  <Typography variant="body1" sx={{ fontSize: '13px', fontWeight: 500, color: '#0f172a' }}>
                    {product.code}
                  </Typography>
                </Box>

                {product.description && (
                  <Box>
                    <Typography variant="body2" sx={{ fontSize: '11px', fontWeight: 600, color: '#64748b', textTransform: 'uppercase', letterSpacing: '0.5px', mb: 0.5 }}>
                      Description
                    </Typography>
                    <Typography variant="body1" sx={{ fontSize: '13px', fontWeight: 500, color: '#0f172a', lineHeight: 1.4 }}>
                      {product.description}
                    </Typography>
                  </Box>
                )}

                <Box>
                  <Typography variant="body2" sx={{ fontSize: '11px', fontWeight: 600, color: '#64748b', textTransform: 'uppercase', letterSpacing: '0.5px', mb: 0.5 }}>
                    Created
                  </Typography>
                  <Typography variant="body1" sx={{ fontSize: '13px', fontWeight: 500, color: '#0f172a' }}>
                    {productUtils.formatDate(product.created_at)}
                  </Typography>
                </Box>

                <Box>
                  <Typography variant="body2" sx={{ fontSize: '11px', fontWeight: 600, color: '#64748b', textTransform: 'uppercase', letterSpacing: '0.5px', mb: 0.5 }}>
                    Last Updated
                  </Typography>
                  <Typography variant="body1" sx={{ fontSize: '13px', fontWeight: 500, color: '#0f172a' }}>
                    {productUtils.formatDate(product.updated_at)}
                  </Typography>
                </Box>

                <Box>
                  <Typography variant="body2" sx={{ fontSize: '11px', fontWeight: 600, color: '#64748b', textTransform: 'uppercase', letterSpacing: '0.5px', mb: 0.5 }}>
                    Routing Assignment
                  </Typography>
                  <Typography variant="body1" sx={{
                    fontSize: '13px',
                    fontWeight: 500,
                    color: product.routing_details ? '#059669' : '#dc2626',
                    lineHeight: 1.4
                  }}>
                    {product.routing_details
                      ? `${product.routing_details.name} (${product.routing_details.code})`
                      : 'No routing assigned'
                    }
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Box>

        {/* Primary Routing Flow Visualization */}
        <Box sx={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden'
        }}>
          {showAssignRouting ? (
            <AssignRoutingForm
              productId={productId!}
              onSuccess={handleRoutingAssigned}
              onCancel={handleAssignRoutingCancel}
            />
          ) : routing ? (
            <Card sx={{
              boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
              borderRadius: 2,
              overflow: 'hidden',
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              bgcolor: 'white'
            }}>
              <Box sx={{
                bgcolor: '#f8fafc',
                px: 3,
                py: 2,
                borderBottom: '1px solid #e2e8f0',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                flexShrink: 0
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Typography variant="h6" sx={{ fontSize: '16px', fontWeight: 600, color: '#0f172a' }}>
                    Routing Flow Diagram
                  </Typography>
                  {product.routing_details && (
                    <Box sx={{
                      bgcolor: '#e0f2fe',
                      color: '#0369a1',
                      px: 2,
                      py: 0.5,
                      borderRadius: 1,
                      fontSize: '12px',
                      fontWeight: 500
                    }}>
                      {product.routing_details.name} ({product.routing_details.code})
                    </Box>
                  )}
                </Box>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={handleAssignRouting}
                  sx={{
                    color: '#0f172a',
                    borderColor: '#e2e8f0',
                    fontSize: '12px',
                    py: 0.5,
                    px: 2,
                    textTransform: 'none',
                    borderRadius: '6px',
                    '&:hover': {
                      bgcolor: '#f8fafc',
                      borderColor: '#cbd5e1',
                    }
                  }}
                >
                  Reassign Routing
                </Button>
              </Box>
              <Box sx={{
                flex: 1,
                position: 'relative',
                bgcolor: '#fafafa',
                overflow: 'hidden'
              }}>
                {isRoutingLoading ? (
                  <LoadingState message="Loading routing information..." />
                ) : routing.schema && routing.schema.routing_schema ? (
                  <Box sx={{
                    height: '100%',
                    width: '100%',
                    position: 'relative',
                    '& .react-flow__pane': {
                      cursor: 'default',
                    },
                    '& .react-flow__node': {
                      transition: 'all 0.2s ease',
                    }
                  }}>
                    <RouteConfigCreation
                      schema={routing.schema.routing_schema}
                      readOnly={true}
                    />
                  </Box>
                ) : (
                  <Box sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    height: '100%',
                    flexDirection: 'column',
                    gap: 2
                  }}>
                    <Typography variant="h6" sx={{ color: '#64748b', fontSize: '16px' }}>
                      No routing schema available
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#94a3b8', fontSize: '14px' }}>
                      This product doesn't have a routing flow assigned yet.
                    </Typography>
                  </Box>
                )}
              </Box>
            </Card>
          ) : (
            <Card sx={{
              boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
              borderRadius: 2,
              overflow: 'hidden',
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              bgcolor: 'white'
            }}>
              <Box sx={{
                bgcolor: '#f8fafc',
                px: 3,
                py: 2,
                borderBottom: '1px solid #e2e8f0',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                flexShrink: 0
              }}>
                <Typography variant="h6" sx={{ fontSize: '16px', fontWeight: 600, color: '#0f172a' }}>
                  Routing Flow Diagram
                </Typography>
                <Button
                  variant="contained"
                  size="small"
                  onClick={handleAssignRouting}
                  sx={{
                    bgcolor: '#0f172a',
                    color: 'white',
                    fontSize: '12px',
                    py: 0.5,
                    px: 2,
                    textTransform: 'none',
                    borderRadius: '6px',
                    '&:hover': {
                      bgcolor: '#1e293b',
                    }
                  }}
                >
                  Assign Routing
                </Button>
              </Box>
              <Box sx={{
                flex: 1,
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                p: 5,
                textAlign: 'center',
                bgcolor: '#fafafa'
              }}>
                <Box
                  component="img"
                  src="/assets/no-routing.svg"
                  alt="No routing"
                  sx={{
                    width: 80,
                    height: 80,
                    opacity: 0.5,
                    mb: 3
                  }}
                  onError={(e) => {
                    // Fallback if image doesn't exist
                    e.currentTarget.style.display = 'none';
                  }}
                />
                <Typography variant="h6" sx={{ mb: 2, color: '#64748b', fontWeight: 600, fontSize: '18px' }}>
                  No Routing Assigned
                </Typography>
                <Typography variant="body1" sx={{ mb: 4, color: '#94a3b8', maxWidth: '400px', fontSize: '14px', lineHeight: 1.5 }}>
                  This product doesn't have a routing flow assigned yet. Click the "Assign Routing" button above to assign a routing configuration.
                </Typography>
              </Box>
            </Card>
          )}
        </Box>
      </Box>

      {/* Edit Product Modal */}
      {product && (
        <EditProductModal
          open={showEditModal}
          onClose={() => setShowEditModal(false)}
          onSuccess={handleEditSuccess}
          productId={productId!}
          initialData={{
            name: product.name,
            code: product.code,
            description: product.description,
            routing_id: product.routing_id,
          }}
        />
      )}

      {/* Delete Product Dialog */}
      {product && (
        <DeleteProductDialog
          open={showDeleteDialog}
          onClose={() => setShowDeleteDialog(false)}
          onSuccess={handleDeleteSuccess}
          productId={productId!}
          productName={product.name}
        />
      )}
    </Box>
  );
};

export default ProductViewPage;
