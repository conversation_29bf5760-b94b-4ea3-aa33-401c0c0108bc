import React from "react";
import { useNavigate } from "react-router-dom";
import styles from "../styles/NotFound.module.scss";

const NotFound: React.FC = () => {
  const navigate = useNavigate();

  const handleGoHome = () => {
    navigate("/");
  };

  return (
    <div className={styles.container}>
      <h1 className={styles.title}>404</h1>
      <h2 className={styles.subtitle}>Oops! Page Not Found</h2>
      <p className={styles.message}>
        The page you are looking for does not exist. Please check the URL or go
        back to the homepage.
      </p>
      <button className={styles.button} onClick={handleGoHome}>
        Go to Home
      </button>
    </div>
  );
};

export default NotFound;
