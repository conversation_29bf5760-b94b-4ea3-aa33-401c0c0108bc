import React, { useState } from "react";
import DynamicTable from "../components/DynamicTable";
import { useWorkOrders, useCreateWorkOrder } from "../hooks/useWorkOrders";
import WorkOrderForm from "../components/WorkOrderForm";
import WorkOrderFilter from "../components/WorkOrderFilter";
import ModalComponent from "../components/ModalComponent";
import { Drawer, Button, Box, Container } from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import useScreen from "../hooks/useScreenSize";
import { useSnackbar } from "../context/SnackBarContext";
import { useQueryClient } from "@tanstack/react-query";
import { PageContainer, HeaderContainer, PageTitle, ActionButton, FilterContainer, FilterText, ResetButton, ButtonGroupContainer } from "../styles/PageTable.styles";

interface WorkOrder {
  line: string;
  part_no: string;
  customer: string;
  order_date: string;
  order_no: string;
  cf: string;
  plan: string;
  actual: string;
  page?: number;
  page_size?: number;
}

const WorkOrderTable: React.FC = () => {
  const [filterOpen, setFilterOpen] = useState(false);
  const [createOpen, setCreateOpen] = useState(false);

  const [filterInput, setFilterInput] = useState<Partial<WorkOrder>>({
    line: "",
    part_no: "",
    order_date: "",
    order_no: "",
  });

  const [filter, setFilter] = useState<Partial<WorkOrder>>({
    line: "",
    part_no: "",
    order_date: "",
    order_no: "",
    page: 1,
    page_size: 30,
  });

  const [workOrderData, setWorkOrderData] = useState<Partial<WorkOrder>>({
    line: "",
    part_no: "",
    customer: "",
    order_date: "",
    order_no: "",
    cf: "",
    plan: "",
    actual: "",
  });

  const [validationErrors, setValidationErrors] = useState<
    Record<string, string>
  >({});

  const { data, isLoading, error } = useWorkOrders(filter as any);
  const { showSnackbar } = useSnackbar();
  const queryClient = useQueryClient();

  const createWorkOrderMutation = useCreateWorkOrder();
  const { device } = useScreen();

  const handleFilterInputChange = (field: string, value: string) => {
    if (value) {
      value = value?.trim?.() ?? value
    }
    setFilterInput((prev) => ({ ...prev, [field]: value }));
  };

  // Handle search
  const handleSearch = (searchTerm: string) => {
    setFilterInput((prev) => ({ ...prev, search: searchTerm }));

    setFilter((prev) => ({
      ...prev,
      search: searchTerm,
      page: 1 // Reset to first page on search
    }));
  };

  const handleWorkOrderChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    let { name, value } = event.target;

    if (value) {
      value = value.trim();
    }
    setWorkOrderData((prev) => ({ ...prev, [name]: value }));

    // Clear error for the changed field
    setValidationErrors((prev) => ({
      ...prev,
      [name]: "",
    }));
  };

  const handleApplyFilter = () => {
    setFilter(filterInput);
    setFilterOpen(false);
  };

  const handleResetFilter = () => {
    setFilterInput({
      line: "",
      part_no: "",
      order_date: "",
      order_no: "",
    });
    setFilter({
      line: "",
      part_no: "",
      customer: "",
      order_date: "",
      page: 1,
      page_size: 20,
    });
  };

  const isFilterApplied = () => {
    const { page, page_size, ...filterWithoutPagination } = filter;
    return Object.values(filterWithoutPagination).some((value) => value !== "");
  };

  const validateWorkOrderData = () => {
    const errors: Record<string, string> = {};
    if (!workOrderData.line) errors.line = "Line is required";
    if (!workOrderData.part_no) errors.part_no = "Part No is required";
    if (!workOrderData.customer) errors.customer = "Customer is required";
    if (!workOrderData.order_date) errors.order_date = "Order Date is required";
    if (!workOrderData.order_no) errors.order_no = "Order No is required";

    // Add validation for CF, Plan, and Actual
    if (!workOrderData.cf) errors.cf = "CF is required";
    if (!workOrderData.plan) errors.plan = "Plan is required";
    if (!workOrderData.actual) errors.actual = "Actual is required";

    // Validate numbers
    if (isNaN(Number(workOrderData.cf)) || Number(workOrderData.cf) <= 0) errors.cf = "CF must be a number and greater than 0";
    if (isNaN(Number(workOrderData.plan)) || Number(workOrderData.plan) <= 0)
      errors.plan = "Plan must be a number and greater than 0";
    if (isNaN(Number(workOrderData.actual)) || Number(workOrderData.actual) <= 0)
      errors.actual = "Actual must be a number and greater than 0";
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleCreateSubmit = async () => {
    const isValid = validateWorkOrderData();
    if (!isValid) {
      return;
    }

    try {
      createWorkOrderMutation.mutate(workOrderData as any, {
        onSuccess: () => {
          queryClient.invalidateQueries({
            queryKey: ["workOrders", { page: 1 }],
          });
          showSnackbar(
            'workorder submitted successfully',
            "success"
          );
        },
        onError: (error: any) => {
          showSnackbar(
            error || "Failed to submit workorder. Please try again.",
            "error"
          );
        }
      });

      console.log("Work order created successfully");
      setCreateOpen(false);
      setWorkOrderData({
        line: "",
        part_no: "",
        customer: "",
        order_date: "",
        order_no: "",
        cf: "",
        plan: "",
        actual: "",
      });
    } catch (error) {
      console.error("Error creating work order:", error);
    }
  };

  const handlePageChange = (newPage: number) => {
    setFilter((prev) => ({ ...prev, page: newPage }));
  };

  const handlePageSizeChange = (newSize: number) => {
    setFilter((prev) => ({ ...prev, page_size: newSize }));
  };

  return (
    <Container maxWidth="xl" sx={{ py: 2, px: 0 }}>
      <HeaderContainer>
        <PageTitle variant="h5" component="h1">
          Work Order Listing
        </PageTitle>

        <ButtonGroupContainer>
          <ActionButton
            variant="contained"
            startIcon={<AddIcon sx={{ fontSize: 16 }} />}
            onClick={() => setCreateOpen(true)}
          >
            Add Work Order
          </ActionButton>
        </ButtonGroupContainer>
      </HeaderContainer>

      {isFilterApplied() && (
        <FilterContainer>
          <FilterText variant="body2">
            Filters applied
          </FilterText>
          <ResetButton
            variant="text"
            size="small"
            onClick={handleResetFilter}
          >
            Reset Filters
          </ResetButton>
        </FilterContainer>
      )}

      <DynamicTable
        headers={[
          { label: "Line", key: "line" },
          { label: "Part No", key: "part_no" },
          { label: "Customer", key: "customer" },
          { label: "Order Date", key: "order_date" },
          { label: "Order No", key: "order_no" },
          { label: "CF", key: "cf" },
          { label: "Plan", key: "plan" },
          { label: "Actual", key: "actual" },
          { label: "Created At", key: "created_at" },
          { label: "Updated At", key: "updated_at" },
          { label: "Created By", key: "created_by" },
          { label: "Created By Name", key: "created_by_name" },
        ]}
        data={data?.results || []}
        loading={isLoading}
        error={error}
        count={data?.count || 0}
        page={filter.page || 1}
        rowsPerPage={data?.results?.length || 10}
        onPageChange={handlePageChange}
        onRowsPerPageChange={handlePageSizeChange}
        onSearch={handleSearch}
        searchPlaceholder="Search work orders..."
        onRowClick={(row) => console.log('Row clicked', row)}
      />

      <ModalComponent
        open={createOpen}
        onClose={() => setCreateOpen(false)}
        onSubmit={handleCreateSubmit}
        title="Create Work Order"
        loading={createWorkOrderMutation.isPending}
      >
        <WorkOrderForm
          workOrderData={workOrderData}
          onChange={handleWorkOrderChange}
          validationErrors={validationErrors}
        />
      </ModalComponent>

      <Drawer
        anchor="right"
        open={filterOpen}
        onClose={() => setFilterOpen(false)}
      >
        <Box
          sx={{
            width: device === "mobile" ? "100vw" : 400,
            p: 2,
            bgcolor: "background.paper",
            borderRadius: 1,
            border: "none",
          }}
        >
          <WorkOrderFilter
            filterInput={filterInput}
            onChange={handleFilterInputChange}
            onClose={() => setFilterOpen(false)}
          />
          <Box sx={{ mt: 2, display: "flex", justifyContent: "center" }}>
            <Button
              variant="contained"
              style={{ backgroundColor: "#000", color: "#fff" }}
              onClick={handleApplyFilter}
              disabled={createWorkOrderMutation.isPending}
            >
              Apply Filter
            </Button>
          </Box>
        </Box>
      </Drawer>
    </Container>
  );
};

export default WorkOrderTable;

