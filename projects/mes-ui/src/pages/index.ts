import { lazy } from "react";
export const Dashboard = lazy(() => import("./Dashboard"));
export const FormConfigList = lazy(() => import("./FormConfigList"));
export const DynamicForm = lazy(() => import("./DynamicForm.tsx"));
export const FormCreator = lazy(() => import("./FormCreator.tsx"));
export const SignInForm = lazy(() => import("./SignIn.tsx"));
export const Reports = lazy(() => import("./ReportPage.tsx"));
export const UploadPage = lazy(() => import("./Upload.tsx"));
export const ProcessRouteList = lazy(() => import("./ProcessRouteList.tsx"));
export const RouteConfigCreation = lazy(() => import("./RouteConfigCreation.tsx"));
export const ProductRoutingPage = lazy(() => import("./ProductRoutingPage.tsx"));
export const RoutingTablePage = lazy(() => import("./RoutingTablePage.tsx"));
export const RoutingViewPage = lazy(() => import("./RoutingViewPage.tsx"));
export const ProductRoute = lazy(() => import("./ProductRoute.tsx"));
export const WorkOrderTable = lazy(() => import("./WorkOrderTable.tsx"));
export const ProductTablePage = lazy(() => import("./ProductTablePage.tsx"));
export const ProductViewPage = lazy(() => import("./ProductViewPage.tsx"));
export const BOMTablePage = lazy(() => import("./BOMTablePage.tsx"));

