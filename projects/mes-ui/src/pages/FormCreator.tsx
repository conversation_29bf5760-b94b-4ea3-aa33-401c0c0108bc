import React, { useState } from "react";
import {
  TextField,
  Button,
  Select,
  MenuItem,
  Checkbox,
  FormControlLabel,
  FormControl,
  InputLabel,
  Typography,
  IconButton,
  SelectChangeEvent,
  Divider,
  ThemeProvider,
} from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import WarningIcon from "@mui/icons-material/Warning";
import { theme } from "../utils/muiTheme"; // Assuming theme is in the same directory
import builderStyles from "../styles/FormBuilder.module.scss";
import { actionType } from "../utils/formActions";
import { handlerList } from "../utils/formHandlers";
import { ResponsiveSize } from "../interfaces/form-config.api.interface";

interface ActionConfig {
  action_type: string;
  handler: string;
}
export interface FieldConfig {
  label: string;
  name: string;
  type: string;
  required: boolean;
  placeholder?: string;
  readonly: boolean;
  regex?: string;
  options?: { value: string; label: string }[];
  position: number;
  alignment?: string;
  isVisible: boolean;
  width?: ResponsiveSize;
  height?: ResponsiveSize;
  marginRight?: ResponsiveSize;
  actions: ActionConfig[];
}

const FormCreator: React.FC = () => {
  const [fields, setFields] = useState<FieldConfig[]>([]);
  const [newField, setNewField] = useState<FieldConfig>({
    label: "",
    name: "",
    type: "text",
    required: false,
    placeholder: "",
    regex: "",
    options: [],
    position: 0,
    alignment: "left",
    readonly: false,
    isVisible: true,
    actions: [],
  });

  const [newOption, setNewOption] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [formName, setFormName] = useState("");

  const validateNewField = (): boolean => {
    if (
      !newField.label ||
      !newField.name ||
      !formName ||
      newField.position === 0
    ) {
      setError("Form name Label, unique name and position are mandatory");
      return false;
    }
    setError(null);
    return true;
  };

  const addField = () => {
    if (validateNewField()) {
      // Ensure unique position for the new field
      const updatedFields = [...fields];
      const positionExists = updatedFields.some(
        (field) => field.position === newField.position
      );

      if (positionExists) {
        // Shift positions of fields to make room for the new field
        updatedFields.forEach((field) => {
          if (field.position >= newField.position) {
            field.position += 1; // Increment positions of fields after the new field
          }
        });
      }

      // Add the new field with the adjusted position
      setFields([...updatedFields, newField]);

      // Reset new field and state
      setNewField({
        label: "",
        name: "",
        type: "text",
        required: false,
        placeholder: "",
        regex: "",
        options: [],
        position: 0, // Reset position
        alignment: "left",
        readonly: false,
        actions: [],
        isVisible: true,
      });
    }
  };

  const deleteField = (index: number) => {
    const fieldToDelete = fields[index];
    // Remove the field
    const updatedFields = fields.filter((_, i) => i !== index);

    // Adjust positions of remaining fields after deletion
    updatedFields.forEach((field) => {
      if (field.position > fieldToDelete.position) {
        field.position -= 1; // Decrease positions of fields after the deleted one
      }
    });

    setFields(updatedFields);
  };

  const addOption = () => {
    if (newOption.trim()) {
      const updatedOptions = newField.options
        ? [...newField.options, { value: newOption, label: newOption }]
        : [];
      setNewField({ ...newField, options: updatedOptions });
      setNewOption("");
    }
  };

  const removeOption = (optionIndex: number) => {
    setNewField({
      ...newField,
      options: newField.options?.filter((_, index) => index !== optionIndex),
    });
  };

  const handleFieldChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | { value: unknown }
    >
  ) => {
    const { name, value } = e.target as HTMLInputElement;
    setNewField({ ...newField, [name]: value });
  };

  const handleTypeChange = (event: SelectChangeEvent) => {
    setNewField({
      ...newField,
      type: event.target.value as string,
      options:
        event.target.value === "select" || event.target.value === "radio"
          ? []
          : undefined,
    });
  };

  const handleRequiredChange = () => {
    setNewField({ ...newField, required: !newField.required });
  };

  const handleisVisibleChange = () => {
    setNewField({ ...newField, isVisible: !newField.isVisible });
  };

  const handleReadOnlyChange = () => {
    setNewField({ ...newField, readonly: !newField.readonly });
  };

  const handlePositionChange = (event: SelectChangeEvent<number>) => {
    const position = event.target.value as number;
    setNewField((prev) => ({ ...prev, position }));
  };

  // State for managing new actions
  const [newAction, setNewAction] = useState<ActionConfig>({
    action_type: "",
    handler: "",
  });

  // Add action to the field
  const addAction = () => {
    if (newAction.action_type && newAction.handler) {
      setNewField((prev) => ({
        ...prev,
        actions: [...prev.actions, newAction],
      }));
      setNewAction({ action_type: "", handler: "" });
    }
  };

  // Handle action type change
  const handleActionTypeChange = (event: SelectChangeEvent<string>) => {
    setNewAction({
      ...newAction,
      action_type: event.target.value as string,
    });
  };

  // Handle handler selection change
  const handleHandlerChange = (event: SelectChangeEvent<string>) => {
    setNewAction({
      ...newAction,
      handler: event.target.value as string,
    });
  };

  // Remove an action
  const removeAction = (index: number) => {
    setNewField((prev) => ({
      ...prev,
      actions: prev.actions.filter((_, i) => i !== index),
    }));
  };

  const handleSubmit = () => {
    if (!formName.trim()) {
      setError("Form name is required.");
      return;
    }

    const newConfig = {
      id: Date.now(),
      name: formName,
      type: "custom",
      fields: fields
        .map((field) => ({
          label: field.label,
          name: field.name,
          type: field.type,
          required: field.required,
          placeholder: field.placeholder || "",
          regex: field.regex || "",
          options: field.options || [],
          width: field.width || "100%",
          marginRight: field.marginRight || "0px",
          alignment: field.marginRight || "left",
          readonly: field.readonly,
          actions: field.actions,
          isVisible: field.isVisible,
          position: field.position,
        }))
        .sort((a, b) => a.position - b.position),
    };

    const existingConfigs = JSON.parse(
      localStorage.getItem("formConfigs") || "[]"
    );

    const isDuplicate = existingConfigs.some(
      (config: any) => config.name === newConfig.name
    );

    if (isDuplicate) {
      setError("A form with this name already exists.");
      return;
    }

    existingConfigs.push(newConfig);
    localStorage.setItem("formConfigs", JSON.stringify(existingConfigs));

    setFields([]);
    setFormName("");
    setError("");
    setNewAction({
      action_type: "",
      handler: "",
    });
  };

  return (
    <ThemeProvider theme={theme}>
      <div className={builderStyles.formBuilderContainer}>
        <Typography variant="h5">Create Form Configuration</Typography>
        <div className={builderStyles.fieldConfig}>
          {error && (
            <Typography color="error" className={builderStyles.errorText}>
              <WarningIcon /> {error}
            </Typography>
          )}
          <div className={builderStyles.fieldItemRow}>
            <TextField
              label="Form Name"
              value={formName}
              className={builderStyles.field}
              size="small"
              onChange={(e) => setFormName(e.target.value)}
              margin="normal"
              error={!formName && !!error}
            />
            <TextField
              label="Field Label"
              name="label"
              size="small"
              value={newField.label}
              onChange={handleFieldChange}
              className={builderStyles.field}
              margin="normal"
              error={!newField.label && !!error}
            />
          </div>
          <div className={builderStyles.fieldItemRow}>
            <TextField
              label="Unique Name"
              name="name"
              size="small"
              className={builderStyles.field}
              value={newField.name}
              onChange={handleFieldChange}
              margin="normal"
              error={!newField.name && !!error}
            />
            <TextField
              label="Placeholder"
              name="placeholder"
              size="small"
              className={builderStyles.field}
              value={newField.placeholder}
              onChange={handleFieldChange}
              margin="normal"
            />
          </div>
          <div className={builderStyles.fieldItemRow}>
            <TextField
              label="Regex Pattern"
              name="regex"
              className={builderStyles.field}
              value={newField.regex}
              onChange={handleFieldChange}
              size="small"
              margin="normal"
              // helperText="Optional: Add a regex pattern for validation"
            />
            <TextField
              label="Width"
              name="width"
              value={newField.width}
              className={builderStyles.field}
              size="small"
              onChange={handleFieldChange}
              margin="normal"
            />
          </div>
          <div className={builderStyles.fieldItemRow}>
            <TextField
              label="Margin Right"
              className={builderStyles.field}
              name="marginRight"
              value={newField.marginRight}
              onChange={handleFieldChange}
              size="small"
              margin="normal"
            />
            <FormControl margin="normal">
              <InputLabel id="alignment-select-label">Alignment</InputLabel>
              <Select
                size="small"
                labelId="alignment-select-label"
                value={newField.alignment}
                className={builderStyles.field}
                label={"Alignment"}
                onChange={(e) =>
                  setNewField({ ...newField, alignment: e.target.value })
                }
              >
                <MenuItem key={"left"} value="left">
                  Left
                </MenuItem>
                <MenuItem key={"center"} value="center">
                  Center
                </MenuItem>
                <MenuItem key={"right"} value="right">
                  Right
                </MenuItem>
              </Select>
            </FormControl>
          </div>
          <div className={builderStyles.fieldItemRow}>
            <FormControl margin="normal" size="small">
              <InputLabel id="position-select-label">Position</InputLabel>
              <Select
                labelId="position-select-label"
                value={newField.position || ""}
                className={builderStyles.field}
                onChange={handlePositionChange}
                label={"Position"}
                error={newField.position === 0 && !!error}
              >
                {[...Array(fields.length + 2)].map((_, index) => (
                  <MenuItem value={index + 1} key={index}>
                    Position {index + 1}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <FormControl margin="normal" size="small">
              <InputLabel id="demo-simple-select-helper-label">
                Field Type
              </InputLabel>
              <Select
                id="demo-simple-select-helper"
                value={newField.type}
                onChange={handleTypeChange}
                className={builderStyles.field}
                name="type"
                label="Field Type"
              >
                <MenuItem value="text">Text</MenuItem>
                <MenuItem value="textarea">Textarea</MenuItem>
                <MenuItem value="select">Select</MenuItem>
                <MenuItem value="radio">Radio</MenuItem>
                <MenuItem value="number">Number</MenuItem>
                <MenuItem value="checkbox">Checkbox</MenuItem>
              </Select>
            </FormControl>
          </div>
          {(newField.type === "select" || newField.type === "radio") && (
            <div className={builderStyles.optionsContainer}>
              <TextField
                label="New Option"
                value={newOption}
                onChange={(e) => setNewOption(e.target.value)}
                size="small"
                margin="dense"
                className={builderStyles.optionField}
              />
              <Button
                variant="contained"
                color="primary"
                onClick={addOption}
                className={builderStyles.addOptionButton}
              >
                Add Option
              </Button>
              {newField.options?.length ? (
                <Typography variant="body1">Options:</Typography>
              ) : null}
              <div className={builderStyles.optionItemRow}>
                {newField.options?.map((option, index) => (
                  <div
                    key={index}
                    style={{
                      display: "flex",
                      width: "30%",
                      alignItems: "center",
                    }}
                  >
                    <TextField
                      value={option.label}
                      disabled
                      variant="outlined"
                      size="small"
                      margin="dense"
                    />
                    <IconButton
                      color="secondary"
                      onClick={() => removeOption(index)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </div>
                ))}
              </div>
            </div>
          )}
          <div className={builderStyles.fieldItemRow}>
            <FormControl margin="normal" size="small">
              <InputLabel id="action-type-select-label">Action Type</InputLabel>
              <Select
                labelId="action-type-select-label"
                value={newAction.action_type}
                onChange={handleActionTypeChange}
                className={builderStyles.field}
                label="Action Type"
              >
                {actionType.map((action) => (
                  <MenuItem value={action}>{action}</MenuItem>
                ))}
              </Select>
            </FormControl>
            <FormControl margin="normal" size="small">
              <InputLabel id="handler-select-label">
                Handler Function
              </InputLabel>
              <Select
                labelId="handler-select-label"
                value={newAction.handler}
                onChange={handleHandlerChange}
                className={builderStyles.field}
                label="Handler Function"
              >
                {handlerList.map((handler) => (
                  <MenuItem key={handler} value={handler}>
                    {handler}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </div>
          {newAction.action_type && newAction.handler && (
            <Button onClick={addAction} variant="contained" color="primary">
              Add Action
            </Button>
          )}
          <div className={builderStyles.optionItemRow}>
            {newField?.actions?.map((action, index) => (
              <div
                key={index}
                style={{
                  display: "flex",
                  width: "32%",
                  alignItems: "center",
                }}
              >
                <div>
                  {action.action_type} - {action.handler}
                </div>
                <IconButton
                  onClick={() => removeAction(index)}
                  color="secondary"
                >
                  <DeleteIcon />
                </IconButton>
              </div>
            ))}
          </div>
          <div>
            <FormControlLabel
              control={
                <Checkbox
                  checked={newField.isVisible}
                  onChange={handleisVisibleChange}
                />
              }
              label="Visibility"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={newField.required}
                  onChange={handleRequiredChange}
                />
              }
              label="Required"
            />
            {(newField.type === "text" ||
              newField.type === "number" ||
              newField.type === "textarea") && (
              <FormControlLabel
                control={
                  <Checkbox
                    checked={newField.readonly}
                    onChange={handleReadOnlyChange}
                  />
                }
                label="Read-Only"
              />
            )}
          </div>

          <Button
            variant="contained"
            color="primary"
            onClick={addField}
            className={builderStyles.addFieldButton}
          >
            Add Field
          </Button>
        </div>

        <div className={builderStyles.fieldList}>
          <Typography variant="h6">Fields List:</Typography>
          {fields.map((field, index) => (
            <div key={index} className={builderStyles.fieldItemRow}>
              <div className={builderStyles.fieldItemContent}>
                <Typography variant="body1">
                  <b>Label:</b> {field.label}
                </Typography>
                <Typography variant="body1">
                  <b>Name:</b> {field.name}
                </Typography>
                <Typography variant="body1">
                  <b>Type:</b> {field.type}
                </Typography>
                <Typography variant="body1">
                  {field.required ? "Required" : "Optional"}
                </Typography>
                <Typography variant="body1">
                  <b>Position:</b> {field.position}
                </Typography>
                <Typography variant="body1">
                  <b>Readonly:</b> {field.readonly}
                </Typography>
              </div>
              <IconButton color="secondary" onClick={() => deleteField(index)}>
                <DeleteIcon />
              </IconButton>
              <Divider />
            </div>
          ))}
        </div>
        <Button
          variant="contained"
          color="success"
          disabled={!fields.length}
          onClick={handleSubmit}
          className={builderStyles.submitButton}
        >
          Submit Form Configuration
        </Button>
      </div>
    </ThemeProvider>
  );
};

export default FormCreator;
