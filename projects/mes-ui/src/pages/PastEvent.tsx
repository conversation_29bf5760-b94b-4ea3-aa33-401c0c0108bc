import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
  Box,
  CircularProgress,
} from "@mui/material";
import styles from "../styles/EventList.module.scss";
import { useFetchFormEvents } from "../hooks/useForm";

const FormEvents = ({ code }: { code: string | undefined }) => {
  const { data: eventQueue, isLoading, isError } = useFetchFormEvents(code);

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" p={4}>
        <CircularProgress />
      </Box>
    );
  }

  if (isError) {
    return (
      <Box display="flex" justifyContent="center" p={4}>
        <Typography variant="h6" color="error">
          Failed to load events
        </Typography>
      </Box>
    );
  }

  if (!eventQueue || eventQueue.length === 0) return null;

  const allKeys = new Set<string>();
  eventQueue.forEach((event) => {
    Object.keys(event.event_data).forEach((key) => allKeys.add(key));
  });
  const sortedKeys = Array.from(allKeys).sort();

  return (
    <Box className={styles.eventListContainer}>
      <TableContainer component={Paper} className={styles.tableContainer} sx={{
        borderRadius: '8px',
        boxShadow: 'none',
        overflow: 'hidden',
        border: '1px solid #e2e8f0',
      }}>
        <Typography variant="h6" sx={{ p: 2 }}>
          Recent Events
        </Typography>
        <Box>
          <Table size="small" sx={{ borderCollapse: 'separate', borderSpacing: 0 }}>
            <TableHead>
              <TableRow sx={{
                backgroundColor: '#f8fafc',
                '& th:first-of-type': {
                  borderTopLeftRadius: '8px'
                },
                '& th:last-child': {
                  borderTopRightRadius: '8px'
                }
              }}>
                <TableCell sx={{
                  fontWeight: 600,
                  fontSize: '0.8rem',
                  color: '#4a5568',
                  borderBottom: '1px solid #e2e8f0',
                  paddingBottom: '10px',
                  paddingTop: '10px',
                  height: '48px',
                  paddingLeft: '16px',
                  paddingRight: '16px',
                }}>#</TableCell>
                <TableCell sx={{
                  fontWeight: 600,
                  fontSize: '0.8rem',
                  color: '#4a5568',
                  borderBottom: '1px solid #e2e8f0',
                  paddingBottom: '10px',
                  paddingTop: '10px',
                  height: '48px',
                  paddingLeft: '16px',
                  paddingRight: '16px',
                }}>Timestamp</TableCell>
                <TableCell sx={{
                  fontWeight: 600,
                  fontSize: '0.8rem',
                  color: '#4a5568',
                  borderBottom: '1px solid #e2e8f0',
                  paddingBottom: '10px',
                  paddingTop: '10px',
                  height: '48px',
                  paddingLeft: '16px',
                  paddingRight: '16px',
                }}>Created By</TableCell>
                <TableCell sx={{
                  fontWeight: 600,
                  fontSize: '0.8rem',
                  color: '#4a5568',
                  borderBottom: '1px solid #e2e8f0',
                  paddingBottom: '10px',
                  paddingTop: '10px',
                  height: '48px',
                  paddingLeft: '16px',
                  paddingRight: '16px',
                }}>Serial Number</TableCell>
                {sortedKeys.map((key) => (
                  <TableCell key={key} sx={{
                    fontWeight: 600,
                    fontSize: '0.8rem',
                    color: '#4a5568',
                    borderBottom: '1px solid #e2e8f0',
                    paddingBottom: '10px',
                    paddingTop: '10px',
                    height: '48px',
                    paddingLeft: '16px',
                    paddingRight: '16px',
                  }}>{key.replace(/_/g, " ")}</TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {eventQueue.map((event, index) => (
                <TableRow key={event.id} sx={{
                  '&:hover': {
                    backgroundColor: '#f1f5f9',
                  },
                  borderBottom: '1px solid #e2e8f0',
                }}>
                  <TableCell sx={{
                    maxWidth: '250px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    color: 'rgb(2, 8, 23)',
                    fontSize: '16px',
                    fontFamily: 'ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
                    fontWeight: 400,
                    lineHeight: '24px',
                    paddingLeft: '16px',
                    paddingRight: '16px',
                  }}>{index + 1}</TableCell>
                  <TableCell sx={{
                    maxWidth: '250px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    color: 'rgb(2, 8, 23)',
                    fontSize: '16px',
                    fontFamily: 'ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
                    fontWeight: 400,
                    lineHeight: '24px',
                    paddingLeft: '16px',
                    paddingRight: '16px',
                  }}>
                    {new Date(event.timestamp).toLocaleString()}
                  </TableCell>
                  <TableCell sx={{
                    maxWidth: '250px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    color: 'rgb(2, 8, 23)',
                    fontSize: '16px',
                    fontFamily: 'ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
                    fontWeight: 400,
                    lineHeight: '24px',
                    paddingLeft: '16px',
                    paddingRight: '16px',
                  }}>{event?.created_by?.username}</TableCell>
                  <TableCell sx={{
                    maxWidth: '250px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    color: 'rgb(2, 8, 23)',
                    fontSize: '16px',
                    fontFamily: 'ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
                    fontWeight: 400,
                    lineHeight: '24px',
                    paddingLeft: '16px',
                    paddingRight: '16px',
                  }}>{event.serial_number}</TableCell>
                  {sortedKeys.map((key) => (
                    <TableCell key={key} sx={{
                      maxWidth: '250px',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      color: 'rgb(2, 8, 23)',
                      fontSize: '16px',
                      fontFamily: 'ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
                      fontWeight: 400,
                      lineHeight: '24px',
                      paddingLeft: '16px',
                      paddingRight: '16px',
                    }}>
                      {(() => {
                        try {
                          const value = event.event_data?.[key];

                          if (value === undefined || value === null)
                            return "N/A";

                          if (Array.isArray(value)) {
                            return (
                              <pre
                                style={{
                                  whiteSpace: "pre-wrap",
                                  margin: 0,
                                  fontSize: '16px',
                                  fontFamily: 'ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
                                  color: 'rgb(2, 8, 23)',
                                }}
                              >
                                {JSON.stringify(value)}
                              </pre>
                            );
                          }

                          if (typeof value === "object") {
                            return (
                              <pre
                                style={{
                                  whiteSpace: "pre-wrap",
                                  margin: 0,
                                  fontSize: '16px',
                                  fontFamily: 'ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
                                  color: 'rgb(2, 8, 23)',
                                }}
                              >
                                {JSON.stringify(value)}
                              </pre>
                            );
                          }

                          return String(value);
                        } catch (error) {
                          console.error(`Error rendering key ${key}:`, error);
                          return "Error loading data";
                        }
                      })()}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </Box>
      </TableContainer>
    </Box>
  );
};

export default FormEvents;
