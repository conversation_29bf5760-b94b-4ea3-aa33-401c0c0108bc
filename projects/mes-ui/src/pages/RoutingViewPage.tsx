import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Chip,
  Card,
  CardContent,
  useTheme,
  Button,
  Stack,
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import { useRoutingById, useDeleteRouting, routingUtils } from '../hooks/useRoutingTable';
import RouteConfigCreation from './RouteConfigCreation';
import { useSnackbar } from '../context/SnackBarContext';

// Import our reusable components
import LoadingState from '../components/LoadingState';
import ErrorState from '../components/ErrorState';
import ConfirmationDialog from '../components/ConfirmationDialog';

const RoutingViewPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const routingId = id ? parseInt(id, 10) : null;
  const navigate = useNavigate();
  const theme = useTheme();
  const { showSnackbar } = useSnackbar();

  // State for delete confirmation dialog
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  // Fetch routing data
  const { data: routing, isLoading, error } = useRoutingById(routingId);

  // Delete routing mutation
  const deleteMutation = useDeleteRouting();

  // Handle errors
  useEffect(() => {
    if (error) {
      showSnackbar(`Error loading routing: ${error instanceof Error ? error.message : 'Unknown error'}`, 'error');
    }
  }, [error, showSnackbar]);

  // Handle back button
  const handleBack = () => {
    navigate('/routings');
  };

  // Handle edit button
  const handleEdit = () => {
    if (routingId) {
      navigate(`/routing/edit/${routingId}`);
    }
  };

  // Handle delete button
  const handleDelete = () => {
    setDeleteDialogOpen(true);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    if (!routingId) return;

    try {
      const result = await deleteMutation.mutateAsync(routingId);
      showSnackbar(result.message, 'success');
      navigate('/routings');
    } catch (error) {
      showSnackbar(`Failed to delete routing: ${error instanceof Error ? error.message : 'Unknown error'}`, 'error');
      setDeleteDialogOpen(false);
    }
  };

  // Handle delete cancellation
  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
  };

  // If loading, show loading indicator
  if (isLoading) {
    return (
      <Container maxWidth="xl" sx={{ py: 3, px: 0 }}>
        <LoadingState message="Loading routing configuration..." size={60} />
      </Container>
    );
  }

  // If error, show error message
  if (error || !routing) {
    return (
      <Container maxWidth="xl" sx={{ py: 3, px: 0 }}>
        <ErrorState
          title="Error Loading Routing"
          message={error instanceof Error ? error.message : 'Failed to load routing configuration.'}
          actionButton={{
            label: 'Back to Routings',
            onClick: handleBack
          }}
        />
      </Container>
    );
  }

  return (
    <Box sx={{
      height: '100vh',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden',
      bgcolor: '#fafafa'
    }}>
      {/* Compact Header */}
      <Box sx={{
        px: 3,
        py: 2,
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        borderBottom: '1px solid #e2e8f0',
        bgcolor: 'white',
        boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
        zIndex: 10
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', flex: 1 }}>
          <Button
            variant="text"
            onClick={handleBack}
            sx={{ mr: 2, color: '#0f172a', minWidth: 'auto', p: 1 }}
            startIcon={<Box component="span" sx={{ transform: 'rotate(180deg)' }}>→</Box>}
          >
            Back
          </Button>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant="h5" component="h1" sx={{ fontWeight: 600, fontSize: '18px', color: '#0f172a' }}>
              {routing.name}
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
              <Typography variant="body2" sx={{ color: '#64748b', fontSize: '13px' }}>
                Created by: {routingUtils.getCreatorName(routing.created_by)}
              </Typography>
              <Box sx={{ width: 4, height: 4, borderRadius: '50%', bgcolor: '#64748b' }} />
              <Typography variant="body2" sx={{ color: '#64748b', fontSize: '13px' }}>
                {routingUtils.formatDate(routing.created_at)}
              </Typography>
            </Box>
          </Box>
        </Box>

        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<DeleteIcon sx={{ fontSize: 16 }} />}
            onClick={handleDelete}
            size="small"
            sx={{
              color: '#dc2626',
              borderColor: '#dc2626',
              height: '32px',
              fontSize: '12px',
              textTransform: 'none',
              borderRadius: '6px',
              '&:hover': {
                bgcolor: '#fef2f2',
                borderColor: '#dc2626',
              }
            }}
          >
            Delete
          </Button>

          <Button
            variant="contained"
            startIcon={<EditIcon sx={{ fontSize: 16 }} />}
            onClick={handleEdit}
            size="small"
            sx={{
              bgcolor: '#0f172a',
              color: 'white',
              height: '32px',
              fontSize: '12px',
              textTransform: 'none',
              borderRadius: '6px',
              '&:hover': {
                bgcolor: '#1e293b',
              }
            }}
          >
            Edit
          </Button>
        </Box>
      </Box>

      {/* Main Content Area */}
      <Box sx={{
        flex: 1,
        display: 'flex',
        overflow: 'hidden',
        gap: 2,
        p: 2
      }}>
        {/* Compact Routing Information Sidebar */}
        <Box sx={{
          width: 280,
          flexShrink: 0,
          display: 'flex',
          flexDirection: 'column',
          gap: 2
        }}>
          <Card sx={{
            boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
            borderRadius: 2,
            overflow: 'hidden',
            bgcolor: 'white'
          }}>
            <Box sx={{
              bgcolor: '#f8fafc',
              px: 3,
              py: 2,
              borderBottom: '1px solid #e2e8f0'
            }}>
              <Typography variant="h6" sx={{ fontSize: '14px', fontWeight: 600, color: '#0f172a' }}>
                Routing Details
              </Typography>
            </Box>
            <CardContent sx={{ p: 3 }}>
              <Stack spacing={2.5}>
                <Box>
                  <Typography variant="body2" sx={{ fontSize: '11px', fontWeight: 600, color: '#64748b', textTransform: 'uppercase', letterSpacing: '0.5px', mb: 0.5 }}>
                    Name
                  </Typography>
                  <Typography variant="body1" sx={{ fontSize: '13px', fontWeight: 500, color: '#0f172a' }}>
                    {routing.name}
                  </Typography>
                </Box>

                <Box>
                  <Typography variant="body2" sx={{ fontSize: '11px', fontWeight: 600, color: '#64748b', textTransform: 'uppercase', letterSpacing: '0.5px', mb: 0.5 }}>
                    Code
                  </Typography>
                  <Typography variant="body1" sx={{ fontSize: '13px', fontWeight: 500, color: '#0f172a' }}>
                    {routing.code}
                  </Typography>
                </Box>

                <Box>
                  <Typography variant="body2" sx={{ fontSize: '11px', fontWeight: 600, color: '#64748b', textTransform: 'uppercase', letterSpacing: '0.5px', mb: 0.5 }}>
                    Created By
                  </Typography>
                  <Typography variant="body1" sx={{ fontSize: '13px', fontWeight: 500, color: '#0f172a' }}>
                    {routingUtils.getCreatorName(routing.created_by)}
                  </Typography>
                </Box>

                <Box>
                  <Typography variant="body2" sx={{ fontSize: '11px', fontWeight: 600, color: '#64748b', textTransform: 'uppercase', letterSpacing: '0.5px', mb: 0.5 }}>
                    Created At
                  </Typography>
                  <Typography variant="body1" sx={{ fontSize: '13px', fontWeight: 500, color: '#0f172a' }}>
                    {routingUtils.formatDate(routing.created_at)}
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>

          {/* Associated Products Card */}
          <Card sx={{
            boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
            borderRadius: 2,
            overflow: 'hidden',
            bgcolor: 'white'
          }}>
            <Box sx={{
              bgcolor: '#f8fafc',
              px: 3,
              py: 2,
              borderBottom: '1px solid #e2e8f0'
            }}>
              <Typography variant="h6" sx={{ fontSize: '14px', fontWeight: 600, color: '#0f172a' }}>
                Associated Products
              </Typography>
            </Box>
            <CardContent sx={{ p: 3 }}>
              {routing.products_details && routing.products_details.length > 0 ? (
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  {routing.products_details.map((product) => (
                    <Box
                      key={product.id}
                      sx={{
                        bgcolor: '#f1f5f9',
                        borderRadius: 1,
                        px: 2,
                        py: 1,
                        border: '1px solid #e2e8f0'
                      }}
                    >
                      <Typography variant="body2" sx={{ fontSize: '12px', fontWeight: 500, color: '#0f172a' }}>
                        {product.name}
                      </Typography>
                      <Typography variant="body2" sx={{ fontSize: '11px', color: '#64748b' }}>
                        {product.code}
                      </Typography>
                    </Box>
                  ))}
                </Box>
              ) : (
                <Typography variant="body2" sx={{ color: '#94a3b8', fontSize: '13px' }}>
                  No products associated with this routing.
                </Typography>
              )}
            </CardContent>
          </Card>
        </Box>

        {/* Primary Routing Flow Visualization */}
        <Box sx={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden'
        }}>
          <Card sx={{
            boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
            borderRadius: 2,
            overflow: 'hidden',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            bgcolor: 'white'
          }}>
            <Box sx={{
              bgcolor: '#f8fafc',
              px: 3,
              py: 2,
              borderBottom: '1px solid #e2e8f0',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              flexShrink: 0
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Typography variant="h6" sx={{ fontSize: '16px', fontWeight: 600, color: '#0f172a' }}>
                  Routing Flow Diagram
                </Typography>
                <Box sx={{
                  bgcolor: '#e0f2fe',
                  color: '#0369a1',
                  px: 2,
                  py: 0.5,
                  borderRadius: 1,
                  fontSize: '12px',
                  fontWeight: 500
                }}>
                  {routing.code}
                </Box>
              </Box>
            </Box>
            <Box sx={{
              flex: 1,
              position: 'relative',
              bgcolor: '#fafafa',
              overflow: 'hidden'
            }}>
              {routing.schema && routing.schema.routing_schema ? (
                <Box sx={{
                  height: '100%',
                  width: '100%',
                  position: 'relative',
                  '& .react-flow__pane': {
                    cursor: 'default',
                  },
                  '& .react-flow__node': {
                    transition: 'all 0.2s ease',
                  }
                }}>
                  <RouteConfigCreation
                    schema={routing.schema.routing_schema}
                    readOnly={true}
                  />
                </Box>
              ) : (
                <Box sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: '100%',
                  flexDirection: 'column',
                  gap: 2
                }}>
                  <Typography variant="h6" sx={{ color: '#64748b', fontSize: '16px' }}>
                    No routing schema available
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#94a3b8', fontSize: '14px' }}>
                    This routing doesn't have a flow diagram configured yet.
                  </Typography>
                </Box>
              )}
            </Box>
          </Card>
        </Box>
      </Box>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={deleteDialogOpen}
        title="Delete Routing Configuration"
        message={`Are you sure you want to delete the routing "${routing.name}"? This action cannot be undone.`}
        confirmLabel="Delete"
        cancelLabel="Cancel"
        onConfirm={handleDeleteConfirm}
        onCancel={handleDeleteCancel}
        isLoading={deleteMutation.isPending}
        severity="error"
      />
    </Box>
  );
};

export default RoutingViewPage;
