import React, {
  use<PERSON><PERSON>back,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useParams } from "react-router-dom";
import {
  Button,
  Typography,
  Box,
  CssBaseline,
  SelectChangeEvent,
} from "@mui/material";
import { ThemeProvider } from "@mui/material/styles";
import InputField from "../components/TextInput";
import SelectField from "../components/SelectInput";
import RadioField from "../components/RadioButton";
import TextAreaField from "../components/TextArea";
import DateField from "../components/DateField";
import { ApiFormConfig } from "../interfaces/form-config.api.interface";
import { FieldConfig } from "./FormCreator";
import { theme } from "../utils/muiTheme";
import CheckboxField from "../components/Checkbox";
import ConfirmationModal from "../components/ConfirmationModalUi";
import { useRoutinBySerialNumber, useSubmitForm } from "../hooks/useForm";
import { useSnackbar } from "../context/SnackBarContext";
// import JsonData from "../config.json";
import componentRegistry from "../utils/componentRegistery";
import { useAuth } from "../context/AuthContext";
import MultiSelectField from "../components/MultiSelect";
import ErrorMessage from "../components/ErrorMessage";
import { FormHandlerFactory } from "../utils/forms/formFactory";
import { handleSerialNumberChange } from "../utils/shared/handlleSerialInputChange";
import { fetchAndProcessFormConfig } from "../utils/shared/fetchAndProcessFormConfig";
import { getValidationSchema } from "../utils/shared/validationSchema";
import { useEventQueue } from "../hooks/useEventQueue";
import Style from "../styles/DynamicForm.module.scss";
// import EventList from "./EventList.tsx";
import FormEvents from "./PastEvent.tsx";
import StepBar from "../components/FormStepBar.tsx";
import { useQueryClient } from "@tanstack/react-query";

const DynamicForm: React.FC = () => {
  const { mutate: submitFormData, isPending: isLoading } = useSubmitForm();
  const queryClient = useQueryClient();

  const { showSnackbar } = useSnackbar();
  const { authState } = useAuth();
  const serialNumberRef = useRef<HTMLInputElement>(null);

  const { id } = useParams<{ id: string }>();
  const formId = id ? parseInt(id) : undefined;

  //form states
  const [formConfig, setFormConfig] = useState<ApiFormConfig | null>(null);
  const [initialConfig, setInitialConfig] = useState<ApiFormConfig | null>(
    null
  );
  const [formValues, setFormValues] = useState<any>({});
  const [nestedFieldValues, setNestedFieldValues] = useState<any>({});
  const [formErrors, setFormErrors] = useState<any>({});
  const [error, setError] = useState<boolean>(false);

  const [showSubmitButton, setShowSubmitButton] = useState<boolean>(false);
  const [failedButtonState, setFailedButtonState] = useState<string | null>(
    null
  );
  const [invalidSubmissionError, setInvalidSubmissionError] = useState(null);

  const { serial_number } = formValues;

  const {
    data: config,
    isLoading: loading,
    isError,
    refetch: fetchRouteBySerialNumber,
  } = useRoutinBySerialNumber(serial_number, true);

  const [modal, setModalState] = useState<{
    title: string;
    isOpen: boolean;
  }>({
    title: "Already exist serial number do you wish to continue?",
    isOpen: false,
  });

  const { title, isOpen } = modal;

  useEffect(() => {
    if (failedButtonState === "fail") {
      setShowSubmitButton(true);
    } else {
      setShowSubmitButton(false);
    }
  }, [failedButtonState]);

  const handleModalClose = () => {
    setModalState((prev) => ({ ...prev, isOpen: false }));
  };

  const handleFormConfigUpdate = useCallback(
    (fieldConfigUpdates: Record<string, any>) => {
      setFormConfig((prevConfig: any) => {
        const updateFieldsRecursively: any = (
          fields: any[],
          isNested = false
        ) =>
          fields.map((field: any) => {
            // Check if this field needs an update
            if (fieldConfigUpdates[field.name]) {
              const updatedField = {
                ...field,
                ...fieldConfigUpdates[field.name],
              };

              // Update form values for this field

              if (!isNested) {
                setFormValues((prevValues: any) => ({
                  ...prevValues,
                  [field.name]: updatedField[field.name],
                }));
              } else {
                setNestedFieldValues((prevNestedValues: any) => ({
                  ...prevNestedValues,
                  [field.name]: updatedField[field.name] || "",
                }));
              }

              return updatedField;
            }

            // If this field contains nested fields (e.g., form_fields), recursively update them
            if (field?.form_fields) {
              return {
                ...field,
                form_fields: updateFieldsRecursively(field.form_fields, true),
              };
            }

            return field;
          });

        return {
          ...prevConfig,
          form_schema: {
            ...prevConfig.form_schema,
            form_fields: updateFieldsRecursively(
              prevConfig.form_schema.form_fields
            ),
          },
        };
      });
    },
    []
  );

  const onCancel = () => {
    setModalState((prev) => ({ ...prev, isOpen: false }));

    if (formValues?.["serial_number"]) {
      handleSerialNumberChange(handleFormConfigUpdate, formValues);
    }

    if (formConfig?.code) {
      const formHandler: any = FormHandlerFactory.getHandler(formConfig.code);
      if (formHandler && typeof formHandler["onModalClose"] === "function") {
        formHandler["onModalClose"](handleFormConfigUpdate);
      }
    }
  };

  useEffect(() => {
    if (formId) {
      fetchAndProcessFormConfig(
        formId,
        setFormConfig,
        setInitialConfig,
        setFormValues,
        setNestedFieldValues,
        setError
      );
    }
  }, []);

  useEffect(() => {
    if (serialNumberRef.current) {
      serialNumberRef.current.focus();
    }
  }, [formValues.serial_number]);

  const validationSchema = useMemo(
    () => getValidationSchema(formConfig),
    [formConfig]
  );

  const handleFieldChange = (
    e:
      | React.ChangeEvent<
        HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
      >
      | SelectChangeEvent
      | SelectChangeEvent<string[]>
      | any,
    field: FieldConfig
  ) => {
    let { name, value, checked, type } = e.target as any;
    setInvalidSubmissionError(null);

    if (value instanceof Date && value < new Date()) {
      value = new Date(); // Reset to today if the date is in the past
    }

    if (value && !(value instanceof Date)) {
      value = value?.trim?.() ?? value;
    }

    if (type === "checkbox") {
      setFormValues((prevValues: any) => ({
        ...prevValues,
        [name]: checked,
      }));
    } else {
      setFormValues((prevValues: any) => ({
        ...prevValues,
        [name]: value
      }));
    }

    if (name === "serial_number" && formValues["part_description"]) {
      handleSerialNumberChange(handleFormConfigUpdate, formValues, value);
    }

    setFormErrors((prevErrors: any) => ({
      ...prevErrors,
      [name]: undefined,
    }));

    handleFieldAction("onChange", e, field);
  };

  const handleFieldAction = async (
    actionType: string,
    event:
      | React.ChangeEvent<
        HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
      >
      | SelectChangeEvent
      | SelectChangeEvent<string[]>
      | any,
    field: FieldConfig
  ) => {
    const action = field.actions.find((a) => a.action_type === actionType);
    const { name, value } = event.target;

    try {
      //retfetchroute by serial number
      if (
        actionType === "onBlur" &&
        serial_number &&
        serial_number.length > 15
      ) {
        if (!config) {
          fetchRouteBySerialNumber();
        }
      }

      const error: any = {};
      if (Object.keys(formValues).includes("fault_reported")) {
        error["fault_reported"] = "";
      }

      if (Object.keys(formValues).includes("failed_stage")) {
        error["failed_stage"] = "";
      }

      await validationSchema.validateAt(name, { [name]: value });

      setFormErrors((prevErrors: any) => ({
        ...prevErrors,
        [name]: undefined,
        ...error,
      }));
    } catch (err: any) {
      setFormErrors((prevErrors: any) => ({
        ...prevErrors,
        [name]: err.message,
      }));
    }
    if (action && !formErrors[field.name]) {
      handleAction(
        action.handler,
        field,
        event,
        handleFormConfigUpdate,
        setModalState
      );
    }
  };

  const handleAction = (
    handlerName: string,
    field: FieldConfig,
    event:
      | React.ChangeEvent<
        HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
      >
      | SelectChangeEvent
      | SelectChangeEvent<string[]>,
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void,
    setModalOpen: React.Dispatch<
      React.SetStateAction<{
        title: string;
        isOpen: boolean;
      }>
    >
  ) => {
    if (formConfig?.code) {
      const formHandler: any = FormHandlerFactory.getHandler(formConfig.code);
      if (formHandler && typeof formHandler[handlerName] === "function") {
        formHandler[handlerName](
          formValues,
          field,
          event,
          handleFormConfigUpdate,
          setModalOpen,
          setFormErrors,
          showSnackbar,
          formId,
          setShowSubmitButton
        );
      } else {
        console.warn(
          `Handler "${handlerName}" is not defined for form ${formId}`
        );
      }
    }
  };

  const handleSubmit = async (
    status?: "pass" | "fail" | "scrap" | "submit",
    field_name?: string,
    field_value?: any
  ) => {
    try {
      formValues["sign_in_user"] = authState?.user?.username;

      if (status) {
        if (status === "submit") {
          formValues["form_status"] = "pass";
        } else {
          formValues["form_status"] = status;
        }
      }

      if (field_name && field_value) {
        formValues[field_name] = field_value;
      }
      const visibleFields = Object.keys(formValues).reduce((acc, key) => {
        const field = formConfig?.form_schema?.form_fields?.find(
          (f) => f.name === key
        );
        if (field?.required) {
          acc[key] = formValues[key];
        }
        return acc;
      }, {} as any);
      console.log("+++++Form Values Before Validation+++++", formValues);
      await validationSchema.validate(visibleFields, { abortEarly: false });
      console.log("+++++Form Values After Validation+++++", formValues);

      let next_action = "main_forward";
      let event_type = "main";

      // hardcode check for now
      if (formConfig?.code === "rework_station") {
        event_type = status === "submit" ? "rework" : status ?? "main";
      }

      if (
        formConfig?.code === "pre_wave" &&
        formValues["form_status"] === "fail"
      ) {
        const failCauses = [
          "wrong_component_name",
          "missing_component_name",
          "wrong_polarity_component",
        ];
        const hasAtLeastOneEntry = failCauses.some(
          (field) => formValues[field]
        );

        if (!hasAtLeastOneEntry) {
          showSnackbar("Please select at least one fail cause.", "error");
          return;
        }
      }

      if (formValues?.form_action) {
        next_action = formValues?.form_action;
      }

      if (formValues?.is_corrected) {
        next_action = "corrected";
      }

      const eventData = {
        serial_number: formValues.serial_number,
        form: formId,
        event_data: formValues,
        timestamp: new Date().toISOString(),
        created_by: formValues.sign_in_user,
        inspection_status: formValues.form_status === "pass" ? true : false,
        event_type: event_type,
        next_action: next_action,
      };

      submitFormData(eventData, {
        onSuccess: () => {
          if (formConfig?.code) {
            queryClient.invalidateQueries({
              queryKey: ["events", formConfig.code],
            });
          }
          if (eventData.serial_number) {
            console.log("Invalidating serial number:", eventData.serial_number);
            queryClient.invalidateQueries({
              queryKey: ["serial_number", eventData.serial_number],
            });
          }
          resetForm();
        },
        onError: (error: any) => {
          showSnackbar(
            error || "Failed to submit form. Please try again.",
            "error"
          );
          error && setInvalidSubmissionError(error);
          console.error("Form submission failed:", error);
        },
      });
    } catch (err: any) {
      handleValidationErrors(err);
    }
  };

  const handleValidationErrors = (err: any) => {
    const newErrors = err.inner.reduce((acc: any, error: any) => {
      acc[error.path] = error.message;
      return acc;
    }, {});
    setFormErrors(newErrors);
    showSnackbar(JSON.stringify(Object.values(newErrors)), "error");
  };

  if (error) {
    console.error(error);
    // const isUnauthorized = error?.response?.status === 403;
    const errorMessage = true
      ? "You are not authorized to access the resource."
      : "Oops! Something went wrong! Please try again later.";

    return <ErrorMessage message={errorMessage} />;
  }

  const resetForm = () => {
    setFormErrors({});
    setFormValues((prev: any) =>
      Object.keys(prev).reduce((acc, key) => {
        if (["user_one", "user_two", "user_three"].indexOf(key) === -1) {
          acc[key] = "";
        } else {
          acc[key] = prev[key];
        }
        return acc;
      }, {} as any)
    );
    setNestedFieldValues({});
    setFormConfig(initialConfig);
    setFailedButtonState(null);
    showSnackbar("Form submitted successfully!", "success");
    serialNumberRef?.current?.focus();
    console.log("Form submission successful");
  };

  if (!formConfig) {
    return <div style={{ textAlign: "center" }}>Loading form...</div>;
  }

  if (formConfig?.form_schema?.form_fields?.length)
    return (
      <>
        {serial_number && (
          <StepBar config={config} isLoading={loading} error={isError} />
        )}
        <ThemeProvider theme={theme}>
          <CssBaseline />
          {invalidSubmissionError && (
            <p className={Style["error-message"]}>{invalidSubmissionError}</p>
          )}
          <Box className={Style["form-box"]}>
            <Typography
              variant="h4"
              gutterBottom
              align="left"
              sx={{
                color: "rgb(41, 44, 49)",
                fontSize: { xs: "1.5rem", md: "2rem" }, // Smaller font size for mobile
              }}
            >
              {formConfig.name}
            </Typography>
            <form>
              <div
                style={{
                  display: "flex",
                  flexWrap: "wrap",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                {formConfig.form_schema.form_fields.map((field) => {
                  if (!field.isVisible) return null;
                  const error = formErrors[field.name];

                  switch (field.type) {
                    case "text":
                    case "number":
                      return (
                        <InputField
                          key={field.name}
                          label={field.label}
                          name={field.name}
                          type={field.type}
                          value={formValues[field.name]}
                          placeholder={field.placeholder}
                          onKeyDown={(e: any) => {
                            if (e.key === "Enter") {
                              e.preventDefault();
                              e.target.blur();
                            }
                          }}
                          onChange={(e) => handleFieldChange(e, field)}
                          onBlur={(e) => handleFieldAction("onBlur", e, field)}
                          error={error}
                          marginRight={field.marginRight}
                          width={field.width}
                          readonly={field.readonly}
                          alignment={field.alignment}
                          ref={
                            field.name === "serial_number"
                              ? serialNumberRef
                              : null
                          }
                        />
                      );
                    case "textarea":
                      return (
                        <TextAreaField
                          key={field.name}
                          label={field.label}
                          name={field.name}
                          value={formValues[field.name]}
                          placeholder={field.placeholder}
                          onChange={(e) => handleFieldChange(e, field)}
                          onBlur={(e) => handleFieldAction("onBlur", e, field)}
                          error={error}
                          marginRight={field.marginRight}
                          width={field.width}
                          alignment={field.alignment}
                          readonly={field.readonly}
                        />
                      );

                    case "select":
                      return (
                        <SelectField
                          key={field.name}
                          label={field.label}
                          name={field.name}
                          readonly={field.readonly}
                          value={formValues[field.name]}
                          options={field.options || []}
                          placeholder={field.placeholder}
                          onChange={(e) => handleFieldChange(e, field)}
                          onBlur={(e) => handleFieldAction("onBlur", e, field)}
                          error={error}
                          marginRight={field.marginRight}
                          width={field.width}
                          alignment={field.alignment}
                        />
                      );
                    case "multi-select":
                      return (
                        <MultiSelectField
                          key={field.name}
                          label={field.label}
                          name={field.name}
                          value={formValues[field.name] || []}
                          options={field.options || []}
                          onChange={(e) => handleFieldChange(e, field)}
                          error={formErrors[field.name]}
                          width={field.width}
                          marginRight={field.marginRight}
                          readonly={field.readonly}
                        />
                      );
                    case "radio":
                      return (
                        <RadioField
                          key={field.name}
                          label={field.label}
                          name={field.name}
                          value={formValues[field.name] || ""}
                          options={field.options || []}
                          onChange={(e) => handleFieldChange(e, field)}
                          error={error}
                          marginRight={field.marginRight}
                          width={field.width}
                          alignment={field.alignment}
                        />
                      );

                    case "checkbox":
                      return (
                        <CheckboxField
                          key={field.name}
                          checked={formValues[field.name] === true}
                          label={field.label}
                          name={field.name}
                          value={formValues[field.name]}
                          onChange={(e) => handleFieldChange(e, field)}
                          error={error}
                          marginRight={field.marginRight}
                          width={field.width}
                          alignment={field.alignment}
                        />
                      );
                    case "date":
                      return (
                        <DateField
                          key={field.name}
                          label={field.label}
                          name={field.name}
                          value={formValues[field.name] || null}
                          onChange={(newValue: Date | null) => {
                            if (newValue) {
                              const localDate = new Date(
                                newValue.getTime() -
                                newValue.getTimezoneOffset() * 60000
                              );
                              handleFieldChange(
                                {
                                  target: {
                                    name: field.name,
                                    value: localDate,
                                  },
                                },
                                field
                              );
                            }
                          }}
                          error={formErrors[field.name]}
                          width={field.width}
                          marginRight={field.marginRight}
                          alignment={field.alignment}
                          readonly={field.readonly}
                          placeholder={field.placeholder}
                        />
                      );
                    case "component":
                      const CustomComponent = field.component_name
                        ? componentRegistry[field.component_name]
                        : null;
                      if (CustomComponent) {
                        const handleComponentActions = (event: any) => {
                          const action = field.actions.find(
                            (a) => a.action_type === "onClick"
                          );
                          if (action) {
                            handleAction(
                              action.handler,
                              field,
                              event,
                              handleFormConfigUpdate,
                              setModalState
                            );
                          }
                          return;
                        };
                        return (
                          <CustomComponent
                            key={field.name}
                            label={field.label}
                            name={field.name}
                            width={field.width}
                            value={formValues[field.name]}
                            marginRight={field.marginRight}
                            disabled={isLoading}
                            handleSubmit={handleSubmit}
                            options={field.options || []}
                            setFailedButtonState={setFailedButtonState}
                            handleComponentActions={handleComponentActions}
                            failedButtonState={failedButtonState}
                            config={field}
                            onSubmit={handleSubmit}
                            nestedFieldValues={nestedFieldValues}
                            handleFormConfigUpdate={handleFormConfigUpdate}
                          />
                        );
                      }
                      return null;
                    default:
                      return null;
                  }
                })}
              </div>
              {showSubmitButton && (
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    marginTop: 2,
                  }}
                >
                  <Button
                    className={Style.submitBtn}
                    variant="contained"
                    onClick={() => handleSubmit()}
                    disabled={isLoading}
                    sx={{
                      backgroundColor: "#3e4146",
                      fontWeight: "bold",
                    }}
                  >
                    {isLoading ? "Submitting..." : "Submit"}
                  </Button>
                </Box>
              )}
            </form>
            <ConfirmationModal
              open={isOpen}
              title={title}
              onCancel={onCancel}
              onClose={handleModalClose}
            />
          </Box>
          {initialConfig?.code && <FormEvents code={initialConfig.code} />}
        </ThemeProvider>
      </>
    );
};

export default DynamicForm;
