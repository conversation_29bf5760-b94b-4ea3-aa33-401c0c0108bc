import React, { useCallback, useState } from "react";
import { useDropzone } from "react-dropzone";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import Button from "@mui/material/Button";
import Box from "@mui/material/Box";
import { useUploadAoiFile } from "../hooks/useUploadAoiFiles";
import { useUploadWorkOrderFile } from "../hooks/useUploadWorkOrderFiles";
import styles from "../styles/Upload.module.scss";
import { useSnackbar } from "../context/SnackBarContext";

const UploadPage: React.FC = () => {
  const [file, setFile] = useState<File | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [woFile, setWoFile] = useState<File | null>(null);
  const [woError, setWoError] = useState<string | null>(null);
  const { showSnackbar } = useSnackbar();
  const {
    mutate: mutateAoi,
    isPending: isLoading,
    isSuccess,
    isError,
  } = useUploadAoiFile();
  const {
    mutate: mutateWo,
    isPending: woIsLoading,
    isSuccess: woIsSuccess,
    isError: woIsError,
  } = useUploadWorkOrderFile();

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      setFile(acceptedFiles[0]);
      setError(null);
    }
  }, []);

  const onWoDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      setWoFile(acceptedFiles[0]);
      setWoError(null);
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [
        ".xlsx",
      ],
    },
    maxFiles: 1,
  });

  const { getRootProps: getWoRootProps, getInputProps: getWoInputProps, isDragActive: isWoDragActive } = useDropzone({
    onDrop: onWoDrop,
    accept: {
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [
        ".xlsx",
      ],
    },
    maxFiles: 1,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!file) {
      setError("Please select or drag a file to upload.");
      return;
    }

    const formData = new FormData();
    formData.append("file", file);

    mutateAoi(formData, {
      onSuccess: () => {
        setFile(null);
        setError(null);
      },
      onError: (error) => {
        console.error("Form submission failed:", error);
        setFile(null);
        console.error("Upload failed:", error);
      },
    });
  };

  const handleWoSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!woFile) {
      setWoError("Please select or drag a file to upload.");
      return;
    }

    const formData = new FormData();
    formData.append("file", woFile);

    mutateWo(formData, {
      onSuccess: () => {
        setWoFile(null);
        setWoError(null);
      },
      onError: (error) => {
        console.error("Form submission failed:", error);
        setWoFile(null);
        console.error("Upload failed:", error);
      },
    });
  };

  return (
    <Box className={styles["upload-container"]}>
      <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: { xs: 2, md: 4 }, width: '100%' }}>
        <Box className={styles["upload-section"]}>
          <h1 className={styles["title"]}>AOI Excel Upload</h1>
          <form onSubmit={handleSubmit} className={styles["form"]}>
            <Box
              {...getRootProps()}
              className={`${styles["upload-box"]} ${isDragActive ? styles["drag-active"] : ""}`}
            >
              <input {...getInputProps()} />
              <CloudUploadIcon className={styles["upload-icon"]} />
              <p>
                {file ? file.name : "Drag and drop a file here, or click to browse"}
              </p>
            </Box>
            <Button
              variant="contained"
              type="submit"
              disabled={isLoading}
              className={styles["upload-button"]}
            >
              {isLoading ? "Uploading AOI File..." : "Upload AOI File"}
            </Button>
          </form>
          {isSuccess && (
            <p className={styles["status-message"]}>
              AOI file uploaded successfully!
            </p>
          )}
          {isError && (
            <p className={styles["error-message"]}>
              Failed to upload AOI file. Please try again.
            </p>
          )}
          {error && <p className={styles["error-message"]}>{error}</p>}
        </Box>

        <Box className={styles["upload-section"]}>
          <h1 className={styles["title"]}>Work Order Excel Upload</h1>
          <form onSubmit={handleWoSubmit} className={styles["form"]}>
            <Box
              {...getWoRootProps()}
              className={`${styles["upload-box"]} ${isWoDragActive ? styles["drag-active"] : ""}`}
            >
              <input {...getWoInputProps()} />
              <CloudUploadIcon className={styles["upload-icon"]} />
              <p>
                {woFile ? woFile.name : "Drag and drop a file here, or click to browse"}
              </p>
            </Box>
            <Button
              variant="contained"
              type="submit"
              disabled={woIsLoading}
              className={styles["upload-button"]}
            >
              {woIsLoading ? "Uploading Work Order File..." : "Upload Work Order File"}
            </Button>
          </form>
          {woIsSuccess && (
            <p className={styles["status-message"]}>
              Work Order file uploaded successfully!
            </p>
          )}
          {woIsError && (
            <p className={styles["error-message"]}>
              Failed to upload Work Order file. Please try again.
            </p>
          )}
          {woError && <p className={styles["error-message"]}>{woError}</p>}
        </Box>
      </Box>
    </Box>
  );
};

export default UploadPage;
