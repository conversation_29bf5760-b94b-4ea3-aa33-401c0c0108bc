import React, { useEffect, useRef, useState } from "react";
import InputField from "../components/TextInput";
import Style from "../styles/ProductRouting.module.scss";
import { useRoutinBySerialNumber } from "../hooks/useForm";
import ProductRouting from "../components/ProductRouting";
import { theme } from "../utils/muiTheme";
import { ThemeProvider } from "@emotion/react";

const fieldConfig = {
  name: "serial_number",
  type: "text",
  label: "Serial number",
  regex: "",
  width: {
    mobile: "100%",
    tablet: "44%",
    desktop: "45%",
  },
  position: 5,
  readonly: false,
  required: true,
  isVisible: true,
  marginRight: {
    mobile: "0px",
    tablet: "0px",
    desktop: "0px",
  },
  placeholder: "Routes By Serial Number",
  used_in_grid: false,
};

function ProductRoute() {
  const [serialNUmber, setSerialNumber] = useState("");
  const [fieldError, setFieldError] = useState("");
  const serialNumberRef = useRef<HTMLInputElement>(null);

  const {
    data: config,
    isLoading: loading,
    isError,
    refetch: fetchRouteBySerialNumber,
  } = useRoutinBySerialNumber(serialNUmber, false);

  useEffect(() => {
    serialNumberRef.current?.focus();
  }, []);

  const handleFieldChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setSerialNumber(value.trim());
  };

  const handleSerialNumberBlur = () => {
    if (serialNUmber.length < 15) {
      setFieldError("Please enter valid serial number");
    } else {
      fetchRouteBySerialNumber();
      setFieldError("");
    }
    return null;
  };

  return (
    <>
      <ThemeProvider theme={theme}>
        <p className={Style.header_text}>
          Products Routes By Serial Number (HE317100#D225105425300038)
        </p>
        <div className={Style.route_wrapper}>
          <InputField
            key={fieldConfig.name}
            label={fieldConfig.label}
            name={fieldConfig.name}
            type={fieldConfig.type}
            value={serialNUmber}
            placeholder={fieldConfig.placeholder}
            onKeyDown={(e: any) => {
              if (e.key === "Enter") {
                e.preventDefault();
                e.target.blur();
              }
            }}
            onChange={(e) => handleFieldChange(e)}
            onBlur={() => handleSerialNumberBlur()}
            error={fieldError}
            marginRight={fieldConfig.marginRight}
            width={fieldConfig.width}
            readonly={fieldConfig.readonly}
            ref={serialNumberRef}
          />
        </div>
      </ThemeProvider>
      {serialNUmber && (
        <ProductRouting config={config} isLoading={loading} error={isError} />
      )}
    </>
  );
}

export default ProductRoute;
