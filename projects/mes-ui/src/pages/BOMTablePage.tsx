import React, { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Container,
  Typography,
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import DynamicTable, { ColumnConfig } from '../components/DynamicTable';
import { useBOMTable, BOMFilter } from '../hooks/useBOMTable';
import { useSnackbar } from '../context/SnackBarContext';

// Import our reusable components
import FilterChips from '../components/FilterChips';
import FilterDrawer from '../components/FilterDrawer';
import CreateBOMModal from '../components/CreateBOMModal';
import ProductBOMsModal from '../components/ProductBOMsModal';
import styles from '../styles/EventList.module.scss';

const BOMTablePage: React.FC = () => {
  const navigate = useNavigate();
  const { showSnackbar } = useSnackbar();

  // State for create BOM modal
  const [createModalOpen, setCreateModalOpen] = useState(false);
  const [productBOMsModalOpen, setProductBOMsModalOpen] = useState(false);
  const [selectedProductCode, setSelectedProductCode] = useState('');

  // State for filters
  const [filterDrawerOpen, setFilterDrawerOpen] = useState(false);
  const [filterInputs, setFilterInputs] = useState<BOMFilter>({
    name: '',
    code: '',
    product: '',
    version: '',
    status: '',
    effective_date: '',
    created_at: '',
    updated_at: '',
    search: '',
  });

  // State for table
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Compute the actual filters to use for the API
  const [activeFilters, setActiveFilters] = useState<BOMFilter>({
    page: 1,
    page_size: 10,
  });

  // Fetch data using the custom hook
  const { data, isLoading, error } = useBOMTable(activeFilters);

  // Define table columns
  const columns: ColumnConfig[] = [
    { key: 'id', label: 'BOM ID', width: '8%', align: 'left' },
    { key: 'name', label: 'BOM Name', width: '15%' },
    { key: 'code', label: 'BOM Code', width: '12%' },
    { 
      key: 'product_detail', 
      label: 'Product', 
      width: '15%',
      format: (value: any) => value?.name || '-'
    },
    { key: 'version', label: 'Version', width: '8%' },
    { 
      key: 'status', 
      label: 'Status', 
      width: '10%',
      format: (value: string) => (
        <span className={`${styles.status} ${styles[value.toLowerCase()]}`}>
          {value}
        </span>
      )
    },
    { 
      key: 'effective_date', 
      label: 'Effective Date', 
      width: '12%',
      format: (value: string) => new Date(value).toLocaleDateString()
    },
    { 
      key: 'components', 
      label: 'Components', 
      width: '10%',
      format: (_value: any, row: any) => (
        <Button
          variant="outlined"
          size="small"
          onClick={(e) => {
            e.stopPropagation();
            setSelectedProductCode(row.product_detail?.code || '');
            setProductBOMsModalOpen(true);
          }}
          sx={{
            textTransform: 'none',
            borderColor: '#e2e8f0',
            color: '#64748b',
            '&:hover': {
              borderColor: '#94a3b8',
              backgroundColor: '#f8fafc',
            },
          }}
        >
          View
        </Button>
      )
    },
    { 
      key: 'created_at', 
      label: 'Created At', 
      width: '10%',
      format: (value: string) => new Date(value).toLocaleDateString()
    },
    { 
      key: 'created_by_username', 
      label: 'Created By', 
      width: '10%'
    },
  ];

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
    setActiveFilters(prev => ({
      ...prev,
      page: newPage + 1, // API uses 1-based indexing
    }));
  };

  // Handle rows per page change
  const handleRowsPerPageChange = (newRowsPerPage: number) => {
    setRowsPerPage(newRowsPerPage);
    setPage(0); // Reset to first page
    setActiveFilters(prev => ({
      ...prev,
      page: 1, // Reset to first page
      page_size: newRowsPerPage,
    }));
  };

  // Handle filter input change
  const handleFilterInputChange = (field: keyof BOMFilter, value: string) => {
    setFilterInputs(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle search
  const handleSearch = (searchTerm: string) => {
    setFilterInputs(prev => ({
      ...prev,
      name: searchTerm,
    }));

    setActiveFilters(prev => ({
      ...prev,
      name: searchTerm,
      page: 1, // Reset to first page on search
    }));
  };

  // Apply filters
  const handleApplyFilters = () => {
    const newFilters: BOMFilter = {
      page: 1,
      page_size: rowsPerPage,
    };

    if (filterInputs.name) newFilters.name = filterInputs.name;
    if (filterInputs.code) newFilters.code = filterInputs.code;
    if (filterInputs.product) newFilters.product = filterInputs.product;
    if (filterInputs.version) newFilters.version = filterInputs.version;
    if (filterInputs.status) newFilters.status = filterInputs.status;
    if (filterInputs.effective_date) newFilters.effective_date = filterInputs.effective_date;
    if (filterInputs.created_at) newFilters.created_at = filterInputs.created_at;
    if (filterInputs.updated_at) newFilters.updated_at = filterInputs.updated_at;

    setActiveFilters(newFilters);
    setPage(0);
    setFilterDrawerOpen(false);
  };

  // Reset filters
  const handleResetFilters = () => {
    setFilterInputs({
      name: '',
      code: '',
      product: '',
      version: '',
      status: '',
      effective_date: '',
      created_at: '',
      updated_at: '',
      search: '',
    });
  };

  // Check if any filters are applied
  const hasActiveFilters = () => {
    return !!(
      activeFilters.name ||
      activeFilters.code ||
      activeFilters.product ||
      activeFilters.version ||
      activeFilters.status ||
      activeFilters.effective_date ||
      activeFilters.created_at ||
      activeFilters.updated_at
    );
  };

  // Handle view BOM
  const handleViewBOM = useCallback((row: any) => {
    navigate(`/bom/${row.id}`);
  }, [navigate]);

  // Handle create new BOM
  const handleCreateBOM = () => {
    setCreateModalOpen(true);
  };

  // Handle create BOM success
  const handleCreateBOMSuccess = () => {
    showSnackbar('BOM created successfully', 'success');
  };

  // Create filter fields for the filter drawer
  const filterFields = [
    { id: 'name', label: 'BOM Name', value: filterInputs.name || '' },
    { id: 'code', label: 'BOM Code', value: filterInputs.code || '' },
    { id: 'product', label: 'Product', value: filterInputs.product || '' },
    { id: 'version', label: 'Version', value: filterInputs.version || '' },
    { id: 'status', label: 'Status', value: filterInputs.status || '' },
    { id: 'effective_date', label: 'Effective Date', value: filterInputs.effective_date || '' },
    { id: 'created_at', label: 'Created At', value: filterInputs.created_at || '' },
    { id: 'updated_at', label: 'Updated At', value: filterInputs.updated_at || '' },
  ];

  // Create a map of filter display names
  const filterDisplayNames = {
    name: 'BOM Name',
    code: 'BOM Code',
    product: 'Product',
    version: 'Version',
    status: 'Status',
    effective_date: 'Effective Date',
    created_at: 'Created At',
    updated_at: 'Updated At',
    search: 'Search',
  };

  // Handle clearing a single filter
  const handleClearFilter = (key: string) => {
    setActiveFilters(prev => {
      const newFilters = { ...prev };
      delete newFilters[key as keyof BOMFilter];
      return newFilters;
    });

    if (key in filterInputs) {
      setFilterInputs(prev => ({
        ...prev,
        [key]: '',
      }));
    }
  };

  // Handle clearing all filters
  const handleClearAllFilters = () => {
    setActiveFilters({
      page: 1,
      page_size: rowsPerPage,
    });
    setFilterInputs({
      name: '',
      code: '',
      product: '',
      version: '',
      status: '',
      effective_date: '',
      created_at: '',
      updated_at: '',
      search: '',
    });
  };

  // Extract active filters for the FilterChips component
  const extractActiveFilters = () => {
    const filters: Record<string, string> = {};

    if (activeFilters.name) filters.name = activeFilters.name;
    if (activeFilters.code) filters.code = activeFilters.code;
    if (activeFilters.product) filters.product = activeFilters.product;
    if (activeFilters.version) filters.version = activeFilters.version;
    if (activeFilters.status) filters.status = activeFilters.status;
    if (activeFilters.effective_date) filters.effective_date = activeFilters.effective_date;
    if (activeFilters.created_at) filters.created_at = activeFilters.created_at;
    if (activeFilters.updated_at) filters.updated_at = activeFilters.updated_at;
    if (activeFilters.search) filters.search = activeFilters.search;

    return filters;
  };

  return (
    <Container maxWidth="xl" sx={{ py: 2, px: 0 }}>
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: 2,
        mt: 0,
        pb: 1.5,
        borderBottom: '1px solid #e2e8f0'
      }}>
        <Typography
          variant="h5"
          component="h1"
          sx={{
            fontWeight: 600,
            fontSize: '20px',
            color: '#1a202c',
            letterSpacing: '-0.5px',
            fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif',
          }}
        >
          BOM Listing
        </Typography>

        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="contained"
            startIcon={<AddIcon sx={{ fontSize: 16 }} />}
            onClick={handleCreateBOM}
            sx={{
              backgroundColor: '#0f172a',
              color: '#fff',
              borderRadius: '4px',
              textTransform: 'none',
              fontWeight: 500,
              boxShadow: 'none',
              fontSize: '13px',
              padding: '6px 12px',
              height: '36px',
              fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif',
              '&:hover': {
                backgroundColor: '#1e293b',
              }
            }}
          >
            Add BOM
          </Button>
        </Box>
      </Box>

      {hasActiveFilters() && (
        <FilterChips
          filters={extractActiveFilters()}
          onClearFilter={handleClearFilter}
          onClearAll={handleClearAllFilters}
          displayNames={filterDisplayNames}
        />
      )}

      <DynamicTable
        headers={columns}
        data={data?.results || []}
        count={data?.count || 0}
        page={page}
        rowsPerPage={rowsPerPage}
        loading={isLoading}
        error={error}
        onPageChange={handlePageChange}
        onRowsPerPageChange={handleRowsPerPageChange}
        rowsPerPageOptions={[5, 10, 25, 50]}
        actions={{
          onView: handleViewBOM,
        }}
        onRowClick={handleViewBOM}
        onSearch={handleSearch}
        searchPlaceholder="Search BOMs by name..."
      />

      {/* Filter Drawer */}
      <FilterDrawer
        open={filterDrawerOpen}
        onClose={() => setFilterDrawerOpen(false)}
        title="Filter BOMs"
        fields={filterFields}
        onFieldChange={(id, value) => handleFilterInputChange(id as keyof BOMFilter, value)}
        onApply={handleApplyFilters}
        onReset={handleResetFilters}
      />

      {/* Create BOM Modal */}
      <CreateBOMModal
        open={createModalOpen}
        onClose={() => setCreateModalOpen(false)}
        onSuccess={handleCreateBOMSuccess}
      />

      {/* Product BOMs Modal */}
      <ProductBOMsModal
        open={productBOMsModalOpen}
        onClose={() => setProductBOMsModalOpen(false)}
        productCode={selectedProductCode}
      />
    </Container>
  );
};

export default BOMTablePage; 