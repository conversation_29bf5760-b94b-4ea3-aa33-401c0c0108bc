import React, { useState } from "react";
import {
  TextField,
  Button,
  Typography,
  Box,
  InputAdornment,
  IconButton,
} from "@mui/material";
import * as Yup from "yup";
import { useLogin } from "../hooks/useLoginLogout";
import styles from "../styles/LoginPage.module.scss";
import sampleImage from "../assets/login_image.png";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import useScreen from "../hooks/useScreenSize";

const LoginPage: React.FC = () => {
  const { mutate, isPending: isLoading } = useLogin();
  const [formState, setFormState] = useState({
    username: "",
    password: "",
  });

  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [showPassword, setShowPassword] = useState(false);
  const { device } = useScreen();

  const validationSchema = Yup.object().shape({
    username: Yup.string()
      .required("Username is required")
      .min(3, "Username must be at least 3 characters"),
    password: Yup.string()
      .required("Password is required")
      .min(6, "Password must be at least 6 characters"),
  });

  const togglePasswordVisibility = () => {
    setShowPassword((prev) => !prev);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setErrors((prevState) => ({
      ...prevState,
      [name]: "",
    }));
    setFormState((prevState) => ({
      ...prevState,
      [name]: value?.trim?.() ?? value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await validationSchema.validate(formState, { abortEarly: false });
      setErrors({});
      mutate(formState);
      console.log("Form submitted successfully:", formState);
    } catch (err: any) {
      const validationErrors: { [key: string]: string } = {};
      err.inner.forEach((error: Yup.ValidationError) => {
        if (error.path) validationErrors[error.path] = error.message;
      });
      setErrors(validationErrors);
    }
  };

  return (
    <div className={styles["login-container"]}>
      <div
        className={styles["left-section"]}
        style={
          {
            display: device !== "desktop" ? "none" : undefined,
            "--bg-image": `url(${sampleImage})`,
            flex: 1,
            background: "var(--bg-image) no-repeat center center",
            backgroundSize: "cover",
            margin: "0px 0px 0px 0px",
          } as React.CSSProperties
        }
      />
      <div className={styles["right-section"]}>
        <Box className={styles["form-box"]}>
          <Typography variant="h4" className={styles["title"]}>
            Login
          </Typography>
          <form onSubmit={handleSubmit}>
            <TextField
              fullWidth
              label="Username"
              variant="outlined"
              name="username"
              value={formState.username}
              onChange={handleChange}
              error={!!errors.username}
              helperText={errors.username}
              sx={{ marginBottom: "1rem" }}
            />
            <TextField
              fullWidth
              label="Password"
              variant="outlined"
              type={showPassword ? "text" : "password"}
              name="password"
              value={formState.password}
              onChange={handleChange}
              error={!!errors.password}
              helperText={errors.password}
              sx={{ marginBottom: "1rem" }}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={togglePasswordVisibility}
                      edge="end"
                      aria-label="toggle password visibility"
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
            <Button
              type="submit"
              fullWidth
              variant="contained"
              disabled={isLoading}
              className={styles["submit-button"]}
            >
              {isLoading ? `Logging in...` : `Login`}
            </Button>
          </form>
        </Box>
      </div>
    </div>
  );
};

export default LoginPage;
