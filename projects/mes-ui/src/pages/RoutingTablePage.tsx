import React, { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Container,
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import DynamicTable, { ColumnConfig } from '../components/DynamicTable';
import { useRoutingTable, RoutingFilter, routingUtils } from '../hooks/useRoutingTable';
import {
  PageContainer,
  HeaderContainer,
  PageTitle,
  ActionButton,
  FilterContainer,
  FilterText,
  ResetButton,
  ButtonGroupContainer
} from '../styles/PageTable.styles';

// Import our reusable components
import FilterChips from '../components/FilterChips';
import FilterDrawer from '../components/FilterDrawer';

const RoutingTablePage: React.FC = () => {
  const navigate = useNavigate();

  // State for filters
  const [filterDrawerOpen, setFilterDrawerOpen] = useState(false);
  const [filterInputs, setFilterInputs] = useState<RoutingFilter>({
    name: '',
    code: '',
    created_at: '',
    updated_at: '',
    search: '',
  });

  // State for table
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Compute the actual filters to use for the API
  const [activeFilters, setActiveFilters] = useState<RoutingFilter>({
    page: 1,
    page_size: 10,
  });

  // Fetch data using the custom hook
  const { data, isLoading, error } = useRoutingTable(activeFilters);

  // Define table columns
  const columns: ColumnConfig[] = [
    { key: 'id', label: 'ID', width: '20%', align: 'left' },
    { key: 'name', label: 'Name', width: '20%' },
    { key: 'description', label: 'description', width: '30%' },
    {
      key: 'created_by',
      label: 'Created By',
      width: '20%',
      format: (value: any) => routingUtils.getCreatorName(value),
    },
    {
      key: 'created_at',
      label: 'Created At',
      width: '10%',
      format: (value: any) => routingUtils.formatDate(value),
    },
  ];

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
    setActiveFilters(prev => ({
      ...prev,
      page: newPage + 1, // API uses 1-based indexing
    }));
  };

  // Handle rows per page change
  const handleRowsPerPageChange = (newRowsPerPage: number) => {
    setRowsPerPage(newRowsPerPage);
    setPage(0); // Reset to first page
    setActiveFilters(prev => ({
      ...prev,
      page: 1,
      page_size: newRowsPerPage,
    }));
  };

  // Handle filter input change
  const handleFilterInputChange = (field: keyof RoutingFilter, value: string) => {
    setFilterInputs(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle search
  const handleSearch = (searchTerm: string) => {
    setFilterInputs(prev => ({
      ...prev,
      search: searchTerm,
    }));

    setActiveFilters(prev => ({
      ...prev,
      search: searchTerm,
      page: 1, // Reset to first page on search
    }));
  };

  // Apply filters
  const handleApplyFilters = () => {
    // Create a new filter object with only non-empty values
    const newFilters: RoutingFilter = {
      page: 1, // Reset to first page
      page_size: rowsPerPage,
    };

    if (filterInputs.name) newFilters.name = filterInputs.name;
    if (filterInputs.code) newFilters.code = filterInputs.code;
    if (filterInputs.created_at) newFilters.created_at = filterInputs.created_at;
    if (filterInputs.updated_at) newFilters.updated_at = filterInputs.updated_at;

    setActiveFilters(newFilters);
    setPage(0); // Reset to first page in the UI
    setFilterDrawerOpen(false);
  };

  // Reset filters
  const handleResetFilters = () => {
    setFilterInputs({
      name: '',
      code: '',
      created_at: '',
      updated_at: '',
    });
  };

  // Check if any filters are applied
  const hasActiveFilters = () => {
    return !!(
      activeFilters.name ||
      activeFilters.code ||
      activeFilters.created_at ||
      activeFilters.updated_at ||
      activeFilters.search
    );
  };

  // Handle view routing
  const handleViewRouting = useCallback((row: any) => {
    navigate(`/routing/${row.id}`);
  }, [navigate]);

  // Handle edit routing
  const handleEditRouting = useCallback((row: any) => {
    // Navigate to routing page
    navigate(`/routing/${row.id}`);
  }, [navigate]);

  // Handle create new routing
  const handleCreateRouting = () => {
    navigate('/routing/create');
  };

  // Create filter fields for the filter drawer
  const filterFields = [
    { id: 'name', label: 'Name', value: filterInputs.name || '' },
    { id: 'code', label: 'Code', value: filterInputs.code || '' },
    { id: 'created_at', label: 'Created At', value: filterInputs.created_at || '' },
    { id: 'updated_at', label: 'Updated At', value: filterInputs.updated_at || '' },
  ];

  // Create a map of filter display names
  const filterDisplayNames = {
    name: 'Name',
    code: 'Code',
    created_at: 'Created At',
    updated_at: 'Updated At',
    search: 'Search',
  };

  // Handle clearing a single filter
  const handleClearFilter = (key: string) => {
    setActiveFilters(prev => {
      const newFilters = { ...prev };
      delete newFilters[key as keyof RoutingFilter];
      return newFilters;
    });

    // Also clear the input if it's one of our filter inputs
    if (key in filterInputs) {
      setFilterInputs(prev => ({
        ...prev,
        [key]: '',
      }));
    }
  };

  // Handle clearing all filters
  const handleClearAllFilters = () => {
    setActiveFilters({
      page: 1,
      page_size: rowsPerPage,
    });
    setFilterInputs({
      name: '',
      code: '',
      created_at: '',
      updated_at: '',
    });
  };

  // Extract active filters for the FilterChips component
  const extractActiveFilters = () => {
    const filters: Record<string, string> = {};

    if (activeFilters.name) filters.name = activeFilters.name;
    if (activeFilters.code) filters.code = activeFilters.code;
    if (activeFilters.created_at) filters.created_at = activeFilters.created_at;
    if (activeFilters.updated_at) filters.updated_at = activeFilters.updated_at;
    if (activeFilters.search) filters.search = activeFilters.search;

    return filters;
  };

  return (
    <Container maxWidth="xl" sx={{ py: 2, px: 0 }}>
      <HeaderContainer>
        <PageTitle variant="h5" component="h1">
          Workflow Listing
        </PageTitle>

        <ButtonGroupContainer>
          <ActionButton
            variant="contained"
            startIcon={<AddIcon sx={{ fontSize: 16 }} />}
            onClick={handleCreateRouting}
          >
            Add Workflow
          </ActionButton>
        </ButtonGroupContainer>
      </HeaderContainer>

      {hasActiveFilters() && (
        <FilterContainer>
          <FilterText variant="body2">
            Filters applied
          </FilterText>
          <ResetButton
            variant="text"
            size="small"
            onClick={handleClearAllFilters}
          >
            Reset Filters
          </ResetButton>
        </FilterContainer>
      )}

      <FilterChips
        filters={extractActiveFilters()}
        displayNames={filterDisplayNames}
        onClearFilter={handleClearFilter}
        onClearAll={handleClearAllFilters}
      />

      <DynamicTable
        headers={columns}
        data={data?.results || []}
        loading={isLoading}
        error={error}
        count={data?.count || 0}
        page={page}
        rowsPerPage={rowsPerPage}
        onPageChange={handlePageChange}
        onRowsPerPageChange={handleRowsPerPageChange}
        onSearch={handleSearch}
        searchPlaceholder="Search workflows..."
        onRowClick={handleViewRouting}
        actions={{
          onEdit: handleEditRouting,
        }}
      />

      <FilterDrawer
        open={filterDrawerOpen}
        onClose={() => setFilterDrawerOpen(false)}
        fields={filterFields}
        onFieldChange={(id, value) => handleFilterInputChange(id as keyof RoutingFilter, value)}
        onApply={handleApplyFilters}
        onReset={handleResetFilters}
        title="Filter Workflows"
      />
    </Container>
  );
};

export default RoutingTablePage;
