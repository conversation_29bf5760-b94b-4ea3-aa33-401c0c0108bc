import React, { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Container,
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import DynamicTable, { ColumnConfig } from '../components/DynamicTable';
import { useProductTable, ProductFilter } from '../hooks/useProductTable';
import { useSnackbar } from '../context/SnackBarContext';
import {
  PageContainer,
  HeaderContainer,
  PageTitle,
  ActionButton,
  FilterContainer,
  FilterText,
  ResetButton,
  ButtonGroupContainer
} from '../styles/PageTable.styles';

// Import our reusable components
import FilterChips from '../components/FilterChips';
import FilterDrawer from '../components/FilterDrawer';
import CreateProductModal from '../components/CreateProductModal';
import styles from '../styles/EventList.module.scss';

const ProductTablePage: React.FC = () => {
  const navigate = useNavigate();
  const { showSnackbar } = useSnackbar();

  // State for create product modal
  const [createModalOpen, setCreateModalOpen] = useState(false);

  // State for filters
  const [filterDrawerOpen, setFilterDrawerOpen] = useState(false);
  const [filterInputs, setFilterInputs] = useState<ProductFilter>({
    name: '',
    code: '',
    created_at: '',
    updated_at: '',
    search: '',
  });

  // State for table
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Compute the actual filters to use for the API
  const [activeFilters, setActiveFilters] = useState<ProductFilter>({
    page: 1,
    page_size: 10,
  });

  // Fetch data using the custom hook
  const { data, isLoading, error } = useProductTable(activeFilters);

  // Debug: Log the data
  console.log('Product data:', data);

  // Define table columns
  const columns: ColumnConfig[] = [
    { key: 'id', label: 'Product ID', width: '15%', align: 'left' },
    { key: 'name', label: 'Name', width: '20%' },
    { key: 'description', label: 'Description', width: '45%' },
    { key: 'routing_id', label: 'Routing Id', width: '10%' },
    {
      key: 'status',
      label: 'Status',
      width: '10%',
      format: (_value: any, row: any) => {
        // Simulate status based on ID for demo purposes
        const statusMap: Record<number, { label: string, className: string }> = {
          1: { label: 'In Production', className: 'in-production' },
          2: { label: 'Pending', className: 'pending' },
          3: { label: 'Completed', className: 'completed' },
          4: { label: 'In Production', className: 'in-production' },
          5: { label: 'Pending', className: 'pending' },
        };

        const status = statusMap[row.id % 5 + 1] || { label: 'In Production', className: 'in-production' };

        // Create a class string that matches the CSS classes in EventList.module.scss
        const statusClass = `${styles.status} ${status.className === 'in-production' ? styles['in-production'] :
          status.className === 'pending' ? styles.pending :
            status.className === 'completed' ? styles.completed : ''}`;

        return (
          <span className={statusClass}>
            {status.label}
          </span>
        );
      }
    },
  ];

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
    setActiveFilters(prev => ({
      ...prev,
      page: newPage + 1, // API uses 1-based indexing
    }));
  };

  // Handle rows per page change
  const handleRowsPerPageChange = (newRowsPerPage: number) => {
    setRowsPerPage(newRowsPerPage);
    setPage(0); // Reset to first page
    setActiveFilters(prev => ({
      ...prev,
      page: 1, // Reset to first page
      page_size: newRowsPerPage,
    }));
  };

  // Handle filter input change
  const handleFilterInputChange = (field: keyof ProductFilter, value: string) => {
    setFilterInputs(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle search - search by name instead of general search
  const handleSearch = (searchTerm: string) => {
    setFilterInputs(prev => ({
      ...prev,
      name: searchTerm, // Use name field instead of search
    }));

    setActiveFilters(prev => ({
      ...prev,
      name: searchTerm, // Use name field instead of search
      page: 1, // Reset to first page on search
    }));
  };

  // Apply filters
  const handleApplyFilters = () => {
    // Create a new filter object with only non-empty values
    const newFilters: ProductFilter = {
      page: 1, // Reset to first page
      page_size: rowsPerPage,
    };

    if (filterInputs.name) newFilters.name = filterInputs.name;
    if (filterInputs.code) newFilters.code = filterInputs.code;
    if (filterInputs.created_at) newFilters.created_at = filterInputs.created_at;
    if (filterInputs.updated_at) newFilters.updated_at = filterInputs.updated_at;

    setActiveFilters(newFilters);
    setPage(0); // Reset to first page in the UI
    setFilterDrawerOpen(false);
  };

  // Reset filters
  const handleResetFilters = () => {
    setFilterInputs({
      name: '',
      code: '',
      created_at: '',
      updated_at: '',
    });
  };

  // Check if any filters are applied
  const hasActiveFilters = () => {
    return !!(
      activeFilters.name ||
      activeFilters.code ||
      activeFilters.created_at ||
      activeFilters.updated_at
    );
  };

  // Handle view product
  const handleViewProduct = useCallback((row: any) => {
    navigate(`/product/${row.id}`);
  }, [navigate]);

  // Handle edit product
  const handleEditProduct = useCallback((row: any) => {
    // Navigate to product page
    navigate(`/product/${row.id}`);
  }, [navigate]);

  // Handle create new product
  const handleCreateProduct = () => {
    setCreateModalOpen(true);
  };

  // Handle create product success
  const handleCreateProductSuccess = () => {
    showSnackbar('Product created successfully', 'success');
  };



  // Create filter fields for the filter drawer
  const filterFields = [
    { id: 'name', label: 'Name', value: filterInputs.name || '' },
    { id: 'code', label: 'Code', value: filterInputs.code || '' },
    { id: 'created_at', label: 'Created At', value: filterInputs.created_at || '' },
    { id: 'updated_at', label: 'Updated At', value: filterInputs.updated_at || '' },
  ];

  // Create a map of filter display names
  const filterDisplayNames = {
    name: 'Name',
    code: 'Code',
    created_at: 'Created At',
    updated_at: 'Updated At',
    search: 'Search',
  };

  // Handle clearing a single filter
  const handleClearFilter = (key: string) => {
    setActiveFilters(prev => {
      const newFilters = { ...prev };
      delete newFilters[key as keyof ProductFilter];
      return newFilters;
    });

    // Also clear the input if it's one of our filter inputs
    if (key in filterInputs) {
      setFilterInputs(prev => ({
        ...prev,
        [key]: '',
      }));
    }
  };

  // Handle clearing all filters
  const handleClearAllFilters = () => {
    setActiveFilters({
      page: 1,
      page_size: rowsPerPage,
    });
    setFilterInputs({
      name: '',
      code: '',
      created_at: '',
      updated_at: '',
    });
  };

  // Extract active filters for the FilterChips component
  const extractActiveFilters = () => {
    const filters: Record<string, string> = {};

    if (activeFilters.name) filters.name = activeFilters.name;
    if (activeFilters.code) filters.code = activeFilters.code;
    if (activeFilters.created_at) filters.created_at = activeFilters.created_at;
    if (activeFilters.updated_at) filters.updated_at = activeFilters.updated_at;
    if (activeFilters.search) filters.search = activeFilters.search;

    return filters;
  };

  return (
    <Container maxWidth="xl" sx={{ py: 2, px: 0 }}>
      <HeaderContainer>
        <PageTitle variant="h5" component="h1">
          Product Listing
        </PageTitle>

        <ButtonGroupContainer>
          <ActionButton
            variant="contained"
            startIcon={<AddIcon sx={{ fontSize: 16 }} />}
            onClick={handleCreateProduct}
          >
            Add Product
          </ActionButton>
        </ButtonGroupContainer>
      </HeaderContainer>

      {hasActiveFilters() && (
        <FilterContainer>
          <FilterText variant="body2">
            Filters applied
          </FilterText>
          <ResetButton
            variant="text"
            size="small"
            onClick={handleClearAllFilters}
          >
            Reset Filters
          </ResetButton>
        </FilterContainer>
      )}

      <FilterChips
        filters={extractActiveFilters()}
        displayNames={filterDisplayNames}
        onClearFilter={handleClearFilter}
        onClearAll={handleClearAllFilters}
      />

      <DynamicTable
        headers={columns}
        data={data?.results || []}
        loading={isLoading}
        error={error}
        count={data?.count || 0}
        page={page}
        rowsPerPage={rowsPerPage}
        onPageChange={handlePageChange}
        onRowsPerPageChange={handleRowsPerPageChange}
        onSearch={handleSearch}
        searchPlaceholder="Search products..."
        onRowClick={handleViewProduct}
        actions={{
          onEdit: handleEditProduct,
        }}
      />

      <FilterDrawer
        open={filterDrawerOpen}
        onClose={() => setFilterDrawerOpen(false)}
        fields={filterFields}
        onFieldChange={(id, value) => handleFilterInputChange(id as keyof ProductFilter, value)}
        onApply={handleApplyFilters}
        onReset={handleResetFilters}
        title="Filter Products"
      />

      <CreateProductModal
        open={createModalOpen}
        onClose={() => setCreateModalOpen(false)}
        onSuccess={handleCreateProductSuccess}
      />


    </Container>
  );
};

export default ProductTablePage;
