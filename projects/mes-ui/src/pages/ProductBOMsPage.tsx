import React, { useEffect, useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  CircularProgress,
  Container,
  Breadcrumbs,
  Link,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  ButtonGroup,
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import AddChildIcon from '@mui/icons-material/PlaylistAdd';
import { useParams, Link as RouterLink } from 'react-router-dom';
import CreateBOMModal from '../components/CreateBOMModal';
import axiosInstance from '../utils/axiosInstance';
import { pageRoutes } from '../config/route';
import { useSnackbar } from '../context/SnackBarContext';

// Transform the header API response to match ProductBOMsModal expected format
const transformHeaderDataToBOMFormat = (headerData: any) => {
  if (!headerData) return null;

  // Transform top_level_items to the structure expected by ProductBOMsModal
  const transformItems = (items: any[]): any[] => {
    return items.map(item => ({
      id: item.id,
      component_id: item.component,
      component: {
        id: item.component_detail.id,
        code: item.component_detail.code,
        name: item.component_detail.name,
        description: item.component_detail.description,
      },
      quantity: item.quantity,
      position: item.position,
      item_type: item.item_type,
      notes: item.notes,
      children: item.children ? transformItems(item.children) : [],
    }));
  };

  return {
    product: headerData.product_detail,
    bom_count: 1,
    boms: [{
      id: headerData.id,
      name: headerData.name,
      code: headerData.code,
      version: headerData.version,
      status: headerData.status,
      effective_date: headerData.effective_date,
      created_by: headerData.created_by_username,
      created_at: headerData.created_at,
      updated_at: headerData.updated_at,
      structure: transformItems(headerData.top_level_items || []),
    }],
  };
};

// Component to display BOM structure with the same UI as ProductBOMsModal
const BOMStructureDisplay: React.FC<{ bomData: any }> = ({ bomData }) => {
  const [expandedItems, setExpandedItems] = useState<Set<number>>(new Set());

  if (!bomData || !bomData.top_level_items) {
    return (
      <Box sx={{ textAlign: 'center', py: 4 }}>
        <Typography color="text.secondary">
          No BOM structure available
        </Typography>
      </Box>
    );
  }

  const toggleExpanded = (itemId: number) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedItems(newExpanded);
  };

  const renderBOMItem = (item: any, level: number = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.has(item.id);

    return (
      <React.Fragment key={item.id}>
        <TableRow
          sx={{
            backgroundColor: level === 0 ? '#f8f9fa' : level === 1 ? '#ffffff' : '#f5f5f5',
            '&:hover': { backgroundColor: '#e3f2fd' },
          }}
        >
          <TableCell sx={{ pl: level * 3 + 2, py: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {hasChildren && (
                <IconButton
                  size="small"
                  onClick={() => toggleExpanded(item.id)}
                  sx={{ p: 0.5 }}
                >
                  <ArrowForwardIosIcon
                    sx={{
                      fontSize: 12,
                      transform: isExpanded ? 'rotate(90deg)' : 'rotate(0deg)',
                      transition: 'transform 0.2s',
                    }}
                  />
                </IconButton>
              )}
              <Typography variant="body2" sx={{ fontWeight: level === 0 ? 600 : 400 }}>
                {item.component_detail.code}
              </Typography>
            </Box>
          </TableCell>
          <TableCell sx={{ py: 1 }}>
            <Typography variant="body2">{item.component_detail.name}</Typography>
          </TableCell>
          <TableCell sx={{ py: 1, textAlign: 'center' }}>
            <Typography variant="body2">{item.quantity}</Typography>
          </TableCell>
          <TableCell sx={{ py: 1, textAlign: 'center' }}>
            <Typography variant="body2">{item.position}</Typography>
          </TableCell>
          <TableCell sx={{ py: 1, textAlign: 'center' }}>
            <Typography variant="body2" sx={{ textTransform: 'capitalize' }}>
              {item.item_type}
            </Typography>
          </TableCell>
          <TableCell sx={{ py: 1, textAlign: 'center' }}>
            <ButtonGroup size="small" variant="outlined">
              <IconButton size="small" title="Edit">
                <EditIcon fontSize="small" />
              </IconButton>
              <IconButton size="small" title="Add Child" disabled={item.item_type === 'component'}>
                <AddChildIcon fontSize="small" />
              </IconButton>
              <IconButton size="small" title="Delete" color="error">
                <DeleteIcon fontSize="small" />
              </IconButton>
            </ButtonGroup>
          </TableCell>
        </TableRow>
        {hasChildren && isExpanded && item.children.map((child: any) => renderBOMItem(child, level + 1))}
      </React.Fragment>
    );
  };

  return (
    <TableContainer component={Paper} sx={{ mt: 2 }}>
      <Table size="small">
        <TableHead>
          <TableRow sx={{ backgroundColor: '#1976d2' }}>
            <TableCell sx={{ color: 'white', fontWeight: 600, py: 2 }}>Component Code</TableCell>
            <TableCell sx={{ color: 'white', fontWeight: 600, py: 2 }}>Component Name</TableCell>
            <TableCell sx={{ color: 'white', fontWeight: 600, py: 2, textAlign: 'center' }}>Quantity</TableCell>
            <TableCell sx={{ color: 'white', fontWeight: 600, py: 2, textAlign: 'center' }}>Position</TableCell>
            <TableCell sx={{ color: 'white', fontWeight: 600, py: 2, textAlign: 'center' }}>Type</TableCell>
            <TableCell sx={{ color: 'white', fontWeight: 600, py: 2, textAlign: 'center' }}>Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {bomData.top_level_items.map((item: any) => renderBOMItem(item, 0))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

const ProductBOMsPage: React.FC = () => {
  // Note: productCode parameter is actually used as BOM header ID in this context
  const { productCode } = useParams<{ productCode: string }>();
  const { showSnackbar } = useSnackbar();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [bomData, setBomData] = useState<any>(null);
  const [createModalOpen, setCreateModalOpen] = useState(false);

  useEffect(() => {
    const fetchBOMHeader = async () => {
      if (!productCode) return;

      setLoading(true);
      setError(null);
      try {
        // productCode parameter contains the BOM header ID
        const headerId = productCode;
        const response = await axiosInstance.get(`/bom/api/headers/${headerId}`);
        setBomData(response.data);
      } catch (err) {
        setError('Failed to fetch BOM details');
        console.error('Error fetching BOM:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchBOMHeader();
  }, [productCode]);

  // Handle create BOM success
  const handleCreateBOMSuccess = () => {
    showSnackbar('BOM created successfully!', 'success');
    // The ProductBOMsModal will automatically refresh its data
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error || !bomData) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography color="error">{error || 'BOM not found'}</Typography>
      </Box>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 3 }}>
        <Breadcrumbs aria-label="breadcrumb">
          <Link component={RouterLink} to={pageRoutes.GO_TO_BOM_TABLE} color="inherit">
            BOMs
          </Link>
          <Typography color="text.primary">{bomData.product_detail?.name || bomData.name}</Typography>
        </Breadcrumbs>
      </Box>

      <Paper
        elevation={0}
        sx={{
          p: 3,
          borderRadius: '8px',
          border: '1px solid #e2e8f0',
          backgroundColor: '#ffffff',
        }}
      >
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Box>
            <Typography variant="h5" sx={{ fontWeight: 600, mb: 1 }}>
              {bomData.name}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              BOM Code: {bomData.code} | Version: {bomData.version}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Product: {bomData.product_detail?.name} ({bomData.product_detail?.code})
            </Typography>
          </Box>

          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setCreateModalOpen(true)}
            sx={{
              bgcolor: 'rgba(0, 0, 0, 0.8)',
              color: 'white',
              '&:hover': {
                bgcolor: 'rgba(0, 0, 0, 0.7)',
              },
              textTransform: 'none',
              fontWeight: 500,
            }}
          >
            Add BOM
          </Button>
        </Box>

        {/* Render BOM structure using the same UI as ProductBOMsModal */}
        <BOMStructureDisplay bomData={bomData} />
      </Paper>

      {/* Create BOM Modal */}
      <CreateBOMModal
        open={createModalOpen}
        onClose={() => setCreateModalOpen(false)}
        onSuccess={handleCreateBOMSuccess}
      />
    </Container>
  );
};

export default ProductBOMsPage; 