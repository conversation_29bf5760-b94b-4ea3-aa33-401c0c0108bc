import React, { useEffect, useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  CircularProgress,
  Container,
  Breadcrumbs,
  Link,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  ButtonGroup,
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import AddChildIcon from '@mui/icons-material/PlaylistAdd';
import { useParams, Link as RouterLink } from 'react-router-dom';
import CreateBOMItemModal from '../components/CreateBOMItemModal';
import EditBOMItemModal from '../components/EditBOMItemModal';
import axiosInstance from '../utils/axiosInstance';
import { pageRoutes } from '../config/route';
import { useSnackbar } from '../context/SnackBarContext';



// Component to display BOM structure with all items always visible
const BOMStructureDisplay: React.FC<{
  bomData: any;
  onEditItem: (item: any) => void;
  onDeleteItem: (itemId: number) => void;
  onAddChild: (parentItem: any) => void;
}> = ({ bomData, onEditItem, onDeleteItem, onAddChild }) => {
  if (!bomData || !bomData.top_level_items) {
    return (
      <Box sx={{ textAlign: 'center', py: 4 }}>
        <Typography color="text.secondary">
          No BOM structure available
        </Typography>
      </Box>
    );
  }

  const renderBOMItem = (item: any, level: number = 0) => {
    const hasChildren = item.children && item.children.length > 0;

    return (
      <React.Fragment key={item.id}>
        <TableRow
          sx={{
            backgroundColor: level === 0 ? '#f8f9fa' : level === 1 ? '#ffffff' : '#f5f5f5',
            '&:hover': { backgroundColor: '#e3f2fd' },
          }}
        >
          <TableCell sx={{ pl: level * 3 + 2, py: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography variant="body2" sx={{ fontWeight: level === 0 ? 600 : 400 }}>
                {item.component_detail.code}
              </Typography>
            </Box>
          </TableCell>
          <TableCell sx={{ py: 1 }}>
            <Typography variant="body2">{item.component_detail.name}</Typography>
          </TableCell>
          <TableCell sx={{ py: 1, textAlign: 'center' }}>
            <Typography variant="body2">{item.quantity}</Typography>
          </TableCell>
          <TableCell sx={{ py: 1, textAlign: 'center' }}>
            <Typography variant="body2">{item.position}</Typography>
          </TableCell>
          <TableCell sx={{ py: 1, textAlign: 'center' }}>
            <Typography variant="body2" sx={{ textTransform: 'capitalize' }}>
              {item.item_type}
            </Typography>
          </TableCell>
          <TableCell sx={{ py: 1, textAlign: 'center' }}>
            <ButtonGroup size="small" variant="outlined">
              <IconButton
                size="small"
                title="Edit"
                onClick={() => onEditItem(item)}
              >
                <EditIcon fontSize="small" />
              </IconButton>
              <IconButton
                size="small"
                title="Add Child"
                disabled={item.item_type === 'component'}
                onClick={() => onAddChild(item)}
              >
                <AddChildIcon fontSize="small" />
              </IconButton>
              <IconButton
                size="small"
                title="Delete"
                color="error"
                onClick={() => onDeleteItem(item.id)}
              >
                <DeleteIcon fontSize="small" />
              </IconButton>
            </ButtonGroup>
          </TableCell>
        </TableRow>
        {/* Show notes row if notes exist */}
        {item.notes && (
          <TableRow>
            <TableCell colSpan={6} sx={{ pl: level * 3 + 4, py: 0.5, fontStyle: 'italic', color: 'text.secondary', fontSize: '0.875rem' }}>
              Notes: {item.notes}
            </TableCell>
          </TableRow>
        )}
        {/* Always show children - no expand/collapse */}
        {hasChildren && item.children.map((child: any) => renderBOMItem(child, level + 1))}
      </React.Fragment>
    );
  };

  return (
    <TableContainer component={Paper} sx={{ mt: 2 }}>
      <Table size="small">
        <TableHead>
          <TableRow sx={{ backgroundColor: '#1976d2' }}>
            <TableCell sx={{ color: 'white', fontWeight: 600, py: 2 }}>Component Code</TableCell>
            <TableCell sx={{ color: 'white', fontWeight: 600, py: 2 }}>Component Name</TableCell>
            <TableCell sx={{ color: 'white', fontWeight: 600, py: 2, textAlign: 'center' }}>Quantity</TableCell>
            <TableCell sx={{ color: 'white', fontWeight: 600, py: 2, textAlign: 'center' }}>Position</TableCell>
            <TableCell sx={{ color: 'white', fontWeight: 600, py: 2, textAlign: 'center' }}>Type</TableCell>
            <TableCell sx={{ color: 'white', fontWeight: 600, py: 2, textAlign: 'center' }}>Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {bomData.top_level_items.map((item: any) => renderBOMItem(item, 0))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

const ProductBOMsPage: React.FC = () => {
  // Note: productCode parameter is actually used as BOM header ID in this context
  const { productCode } = useParams<{ productCode: string }>();
  const { showSnackbar } = useSnackbar();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [bomData, setBomData] = useState<any>(null);

  // Modal states
  const [createItemModalOpen, setCreateItemModalOpen] = useState(false);
  const [editItemModalOpen, setEditItemModalOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const [selectedParentItem, setSelectedParentItem] = useState<any>(null);

  useEffect(() => {
    const fetchBOMHeader = async () => {
      if (!productCode) return;

      setLoading(true);
      setError(null);
      try {
        // productCode parameter contains the BOM header ID
        const headerId = productCode;
        const response = await axiosInstance.get(`/bom/api/headers/${headerId}`);
        setBomData(response.data);
      } catch (err) {
        setError('Failed to fetch BOM details');
        console.error('Error fetching BOM:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchBOMHeader();
  }, [productCode]);

  // Get all available parent items (assemblies only)
  const getAvailableParentItems = () => {
    if (!bomData?.top_level_items) return [];

    const collectAssemblyItems = (items: any[]): any[] => {
      const assemblies: any[] = [];
      items.forEach(item => {
        if (item.item_type === 'assembly') {
          assemblies.push(item);
        }
        if (item.children && item.children.length > 0) {
          assemblies.push(...collectAssemblyItems(item.children));
        }
      });
      return assemblies;
    };

    return collectAssemblyItems(bomData.top_level_items);
  };

  // Handle edit item
  const handleEditItem = (item: any) => {
    setSelectedItem(item);
    setEditItemModalOpen(true);
  };

  // Handle delete item
  const handleDeleteItem = async (itemId: number) => {
    if (!window.confirm('Are you sure you want to delete this BOM item?')) {
      return;
    }

    try {
      await axiosInstance.delete(`/bom/api/items/${itemId}/`);
      showSnackbar('BOM item deleted successfully', 'success');
      // Refresh BOM data
      const response = await axiosInstance.get(`/bom/api/headers/${productCode}`);
      setBomData(response.data);
    } catch (error: any) {
      console.error('Error deleting BOM item:', error);
      showSnackbar(
        error.response?.data?.message || 'Failed to delete BOM item',
        'error'
      );
    }
  };

  // Handle add child
  const handleAddChild = (parentItem: any) => {
    setSelectedParentItem(parentItem);
    setCreateItemModalOpen(true);
  };

  // Handle add top-level item
  const handleAddTopLevelItem = () => {
    setSelectedParentItem(null);
    setCreateItemModalOpen(true);
  };

  // Handle create/edit success
  const handleItemSuccess = async () => {
    // Refresh BOM data
    try {
      const response = await axiosInstance.get(`/bom/api/headers/${productCode}`);
      setBomData(response.data);
    } catch (error) {
      console.error('Error refreshing BOM data:', error);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error || !bomData) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography color="error">{error || 'BOM not found'}</Typography>
      </Box>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 3 }}>
        <Breadcrumbs aria-label="breadcrumb">
          <Link component={RouterLink} to={pageRoutes.GO_TO_BOM_TABLE} color="inherit">
            BOMs
          </Link>
          <Typography color="text.primary">{bomData.product_detail?.name || bomData.name}</Typography>
        </Breadcrumbs>
      </Box>

      <Paper
        elevation={0}
        sx={{
          p: 3,
          borderRadius: '8px',
          border: '1px solid #e2e8f0',
          backgroundColor: '#ffffff',
        }}
      >
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Box>
            <Typography variant="h5" sx={{ fontWeight: 600, mb: 1 }}>
              {bomData.name}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              BOM Code: {bomData.code} | Version: {bomData.version}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Product: {bomData.product_detail?.name} ({bomData.product_detail?.code})
            </Typography>
          </Box>

          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAddTopLevelItem}
            sx={{
              bgcolor: 'rgba(0, 0, 0, 0.8)',
              color: 'white',
              '&:hover': {
                bgcolor: 'rgba(0, 0, 0, 0.7)',
              },
              textTransform: 'none',
              fontWeight: 500,
            }}
          >
            Add BOM Item
          </Button>
        </Box>

        {/* Render BOM structure using the same UI as ProductBOMsModal */}
        {bomData && (
          <Box>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              BOM Components ({bomData.top_level_items?.length || 0} top-level items)
            </Typography>
            <BOMStructureDisplay
              bomData={bomData}
              onEditItem={handleEditItem}
              onDeleteItem={handleDeleteItem}
              onAddChild={handleAddChild}
            />
          </Box>
        )}
      </Paper>

      {/* Create BOM Item Modal */}
      <CreateBOMItemModal
        open={createItemModalOpen}
        onClose={() => {
          setCreateItemModalOpen(false);
          setSelectedParentItem(null);
        }}
        onSuccess={handleItemSuccess}
        bomHeaderId={parseInt(productCode || '0')}
        parentItemId={selectedParentItem?.id || null}
        availableParentItems={getAvailableParentItems()}
      />

      {/* Edit BOM Item Modal */}
      <EditBOMItemModal
        open={editItemModalOpen}
        onClose={() => {
          setEditItemModalOpen(false);
          setSelectedItem(null);
        }}
        onSuccess={handleItemSuccess}
        bomItem={selectedItem}
        availableParentItems={getAvailableParentItems()}
      />
    </Container>
  );
};

export default ProductBOMsPage; 