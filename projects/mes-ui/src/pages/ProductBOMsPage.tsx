import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  CircularProgress,
  Container,
  Breadcrumbs,
  Link,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import AddChildIcon from '@mui/icons-material/PlaylistAdd';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import { useParams, Link as RouterLink } from 'react-router-dom';
import CreateBOMItemModal from '../components/CreateBOMItemModal';
import EditBOMItemModal from '../components/EditBOMItemModal';
import DeleteBOMItemModal from '../components/DeleteBOMItemModal';
import { pageRoutes } from '../config/route';
import { useBOMDetails } from '../hooks/useBOMTable';



// Component to display BOM structure with accordion-style nested children
const BOMStructureDisplay: React.FC<{
  bomData: any;
  onEditItem: (item: any) => void;
  onDeleteItem: (item: any) => void;
  onAddChild: (parentItem: any) => void;
}> = ({ bomData, onEditItem, onDeleteItem, onAddChild }) => {
  const [expandedItems, setExpandedItems] = useState<Set<number>>(new Set());

  // Only show "no items" message if the array exists but is empty
  if (!bomData || !bomData.top_level_items || bomData.top_level_items.length === 0) {
    return (
      <Box sx={{ textAlign: 'center', py: 8 }}>
        <Typography variant="h6" color="text.secondary" sx={{ mb: 1 }}>
          No BOM items found
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Click "ADD ITEM" to create your first BOM item
        </Typography>
      </Box>
    );
  }

  const toggleExpanded = (itemId: number) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedItems(newExpanded);
  };

  const renderBOMItem = (item: any, level: number = 0, parentItemId: number | null = null): React.ReactNode => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.has(item.id);

    // Ensure item has parent_item information
    const itemWithParent = { ...item, parent_item: parentItemId };

    return (
      <React.Fragment key={item.id}>
        <TableRow
          sx={{
            '&:hover': {
              backgroundColor: level > 0 ? '#f1f5f9' : '#f8fafc',
              transition: 'background-color 0.15s ease'
            },
            borderBottom: '1px solid #f1f5f9',
            backgroundColor: level > 0 ? '#fafbfc' : 'white',
          }}
        >
          <TableCell sx={{ py: 3, px: 3, borderBottom: '1px solid #f1f5f9' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, pl: level * 3 }}>
              {hasChildren && (
                <IconButton
                  size="small"
                  onClick={() => toggleExpanded(item.id)}
                  sx={{
                    p: 0.5,
                    mr: 0.5,
                    color: '#6b7280',
                    '&:hover': {
                      backgroundColor: 'rgba(107, 114, 128, 0.1)',
                      color: '#374151'
                    }
                  }}
                >
                  {isExpanded ? (
                    <KeyboardArrowDownIcon sx={{ fontSize: 18 }} />
                  ) : (
                    <KeyboardArrowRightIcon sx={{ fontSize: 18 }} />
                  )}
                </IconButton>
              )}
              {!hasChildren && level > 0 && (
                <Box sx={{ width: 32, height: 32, mr: 0.5 }} />
              )}
              <Typography variant="body2" sx={{
                fontWeight: level === 0 ? 600 : 500,
                color: '#111827',
                fontSize: '0.9rem'
              }}>
                {item.component_detail.name}
              </Typography>
            </Box>
          </TableCell>
          <TableCell sx={{ py: 3, px: 3, borderBottom: '1px solid #f1f5f9' }}>
            <Typography variant="body2" sx={{
              color: '#6b7280',
              fontSize: '0.85rem',
              fontFamily: 'monospace'
            }}>
              {item.component_detail.code}
            </Typography>
          </TableCell>
          <TableCell sx={{ py: 3, px: 3, textAlign: 'center', borderBottom: '1px solid #f1f5f9' }}>
            <Typography variant="body2" sx={{
              color: '#111827',
              fontSize: '0.9rem',
              fontWeight: 500
            }}>
              {item.quantity}
            </Typography>
          </TableCell>
          <TableCell sx={{ py: 3, px: 3, textAlign: 'center', borderBottom: '1px solid #f1f5f9' }}>
            <Box sx={{
              display: 'inline-block',
              px: 2,
              py: 0.5,
              borderRadius: '12px',
              backgroundColor: item.item_type === 'assembly' ? '#dbeafe' : '#f3f4f6',
              color: item.item_type === 'assembly' ? '#1e40af' : '#374151',
              fontSize: '0.75rem',
              fontWeight: 500,
              textTransform: 'capitalize'
            }}>
              {item.item_type}
            </Box>
          </TableCell>
          <TableCell sx={{ py: 3, px: 3, borderBottom: '1px solid #f1f5f9' }}>
            <Typography variant="body2" sx={{
              color: '#6b7280',
              fontSize: '0.85rem',
              fontStyle: item.notes ? 'normal' : 'italic'
            }}>
              {item.notes || 'No notes'}
            </Typography>
          </TableCell>
          <TableCell sx={{ py: 3, px: 3, textAlign: 'center', borderBottom: '1px solid #f1f5f9' }}>
            <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center' }}>
              <IconButton
                size="small"
                title="Edit"
                onClick={() => onEditItem(itemWithParent)}
                sx={{
                  color: '#6b7280',
                  p: 0.75,
                  borderRadius: '6px',
                  '&:hover': {
                    backgroundColor: '#f3f4f6',
                    color: '#374151'
                  }
                }}
              >
                <EditIcon fontSize="small" />
              </IconButton>
              <IconButton
                size="small"
                title="Add Child"
                onClick={() => onAddChild(item)}
                sx={{
                  color: '#6b7280',
                  p: 0.75,
                  borderRadius: '6px',
                  '&:hover': {
                    backgroundColor: '#f3f4f6',
                    color: '#374151'
                  }
                }}
              >
                <AddChildIcon fontSize="small" />
              </IconButton>
              <IconButton
                size="small"
                title="Delete"
                onClick={() => onDeleteItem(itemWithParent)}
                sx={{
                  color: '#ef4444',
                  p: 0.75,
                  borderRadius: '6px',
                  '&:hover': {
                    backgroundColor: '#fef2f2',
                    color: '#dc2626'
                  }
                }}
              >
                <DeleteIcon fontSize="small" />
              </IconButton>
            </Box>
          </TableCell>
        </TableRow>
        {/* Render children when expanded */}
        {hasChildren && isExpanded && (
          <>
            {item.children.map((child: any) =>
              renderBOMItem(child, level + 1, item.id)
            )}
          </>
        )}
      </React.Fragment>
    );
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* BOM Items Table */}
      <TableContainer sx={{
        boxShadow: 'none',
        border: 'none',
        borderRadius: '8px',
        overflow: 'hidden'
      }}>
        <Table size="medium">
          <TableHead>
            <TableRow sx={{
              backgroundColor: '#f8fafc',
              borderBottom: '1px solid #e2e8f0'
            }}>
              <TableCell sx={{
                fontWeight: 700,
                py: 3,
                px: 3,
                fontSize: '0.8rem',
                textTransform: 'uppercase',
                color: '#374151',
                borderBottom: 'none',
                letterSpacing: '0.05em'
              }}>
                Component Name
              </TableCell>
              <TableCell sx={{
                fontWeight: 700,
                py: 3,
                px: 3,
                fontSize: '0.8rem',
                textTransform: 'uppercase',
                color: '#374151',
                borderBottom: 'none',
                letterSpacing: '0.05em'
              }}>
                Code
              </TableCell>
              <TableCell sx={{
                fontWeight: 700,
                py: 3,
                px: 3,
                fontSize: '0.8rem',
                textTransform: 'uppercase',
                textAlign: 'center',
                color: '#374151',
                borderBottom: 'none',
                letterSpacing: '0.05em'
              }}>
                Quantity
              </TableCell>
              <TableCell sx={{
                fontWeight: 700,
                py: 3,
                px: 3,
                fontSize: '0.8rem',
                textTransform: 'uppercase',
                textAlign: 'center',
                color: '#374151',
                borderBottom: 'none',
                letterSpacing: '0.05em'
              }}>
                Type
              </TableCell>
              <TableCell sx={{
                fontWeight: 700,
                py: 3,
                px: 3,
                fontSize: '0.8rem',
                textTransform: 'uppercase',
                color: '#374151',
                borderBottom: 'none',
                letterSpacing: '0.05em'
              }}>
                Notes
              </TableCell>
              <TableCell sx={{
                fontWeight: 700,
                py: 3,
                px: 3,
                fontSize: '0.8rem',
                textTransform: 'uppercase',
                textAlign: 'center',
                color: '#374151',
                borderBottom: 'none',
                letterSpacing: '0.05em'
              }}>
                Actions
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {bomData.top_level_items.map((item: any) => renderBOMItem(item, 0, null))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

const ProductBOMsPage: React.FC = () => {
  // Note: productCode parameter is actually used as BOM header ID in this context
  const { productCode } = useParams<{ productCode: string }>();

  // TanStack Query hooks
  const { data: bomData, isLoading: loading, error } = useBOMDetails(productCode || '');

  // Modal states
  const [createItemModalOpen, setCreateItemModalOpen] = useState(false);
  const [editItemModalOpen, setEditItemModalOpen] = useState(false);
  const [deleteItemModalOpen, setDeleteItemModalOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const [selectedParentItem, setSelectedParentItem] = useState<any>(null);
  const [itemToDelete, setItemToDelete] = useState<any>(null);

  // Get all available parent items (assemblies only)
  const getAvailableParentItems = () => {
    if (!bomData?.top_level_items) return [];

    const collectAssemblyItems = (items: any[], parentId: number | null = null): any[] => {
      const assemblies: any[] = [];
      items.forEach(item => {
        // Add parent_item information to each item
        const itemWithParent = { ...item, parent_item: parentId };

        if (item.item_type === 'assembly') {
          assemblies.push(itemWithParent);
        }
        if (item.children && item.children.length > 0) {
          assemblies.push(...collectAssemblyItems(item.children, item.id));
        }
      });
      return assemblies;
    };

    return collectAssemblyItems(bomData.top_level_items);
  };

  // Handle edit item
  const handleEditItem = (item: any) => {
    setSelectedItem(item);
    setEditItemModalOpen(true);
  };

  // Handle delete item - open confirmation modal
  const handleDeleteItem = (item: any) => {
    setItemToDelete(item);
    setDeleteItemModalOpen(true);
  };

  // Handle add child
  const handleAddChild = (parentItem: any) => {
    setSelectedParentItem(parentItem);
    setCreateItemModalOpen(true);
  };

  // Handle add top-level item
  const handleAddTopLevelItem = () => {
    setSelectedParentItem(null);
    setCreateItemModalOpen(true);
  };

  // Handle create/edit success - TanStack Query will auto-refresh
  const handleItemSuccess = () => {
    // No manual refresh needed - TanStack Query handles this automatically
  };

  // Handle delete success
  const handleDeleteSuccess = () => {
    setItemToDelete(null);
    setDeleteItemModalOpen(false);
    // TanStack Query will automatically refetch the data
  };

  if (loading) {
    return (
      <Container maxWidth="xl" sx={{ py: 3 }}>
        <Box sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          height: '60vh',
          gap: 2
        }}>
          <CircularProgress size={40} sx={{ color: '#667eea' }} />
          <Typography variant="body1" color="text.secondary">
            Loading BOM details...
          </Typography>
        </Box>
      </Container>
    );
  }

  if (error || !bomData) {
    return (
      <Container maxWidth="xl" sx={{ py: 3 }}>
        <Box sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          height: '60vh',
          gap: 2
        }}>
          <Typography variant="h6" color="error" sx={{ mb: 1 }}>
            Error Loading BOM
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center' }}>
            {error instanceof Error ? error.message : error || 'BOM not found'}
          </Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* Breadcrumbs */}
      <Box sx={{ mb: 4 }}>
        <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 1 }}>
          <Link
            component={RouterLink}
            to={pageRoutes.GO_TO_BOM_TABLE}
            color="inherit"
            sx={{
              textDecoration: 'none',
              '&:hover': { textDecoration: 'underline' }
            }}
          >
            BOMs
          </Link>
          <Typography color="text.primary" sx={{ fontWeight: 500 }}>
            {bomData.product_detail?.name || bomData.name}
          </Typography>
        </Breadcrumbs>
      </Box>

      {/* Main Content Card */}
      <Paper
        elevation={0}
        sx={{
          borderRadius: '12px',
          border: '1px solid #e5e7eb',
          backgroundColor: '#ffffff',
          overflow: 'hidden',
          boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
        }}
      >
        {/* Header Section */}
        <Box sx={{
          p: 4,
          borderBottom: '1px solid #f3f4f6',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          position: 'relative'
        }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
            <Box>
              <Typography variant="h4" sx={{ fontWeight: 700, mb: 2, color: 'white' }}>
                {bomData.name}
              </Typography>
              <Box sx={{ display: 'flex', gap: 3, mb: 1 }}>
                <Typography variant="body1" sx={{ color: 'rgba(255,255,255,0.9)' }}>
                  <strong>Code:</strong> {bomData.code}
                </Typography>
                <Typography variant="body1" sx={{ color: 'rgba(255,255,255,0.9)' }}>
                  <strong>Version:</strong> {bomData.version}
                </Typography>
              </Box>
              <Typography variant="body1" sx={{ color: 'rgba(255,255,255,0.9)' }}>
                <strong>Product:</strong> {bomData.product_detail?.name} ({bomData.product_detail?.code})
              </Typography>
            </Box>

            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleAddTopLevelItem}
              sx={{
                bgcolor: 'rgba(255,255,255,0.2)',
                color: 'white',
                border: '1px solid rgba(255,255,255,0.3)',
                backdropFilter: 'blur(10px)',
                '&:hover': {
                  bgcolor: 'rgba(255,255,255,0.3)',
                  border: '1px solid rgba(255,255,255,0.5)',
                },
                textTransform: 'none',
                fontWeight: 600,
                fontSize: '0.95rem',
                px: 3,
                py: 1.5,
                borderRadius: '8px',
              }}
            >
              Add Item
            </Button>
          </Box>
        </Box>

        {/* BOM Structure Display */}
        {bomData && (
          <BOMStructureDisplay
            bomData={bomData}
            onEditItem={handleEditItem}
            onDeleteItem={handleDeleteItem}
            onAddChild={handleAddChild}
          />
        )}
      </Paper>

      {/* Create BOM Item Modal */}
      <CreateBOMItemModal
        open={createItemModalOpen}
        onClose={() => {
          setCreateItemModalOpen(false);
          setSelectedParentItem(null);
        }}
        onSuccess={handleItemSuccess}
        bomHeaderId={parseInt(productCode || '0')}
        parentItemId={selectedParentItem?.id || null}
        availableParentItems={getAvailableParentItems()}
      />

      {/* Edit BOM Item Modal */}
      <EditBOMItemModal
        open={editItemModalOpen}
        onClose={() => {
          setEditItemModalOpen(false);
          setSelectedItem(null);
        }}
        onSuccess={handleItemSuccess}
        bomItem={selectedItem}
      />

      {/* Delete BOM Item Modal */}
      <DeleteBOMItemModal
        open={deleteItemModalOpen}
        onClose={() => {
          setDeleteItemModalOpen(false);
          setItemToDelete(null);
        }}
        onSuccess={handleDeleteSuccess}
        bomItem={itemToDelete}
      />
    </Container>
  );
};

export default ProductBOMsPage; 