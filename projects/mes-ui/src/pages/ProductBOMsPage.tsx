import React, { useEffect, useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  CircularProgress,
  Container,
  Breadcrumbs,
  Link,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import AddChildIcon from '@mui/icons-material/PlaylistAdd';
import { useParams, Link as RouterLink } from 'react-router-dom';
import CreateBOMItemModal from '../components/CreateBOMItemModal';
import EditBOMItemModal from '../components/EditBOMItemModal';
import axiosInstance from '../utils/axiosInstance';
import { pageRoutes } from '../config/route';
import { useSnackbar } from '../context/SnackBarContext';



// Component to display BOM structure as flat list like the reference image
const BOMStructureDisplay: React.FC<{
  bomData: any;
  onEditItem: (item: any) => void;
  onDeleteItem: (itemId: number) => void;
  onAddChild: (parentItem: any) => void;
}> = ({ bomData, onEditItem, onDeleteItem, onAddChild }) => {
  if (!bomData || !bomData.top_level_items) {
    return (
      <Box sx={{ textAlign: 'center', py: 4 }}>
        <Typography color="text.secondary">
          No BOM structure available
        </Typography>
      </Box>
    );
  }

  // Flatten all items into a single list
  const flattenItems = (items: any[], parentName?: string): any[] => {
    const flattened: any[] = [];
    items.forEach(item => {
      flattened.push({
        ...item,
        parentName: parentName || null,
        isChild: !!parentName
      });
      if (item.children && item.children.length > 0) {
        flattened.push(...flattenItems(item.children, item.component_detail.name));
      }
    });
    return flattened;
  };

  const allItems = flattenItems(bomData.top_level_items);

  return (
    <Box sx={{ mt: 0 }}>
      {/* BOM Items Table */}
      <TableContainer component={Paper} sx={{ boxShadow: 'none', border: '1px solid #e0e0e0' }}>
        <Table size="small">
          <TableHead>
            <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
              <TableCell sx={{ fontWeight: 600, py: 2, fontSize: '0.875rem', textTransform: 'uppercase' }}>
                COMPONENT NAME
              </TableCell>
              <TableCell sx={{ fontWeight: 600, py: 2, fontSize: '0.875rem', textTransform: 'uppercase' }}>
                CODE
              </TableCell>
              <TableCell sx={{ fontWeight: 600, py: 2, fontSize: '0.875rem', textTransform: 'uppercase', textAlign: 'center' }}>
                QUANTITY
              </TableCell>
              <TableCell sx={{ fontWeight: 600, py: 2, fontSize: '0.875rem', textTransform: 'uppercase', textAlign: 'center' }}>
                TYPE
              </TableCell>
              <TableCell sx={{ fontWeight: 600, py: 2, fontSize: '0.875rem', textTransform: 'uppercase' }}>
                NOTES
              </TableCell>
              <TableCell sx={{ fontWeight: 600, py: 2, fontSize: '0.875rem', textTransform: 'uppercase', textAlign: 'center' }}>
                ACTIONS
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {allItems.map((item, index) => (
              <TableRow
                key={`${item.id}-${index}`}
                sx={{
                  '&:hover': { backgroundColor: '#f9f9f9' },
                  borderBottom: '1px solid #e0e0e0',
                }}
              >
                <TableCell sx={{ py: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {item.isChild && (
                      <Box sx={{
                        width: 8,
                        height: 8,
                        backgroundColor: '#666',
                        borderRadius: '50%',
                        mr: 1
                      }} />
                    )}
                    <Typography variant="body2" sx={{ fontWeight: item.isChild ? 400 : 500 }}>
                      {item.component_detail.name}
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell sx={{ py: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    {item.component_detail.code}
                  </Typography>
                </TableCell>
                <TableCell sx={{ py: 2, textAlign: 'center' }}>
                  <Typography variant="body2">{item.quantity}</Typography>
                </TableCell>
                <TableCell sx={{ py: 2, textAlign: 'center' }}>
                  <Typography variant="body2" sx={{ textTransform: 'lowercase' }}>
                    {item.item_type}
                  </Typography>
                </TableCell>
                <TableCell sx={{ py: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    {item.notes || '-'}
                  </Typography>
                </TableCell>
                <TableCell sx={{ py: 2, textAlign: 'center' }}>
                  <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center' }}>
                    <IconButton
                      size="small"
                      title="Edit"
                      onClick={() => onEditItem(item)}
                      sx={{ color: '#666' }}
                    >
                      <EditIcon fontSize="small" />
                    </IconButton>
                    <IconButton
                      size="small"
                      title="Add Child"
                      disabled={item.item_type === 'component'}
                      onClick={() => onAddChild(item)}
                      sx={{ color: '#666' }}
                    >
                      <AddChildIcon fontSize="small" />
                    </IconButton>
                    <IconButton
                      size="small"
                      title="Delete"
                      onClick={() => onDeleteItem(item.id)}
                      sx={{ color: '#d32f2f' }}
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

const ProductBOMsPage: React.FC = () => {
  // Note: productCode parameter is actually used as BOM header ID in this context
  const { productCode } = useParams<{ productCode: string }>();
  const { showSnackbar } = useSnackbar();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [bomData, setBomData] = useState<any>(null);

  // Modal states
  const [createItemModalOpen, setCreateItemModalOpen] = useState(false);
  const [editItemModalOpen, setEditItemModalOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const [selectedParentItem, setSelectedParentItem] = useState<any>(null);

  useEffect(() => {
    const fetchBOMHeader = async () => {
      if (!productCode) return;

      setLoading(true);
      setError(null);
      try {
        // productCode parameter contains the BOM header ID
        const headerId = productCode;
        const response = await axiosInstance.get(`/bom/api/headers/${headerId}`);
        setBomData(response.data);
      } catch (err) {
        setError('Failed to fetch BOM details');
        console.error('Error fetching BOM:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchBOMHeader();
  }, [productCode]);

  // Get all available parent items (assemblies only)
  const getAvailableParentItems = () => {
    if (!bomData?.top_level_items) return [];

    const collectAssemblyItems = (items: any[]): any[] => {
      const assemblies: any[] = [];
      items.forEach(item => {
        if (item.item_type === 'assembly') {
          assemblies.push(item);
        }
        if (item.children && item.children.length > 0) {
          assemblies.push(...collectAssemblyItems(item.children));
        }
      });
      return assemblies;
    };

    return collectAssemblyItems(bomData.top_level_items);
  };

  // Handle edit item
  const handleEditItem = (item: any) => {
    setSelectedItem(item);
    setEditItemModalOpen(true);
  };

  // Handle delete item
  const handleDeleteItem = async (itemId: number) => {
    if (!window.confirm('Are you sure you want to delete this BOM item?')) {
      return;
    }

    try {
      await axiosInstance.delete(`/bom/api/items/${itemId}/`);
      showSnackbar('BOM item deleted successfully', 'success');
      // Refresh BOM data
      const response = await axiosInstance.get(`/bom/api/headers/${productCode}`);
      setBomData(response.data);
    } catch (error: any) {
      console.error('Error deleting BOM item:', error);
      showSnackbar(
        error.response?.data?.message || 'Failed to delete BOM item',
        'error'
      );
    }
  };

  // Handle add child
  const handleAddChild = (parentItem: any) => {
    setSelectedParentItem(parentItem);
    setCreateItemModalOpen(true);
  };

  // Handle add top-level item
  const handleAddTopLevelItem = () => {
    setSelectedParentItem(null);
    setCreateItemModalOpen(true);
  };

  // Handle create/edit success
  const handleItemSuccess = async () => {
    // Refresh BOM data
    try {
      const response = await axiosInstance.get(`/bom/api/headers/${productCode}`);
      setBomData(response.data);
    } catch (error) {
      console.error('Error refreshing BOM data:', error);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error || !bomData) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography color="error">{error || 'BOM not found'}</Typography>
      </Box>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 3 }}>
        <Breadcrumbs aria-label="breadcrumb">
          <Link component={RouterLink} to={pageRoutes.GO_TO_BOM_TABLE} color="inherit">
            BOMs
          </Link>
          <Typography color="text.primary">{bomData.product_detail?.name || bomData.name}</Typography>
        </Breadcrumbs>
      </Box>

      <Paper
        elevation={0}
        sx={{
          p: 3,
          borderRadius: '8px',
          border: '1px solid #e2e8f0',
          backgroundColor: '#ffffff',
        }}
      >
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Box>
            <Typography variant="h5" sx={{ fontWeight: 600, mb: 1 }}>
              {bomData.name}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              BOM Code: {bomData.code} | Version: {bomData.version}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Product: {bomData.product_detail?.name} ({bomData.product_detail?.code})
            </Typography>
          </Box>

          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAddTopLevelItem}
            sx={{
              bgcolor: '#000',
              color: 'white',
              '&:hover': {
                bgcolor: '#333',
              },
              textTransform: 'uppercase',
              fontWeight: 600,
              fontSize: '0.875rem',
              px: 3,
              py: 1,
            }}
          >
            ADD ITEM
          </Button>
        </Box>

        {/* BOM Structure Display */}
        {bomData && (
          <BOMStructureDisplay
            bomData={bomData}
            onEditItem={handleEditItem}
            onDeleteItem={handleDeleteItem}
            onAddChild={handleAddChild}
          />
        )}
      </Paper>

      {/* Create BOM Item Modal */}
      <CreateBOMItemModal
        open={createItemModalOpen}
        onClose={() => {
          setCreateItemModalOpen(false);
          setSelectedParentItem(null);
        }}
        onSuccess={handleItemSuccess}
        bomHeaderId={parseInt(productCode || '0')}
        parentItemId={selectedParentItem?.id || null}
        availableParentItems={getAvailableParentItems()}
      />

      {/* Edit BOM Item Modal */}
      <EditBOMItemModal
        open={editItemModalOpen}
        onClose={() => {
          setEditItemModalOpen(false);
          setSelectedItem(null);
        }}
        onSuccess={handleItemSuccess}
        bomItem={selectedItem}
        availableParentItems={getAvailableParentItems()}
      />
    </Container>
  );
};

export default ProductBOMsPage; 