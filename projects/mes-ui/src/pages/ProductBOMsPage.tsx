import React, { useEffect, useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  CircularProgress,
  Container,
  Breadc<PERSON><PERSON>,
  Link,
  Button,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  IconButton,
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import EditIcon from '@mui/icons-material/Edit';
import { useParams, Link as RouterLink } from 'react-router-dom';
import CreateBOMModal from '../components/CreateBOMModal';
import axiosInstance from '../utils/axiosInstance';
import { pageRoutes } from '../config/route';
import { useSnackbar } from '../context/SnackBarContext';

interface BOMItem {
  id: number;
  component: number;
  component_detail: {
    id: number;
    code: string;
    name: string;
    description: string;
  };
  quantity: string;
  position: string;
  item_type: string;
  notes: string;
  children: BOMItem[];
}

// Component to display individual BOM items with nested structure
const BOMItemDisplay: React.FC<{ item: BOMItem; level: number }> = ({ item, level }) => {
  const [expanded, setExpanded] = useState(false);
  const hasChildren = item.children && item.children.length > 0;

  const getItemTypeColor = (type: string) => {
    switch (type) {
      case 'assembly':
        return '#1976d2';
      case 'component':
        return '#388e3c';
      default:
        return '#757575';
    }
  };

  return (
    <Box sx={{ mb: 1 }}>
      <Accordion
        expanded={expanded}
        onChange={() => setExpanded(!expanded)}
        sx={{
          boxShadow: 'none',
          border: '1px solid #e0e0e0',
          '&:before': { display: 'none' },
          ml: level * 2,
        }}
      >
        <AccordionSummary
          expandIcon={hasChildren ? <ExpandMoreIcon /> : null}
          sx={{
            backgroundColor: level === 0 ? '#f5f5f5' : '#fafafa',
            minHeight: 48,
            '& .MuiAccordionSummary-content': {
              alignItems: 'center',
            },
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', gap: 2 }}>
            <Typography variant="body2" sx={{ fontWeight: 500, minWidth: 120 }}>
              {item.component_detail.code}
            </Typography>
            <Typography variant="body2" sx={{ flex: 1 }}>
              {item.component_detail.name}
            </Typography>
            <Chip
              label={item.item_type}
              size="small"
              sx={{
                backgroundColor: getItemTypeColor(item.item_type),
                color: 'white',
                fontSize: '0.75rem',
                minWidth: 80,
              }}
            />
            <Typography variant="body2" sx={{ minWidth: 60, textAlign: 'center' }}>
              Qty: {item.quantity}
            </Typography>
            <Typography variant="body2" sx={{ minWidth: 80, textAlign: 'center' }}>
              {item.position}
            </Typography>
            <IconButton size="small" sx={{ ml: 1 }}>
              <EditIcon fontSize="small" />
            </IconButton>
          </Box>
        </AccordionSummary>

        {hasChildren && (
          <AccordionDetails sx={{ p: 0 }}>
            {item.children.map((child) => (
              <BOMItemDisplay key={child.id} item={child} level={level + 1} />
            ))}
          </AccordionDetails>
        )}
      </Accordion>

      {item.notes && (
        <Typography variant="caption" color="text.secondary" sx={{ ml: level * 2 + 2, display: 'block', mt: 0.5 }}>
          Notes: {item.notes}
        </Typography>
      )}
    </Box>
  );
};

const ProductBOMsPage: React.FC = () => {
  // Note: productCode parameter is actually used as BOM header ID in this context
  const { productCode } = useParams<{ productCode: string }>();
  const { showSnackbar } = useSnackbar();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [bomData, setBomData] = useState<any>(null);
  const [createModalOpen, setCreateModalOpen] = useState(false);

  useEffect(() => {
    const fetchBOMHeader = async () => {
      if (!productCode) return;

      setLoading(true);
      setError(null);
      try {
        // productCode parameter contains the BOM header ID
        const headerId = productCode;
        const response = await axiosInstance.get(`/bom/api/headers/${headerId}`);
        setBomData(response.data);
      } catch (err) {
        setError('Failed to fetch BOM details');
        console.error('Error fetching BOM:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchBOMHeader();
  }, [productCode]);

  // Handle create BOM success
  const handleCreateBOMSuccess = () => {
    showSnackbar('BOM created successfully!', 'success');
    // The ProductBOMsModal will automatically refresh its data
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error || !bomData) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography color="error">{error || 'BOM not found'}</Typography>
      </Box>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 3 }}>
        <Breadcrumbs aria-label="breadcrumb">
          <Link component={RouterLink} to={pageRoutes.GO_TO_BOM_TABLE} color="inherit">
            BOMs
          </Link>
          <Typography color="text.primary">{bomData.product_detail?.name || bomData.name}</Typography>
        </Breadcrumbs>
      </Box>

      <Paper
        elevation={0}
        sx={{
          p: 3,
          borderRadius: '8px',
          border: '1px solid #e2e8f0',
          backgroundColor: '#ffffff',
        }}
      >
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Box>
            <Typography variant="h5" sx={{ fontWeight: 600, mb: 1 }}>
              {bomData.name}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              BOM Code: {bomData.code} | Version: {bomData.version}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Product: {bomData.product_detail?.name} ({bomData.product_detail?.code})
            </Typography>
          </Box>

          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setCreateModalOpen(true)}
            sx={{
              bgcolor: 'rgba(0, 0, 0, 0.8)',
              color: 'white',
              '&:hover': {
                bgcolor: 'rgba(0, 0, 0, 0.7)',
              },
              textTransform: 'none',
              fontWeight: 500,
            }}
          >
            Add BOM
          </Button>
        </Box>

        {/* BOM Structure Display */}
        {bomData.top_level_items && bomData.top_level_items.length > 0 ? (
          <Box>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              BOM Structure
            </Typography>
            {bomData.top_level_items.map((item: any) => (
              <BOMItemDisplay key={item.id} item={item} level={0} />
            ))}
          </Box>
        ) : (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography color="text.secondary">
              No components found in this BOM
            </Typography>
          </Box>
        )}
      </Paper>

      {/* Create BOM Modal */}
      <CreateBOMModal
        open={createModalOpen}
        onClose={() => setCreateModalOpen(false)}
        onSuccess={handleCreateBOMSuccess}
      />
    </Container>
  );
};

export default ProductBOMsPage; 