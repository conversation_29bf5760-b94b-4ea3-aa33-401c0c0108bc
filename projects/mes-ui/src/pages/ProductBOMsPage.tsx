import React, { useEffect, useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  CircularProgress,
  Container,
  Breadcrumbs,
  Link,
} from '@mui/material';
import { useParams, Link as RouterLink } from 'react-router-dom';
import ProductBOMsModal from '../components/ProductBOMsModal';
import axiosInstance from '../utils/axiosInstance';
import { pageRoutes } from '../config/route';

interface Product {
  id: number;
  code: string;
  name: string;
  description: string;
  type_id: string;
  is_active: boolean;
}

const ProductBOMsPage: React.FC = () => {
  const { productCode } = useParams<{ productCode: string }>();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [product, setProduct] = useState<Product | null>(null);

  useEffect(() => {
    const fetchProduct = async () => {
      if (!productCode) return;
      
      setLoading(true);
      setError(null);
      try {
        const response = await axiosInstance.get(`/bom/api/headers/product_boms?product_code=${productCode}`);
        setProduct(response.data.product);
      } catch (err) {
        setError('Failed to fetch product details');
        console.error('Error fetching product:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchProduct();
  }, [productCode]);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error || !product) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography color="error">{error || 'Product not found'}</Typography>
      </Box>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 3 }}>
        <Breadcrumbs aria-label="breadcrumb">
          <Link component={RouterLink} to={pageRoutes.GO_TO_BOM_TABLE} color="inherit">
            BOMs
          </Link>
          <Typography color="text.primary">{product.name}</Typography>
        </Breadcrumbs>
      </Box>

      <Paper 
        elevation={0}
        sx={{ 
          p: 3,
          borderRadius: '8px',
          border: '1px solid #e2e8f0',
          backgroundColor: '#ffffff',
        }}
      >
        <Box sx={{ mb: 3 }}>
          <Typography variant="h5" sx={{ fontWeight: 600, mb: 1 }}>
            {product.name}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Product Code: {product.code}
          </Typography>
        </Box>

        <ProductBOMsModal
          open={true}
          onClose={() => {}}
          productCode={productCode || ''}
          status="active"
        />
      </Paper>
    </Container>
  );
};

export default ProductBOMsPage; 