import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
  Box,
} from "@mui/material";
import { EventItem } from "../hooks/useEventQueue";
import styles from "../styles/EventList.module.scss";

const EventList = ({ eventQueue }: { eventQueue: EventItem[] }) => {
  if (eventQueue.length === 0) {
    return null; // Hide if no events
  }

  // Parse event data and extract all unique keys
  const allKeys = new Set<string>();

  eventQueue.forEach((event) => {
    Object.values(event.data).forEach((value) => {
      try {
        const parsed = typeof value === "string" ? JSON.parse(value) : value;
        if (typeof parsed === "object" && parsed !== null) {
          Object.keys(parsed).forEach((key) => allKeys.add(key));
        }
      } catch (e) {
        // Ignore parsing errors, keep the original value
      }
    });
  });

  const sortedKeys = Array.from(allKeys).sort();

  return (
    <Box className={styles.eventListContainer}>
      <TableContainer component={Paper} className={styles.tableContainer} sx={{
        borderRadius: '8px',
        boxShadow: 'none',
        overflow: 'hidden',
        border: '1px solid #e2e8f0',
      }}>
        <Typography variant="h6" sx={{ p: 2 }}>
          Recent Events
        </Typography>
        <Box>
          <Table size="small" sx={{ borderCollapse: 'separate', borderSpacing: 0 }}>
            <TableHead>
              <TableRow sx={{
                backgroundColor: '#f8fafc',
                '& th:first-of-type': {
                  borderTopLeftRadius: '8px'
                },
                '& th:last-child': {
                  borderTopRightRadius: '8px'
                }
              }}>
                <TableCell sx={{
                  fontWeight: 600,
                  fontSize: '0.8rem',
                  color: '#4a5568',
                  borderBottom: '1px solid #e2e8f0',
                  paddingBottom: '10px',
                  paddingTop: '10px',
                  height: '48px',
                  paddingLeft: '16px',
                  paddingRight: '16px',
                  minWidth: '60px',
                  width: '80px',
                  textAlign: 'center',
                }}>#</TableCell>
                <TableCell sx={{
                  fontWeight: 600,
                  fontSize: '0.8rem',
                  color: '#4a5568',
                  borderBottom: '1px solid #e2e8f0',
                  paddingBottom: '10px',
                  paddingTop: '10px',
                  height: '48px',
                  paddingLeft: '16px',
                  paddingRight: '16px',
                }}>Timestamp</TableCell>
                <TableCell sx={{
                  fontWeight: 600,
                  fontSize: '0.8rem',
                  color: '#4a5568',
                  borderBottom: '1px solid #e2e8f0',
                  paddingBottom: '10px',
                  paddingTop: '10px',
                  height: '48px',
                  paddingLeft: '16px',
                  paddingRight: '16px',
                }}>API Status</TableCell>
                {sortedKeys.map((key) => (
                  <TableCell key={key} className={styles.responsiveColumn} sx={{
                    fontWeight: 600,
                    fontSize: '0.8rem',
                    color: '#4a5568',
                    borderBottom: '1px solid #e2e8f0',
                    paddingBottom: '10px',
                    paddingTop: '10px',
                    height: '48px',
                    paddingLeft: '16px',
                    paddingRight: '16px',
                  }}>
                    {key.replace(/_/g, " ").toUpperCase()}
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {eventQueue.map((event, index) => {
                const parsedData: Record<string, any> = {};

                // Parse event data to extract nested values
                Object.values(event.data).forEach((value) => {
                  try {
                    const parsed =
                      typeof value === "string" ? JSON.parse(value) : value;
                    if (typeof parsed === "object" && parsed !== null) {
                      Object.assign(parsedData, parsed);
                    }
                  } catch (e) {
                    // Ignore errors, keep the original value
                  }
                });

                return (
                  <TableRow key={event.id} sx={{
                    '&:hover': {
                      backgroundColor: '#f1f5f9',
                    },
                    borderBottom: '1px solid #e2e8f0',
                  }}>
                    <TableCell sx={{
                      minWidth: '60px',
                      width: '80px',
                      color: 'rgb(2, 8, 23)',
                      fontSize: '16px',
                      fontFamily: 'ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
                      fontWeight: 400,
                      lineHeight: '24px',
                      paddingLeft: '16px',
                      paddingRight: '16px',
                      textAlign: 'center',
                    }}>{index + 1}</TableCell>
                    <TableCell sx={{
                      maxWidth: '250px',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      color: 'rgb(2, 8, 23)',
                      fontSize: '16px',
                      fontFamily: 'ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
                      fontWeight: 400,
                      lineHeight: '24px',
                      paddingLeft: '16px',
                      paddingRight: '16px',
                    }}>
                      {new Date(event.timestamp).toLocaleString()}
                    </TableCell>
                    <TableCell sx={{
                      maxWidth: '250px',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      color: event.status === "success" ? "green" : "red",
                      fontSize: '16px',
                      fontFamily: 'ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
                      fontWeight: 400,
                      lineHeight: '24px',
                      paddingLeft: '16px',
                      paddingRight: '16px',
                    }}>
                      {event.status.toUpperCase()}
                    </TableCell>
                    {sortedKeys.map((key) => (
                      <TableCell key={key} sx={{
                        maxWidth: '250px',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap',
                        color: 'rgb(2, 8, 23)',
                        fontSize: '16px',
                        fontFamily: 'ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
                        fontWeight: 400,
                        lineHeight: '24px',
                        paddingLeft: '16px',
                        paddingRight: '16px',
                      }}>
                        {parsedData[key] !== undefined
                          ? String(parsedData[key]).replace(/^"(.*)"$/, "$1") // Remove surrounding double quotes
                          : "N/A"}
                      </TableCell>
                    ))}
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </Box>
      </TableContainer>
    </Box>
  );
};

export default EventList;
