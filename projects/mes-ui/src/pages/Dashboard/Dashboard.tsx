import React, { useEffect, useState } from "react";
import styles from "../../styles/Dashboard.module.scss"; // Adjust the path as needed
import ReactECharts from "echarts-for-react";
import { useDashboardData, useTabsData } from "../../hooks/useChartData";
import {
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  Box,
  Typography,
} from "@mui/material";
import Chart from "./Chart";
import CollapsibleSidebar from "../../components/Dashboard/CollapsibleSidebar";
import KPICard from "../../components/Dashboard/Charts/KpiCard";
import {
  CheckCircleOutline,
  Close,
  Home,
  SearchOffOutlined,
} from "@mui/icons-material";
import RadialProgress from "../../components/Dashboard/Charts/RadialProgress";
import RadialProgressCard from "../../components/Dashboard/Charts/RadialProgress";
import PieChart from "../../components/Dashboard/Charts/PieChart";
import LineChart from "../../components/Dashboard/Charts/LineChart";
import Heatmap from "../../components/Dashboard/Charts/Heatmap";
import BarGraph from "../../components/Dashboard/Charts/BarGraph";
import BarLineGraph from "../../components/Dashboard/Charts/BarLineGraph";

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`dashboard-tabpanel-${index}`}
      aria-labelledby={`dashboard-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const mockApiResponse = {
  charts: [
    {
      id: 1,
      type: "kpi",
      name: "Total Inspected",
      data: 72000,
      change: 10,
      color: "purple",
      icon: <SearchOffOutlined />,
    },
    {
      id: 2,
      type: "kpi",
      name: "Total Pass",
      data: 21000,
      change: -10,
      color: "green",
      icon: <CheckCircleOutline />,
    },
    {
      id: 3,
      type: "kpi",
      name: "Total Fail",
      data: 51000,
      change: 10,
      color: "red",
      icon: <Close />,
    },
    {
      id: 4,
      type: "kpi",
      name: "Total Orders",
      data: 10000,
      change: -10,
      color: "blue",
      icon: <Home />,
    },

    {
      id: 5,
      type: "pie",
      name: "Defect Distribution",
      dataset: [
        ["part_no", "production_share"],

        ["HE315471", 13],

        ["HE317171-35.12", 12],

        ["HE316231", 11],

        ["HE318152", 10],

        ["HE317598-35.24", 4],

        ["HE315451-20.31", 3],

        ["HE317771-24.18", 2],

        ["HE315731", 2],

        ["HE317981-35.30", 2],

        ["HE316801", 1],

        ["Other", 13],
      ],
    },
    {
      id: 6,
      type: "line",
      name: "OEE Trend",
      dataset: [
        ["Time", "OEE", "Performance", "Availability", "Quality", "Goal"],
        ["08:00", 52, 75, 60, 88, 80],
        ["09:00", 64, 78, 68, 92, 80],
        ["10:00", 49, 74, 55, 90, 80],
        ["11:00", 58, 80, 62, 95, 80],
        ["12:00", 47, 72, 50, 88, 80],
        ["13:00", 63, 79, 65, 93, 80],
        ["14:00", 51, 76, 58, 90, 80],
        ["15:00", 60, 82, 70, 96, 80],
        ["16:00", 55, 80, 66, 94, 80],
      ],
    },

    {
      id: 7,
      type: "radial-progress",
      name: "OEE",
      data: 89,
      width: "extra-wide",
    },
    { id: 8, type: "radial-progress", name: "Performance", data: 80 },
    { id: 9, type: "radial-progress", name: "Availability", data: 60 },
    { id: 10, type: "radial-progress", name: "Quality", data: 85 },
    {
      id: 12,
      type: "bar-line",
      name: "Shift Wise Rejection",
      data: [
        ["Day", "Inspected", "Pass", "FPY"],
        ["Sunday", 85, 43, 50.5],
        ["Monday", 83, 73, 88.0],
        ["Tuesday", 86, 65, 75.6],
        ["Wednesday", 72, 53, 73.6],
        ["Thursday", 85, 43, 50.5],
        ["Friday", 83, 73, 88.0],
        ["Saturday", 86, 65, 75.6],
      ],
      barKeys: ["Inspected", "Pass"],
      lineKeys: ["FPY"],
      yAxis: "Units",
      showToolbox: true,
      showZoom: true,
    },
    {
      id: 13,
      type: "bar",
      name: "Defect Count by Shift",
      data: [
        ["Month", "Time"],
        ["January", 85],
        ["Febraury", 83],
        ["March", 86],
        ["April", 72],
        ["May", 85],
        ["June", 83],
        ["July", 83],
        ["August", 83],
        ["Spetember", 83],
        ["October", 86],
      ],
      yAxis: "Defect",
      showToolbox: true,
      showZoom: true,
    },
    {
      id: 14,
      type: "heatmap",
      name: "Defect Heatmap",
      data: [
        ["part_no", "defect_type", "count"],
        ["HE315471", "Dry solder", 37],
        ["HE315471", "Lifted Component", 35],
        ["HE315471", "Solder Short", 1],
        ["HE315471", "Missing Component", 22],
        ["HE315471", "Wrong Polarity", 4],
        ["HE315471", "Lead Cutting", 4],
        ["HE315471", "Less Solder", 4],

        ["HE316231", "Dry solder", 17],
        ["HE316231", "Lifted Component", 35],
        ["HE316231", "Solder Short", 12],
        ["HE316231", "Missing Component", 16],
        ["HE316231", "Wrong Polarity", 6],
        ["HE316231", "Lead Cutting", 4],
        ["HE316231", "Less Solder", 4],

        ["HE317171-35.12", "Dry solder", 34],
        ["HE317171-35.12", "Lifted Component", 16],
        ["HE317171-35.12", "Solder Short", 11],
        ["HE317171-35.12", "Missing Component", 2],
        ["HE317171-35.12", "Wrong Polarity", 10],
        ["HE317171-35.12", "Less Solder", 2],

        ["HE318152", "Dry solder", 33],
        ["HE318152", "Lifted Component", 35],
        ["HE318152", "Solder Short", 2],
        ["HE318152", "Missing Component", 2],

        ["HE317381", "Dry solder", 27],
        ["HE317381", "Lifted Component", 15],
        ["HE317381", "Solder Short", 2],
        ["HE317381", "Missing Component", 4],
        ["HE317381", "Wrong Polarity", 2],
        ["HE317381", "Lead Cutting", 1],

        ["HE317771-24.18", "Dry solder", 13],
        ["HE317771-24.18", "Lifted Component", 7],
        ["HE317771-24.18", "Solder Short", 7],
        ["HE317771-24.18", "Missing Component", 2],

        ["HE317604", "Dry solder", 3],
        ["HE317604", "Lifted Component", 24],
        ["HE317604", "Solder Short", 1],

        ["HE315731", "Dry solder", 10],
        ["HE315731", "Lifted Component", 5],

        ["HE316351-38.16", "Dry solder", 10],
        ["HE316351-38.16", "Solder Short", 5],

        ["HE317141-35.23", "Dry solder", 6],
        ["HE317141-35.23", "Lifted Component", 4],
        ["HE317141-35.23", "Solder Short", 1],
        ["HE317141-35.23", "Missing Component", 2],

        ["HE315191-40.10", "Dry solder", 12],
        ["HE315191-40.10", "Solder Short", 1],
        ["HE315191-40.10", "Lead Cutting", 1],

        ["HE316361", "Dry solder", 11],
      ],
      axisLabels: { y: "Part No.", x: "Defect Type" },
    },
  ],
  gridData: {
    desktop: [
      { id: 1, row: 1, col: 1 },
      { id: 2, row: 1, col: 2 },
      { id: 3, row: 1, col: 3 },
      { id: 4, row: 1, col: 4 },

      { id: 5, row: 2, col: 1, colspan: 2 },
      { id: 6, row: 2, col: 3, colspan: 2 },

      { id: 7, row: 3, col: 1 },
      { id: 8, row: 3, col: 2 },
      { id: 9, row: 3, col: 3 },
      { id: 10, row: 3, col: 4 },
      { id: 11, row: 3, col: 5 },
      { id: 13, row: 4, col: 6, colspan: 2 },
      { id: 14, row: 4, col: 7, colspan: 2 },
      { id: 12, row: 5, col: 1, colspan: 4 },
    ],
    mobile: [
      { id: 1 },
      { id: 2 },
      { id: 3 },
      { id: 4 },
      { id: 5 },
      { id: 6 },
      { id: 7 },
      { id: 8 },
      { id: 9 },
      { id: 10 },
      { id: 11 },
      { id: 12 },
    ],
  },
};

function Dashboard() {
  const [tabValue, setTabValue] = useState(0);
  const {
    data: tabsData,
    isLoading: isLoadingTabs,
    error: errorTabs,
  } = useTabsData();

  const isMobile = window.innerWidth <= 768;
  const charts = mockApiResponse.charts;
  const layout = isMobile
    ? mockApiResponse.gridData.mobile
    : mockApiResponse.gridData.desktop;

  const gridStyles = {
    display: "grid",
    gridTemplateColumns: isMobile ? "1fr" : "repeat(4, 1fr)",
  };

  const getChartById = (id: number) => charts.find((c) => c.id === id);

  const chartMap = {
    kpi: KPICard,
    pie: PieChart,
    line: LineChart,
    bar: BarGraph,
    "bar-line": BarLineGraph,
    "radial-progress": RadialProgress,
    heatmap: Heatmap,
  };
  const {
    data: dashboardData,
    isLoading: isLoadingDashboard,
    error: errorDashboard,
  } = useDashboardData(tabsData?.[tabValue]?.id);

  useEffect(() => {
    if (tabsData) {
      setTabValue(0);
    }
  }, [tabsData]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const renderLoading = () => (
    <div className={styles.loadingContainer}>
      <CircularProgress />
    </div>
  );

  const renderError = (error: any) => (
    <div className={styles.errorContainer}>
      <Alert severity="error">
        Error loading chart data:{" "}
        {error instanceof Error ? error.message : "Unknown error"}
      </Alert>
    </div>
  );

  if (isLoadingTabs) return renderLoading();

  //   if (errorTabs) return renderError(errorTabs);

  return (
    <div className={styles.dashboard}>
<Box sx={{ borderBottom: 1, borderColor: "divider" }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          aria-label="dashboard tabs"
        >
          {tabsData?.map((tab: any) => (
            <Tab label={tab.name} key={tab.id} />
          ))}
        </Tabs>
      </Box>

      {isLoadingDashboard && renderLoading()}
      {errorDashboard && renderError(errorDashboard)}

      <TabPanel value={tabValue} index={tabValue}>
        <div className={styles.chartGrid}>
          {dashboardData &&
            dashboardData?.charts?.map((chart: any) => (
              <Chart
                key={chart.id}
                chartId={chart.id} height={""}              />
            ))}
          </div>
        
      </TabPanel>
      <div style={gridStyles}>
        {layout.map((item) => {
          return (
            <div
              key={item.id}
              style={{
                gridColumn: `span ${item?.colspan || 1}`,
                backgroundColor: "#fff",
                padding: 10,
                borderRadius: 8,
                minHeight: 100,
              }}
            >
             <Chart
             id={item.id}

             />
            </div>
          );
        })}
      </div>

     </div>
  );
}

export default Dashboard;
