import React, { useState } from 'react';
import KPICard from '../../components/Dashboard/Charts/KpiCard';
import { PieChart } from '@mui/icons-material';
import LineChart from '../../components/Dashboard/Charts/LineChart';
import BarGraph from '../../components/Dashboard/Charts/BarGraph';
import BarLineGraph from '../../components/Dashboard/Charts/BarLineGraph';
import RadialProgress from '../../components/Dashboard/Charts/RadialProgress';
import Heatmap from '../../components/Dashboard/Charts/Heatmap';
import { useChartData } from '../../hooks/useChartData';

interface propType {
    id:number;

}

const chartMap = {
    kpi: KPICard,
    pie: PieChart,
    line: LineChart,
    bar: BarGraph,
    "bar-line": BarLineGraph,
    "radial-progress": RadialProgress,
    heatmap: Heatmap,
  };

const Chart = ({id}:propType)=>{
    const [filters, setFilters] = useState({})
    const {
      data: chartData,
      isLoading: isLoading<PERSON>hart,
      error: errorChart,
    } = useChartData(id, filters);
    const ChartComponent = chartMap[chartData?.type as any];
    if (!ChartComponent) return null;

    return (
          <ChartComponent
            title={chartData?.name}
            data={chartData?.data}
            {...chartData}
          />
      );
}

export default Chart