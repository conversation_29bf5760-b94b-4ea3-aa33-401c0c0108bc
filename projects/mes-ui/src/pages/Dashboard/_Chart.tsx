import React, { useState } from "react";
import styles from "../../styles/Dashboard.module.scss"; // Adjust the path as needed
import ReactECharts from "echarts-for-react";
import { useChartData } from "../../hooks/useChartData";
import {
  IconButton,
  Button,
  Menu,
  MenuItem,
  TextField,
  Checkbox,
  FormControlLabel,
  Box,
  Typography,
  ToggleButtonGroup,
  ToggleButton,
  InputAdornment,
  Tooltip,
  CircularProgress,
} from "@mui/material";
import { CalendarToday, CheckBox, Clear, FilterAltOutlined, Info, InfoOutlined, Percent, Tag, Title } from "@mui/icons-material";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { format } from "date-fns";
interface ChartProps {
  chartId: number;
  height: string
}

const chart_filters: any = {
  created_at: "date",
  value: "int",
  another: "str",
  boolean_value: "bool",
};

const MenuItemIcon = ({ type }: { type: string }) => {
  if (type === "date") return <CalendarToday />;
  if (type === "int") return <Tag />;
  if (type === "str") return <Title />;
  if (type === "bool") return <CheckBox />;
  return <></>;
};

const FilterMenu = ({
  allowedFilters,
  applyFilter,
}: {
  allowedFilters: Record<string, any>;
  applyFilter: (filters: Record<string, any>) => void;
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [fieldMenuAnchorEl, setFieldMenuAnchorEl] =
    useState<null | HTMLElement>(null);
  const [selectedField, setSelectedField] = useState<string | null>(null);
  const [filters, setFilters] = useState<Record<string, any>>({});
  const [inputMode, setInputMode] = useState<Record<string, "exact" | "range">>(
    {}
  );
  const [patternType, setPatternType] = useState<
    Record<string, "suffix" | "prefix">
  >({});

  const open = Boolean(anchorEl);
  const fieldOpen = Boolean(fieldMenuAnchorEl);

  const handleMainClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleFieldClick = (
    event: React.MouseEvent<HTMLElement>,
    field: string
  ) => {
    setSelectedField(field);
    setFieldMenuAnchorEl(event.currentTarget);
  };

  const handleCloseAll = () => {
    setAnchorEl(null);
    setFieldMenuAnchorEl(null);
  };

  const handleChange = (field: string, key: string, value: any) => {
    setFilters((prev) => ({
      ...prev,
      [field]: {
        ...prev[field],
        [key]: value,
      },
    }));
  };

  const renderInput = (field: string, type: string) => {
    const filterValue = filters[field] || {};
    const mode = inputMode[field] || "exact";

    switch (type) {
      case "int": {
        const inputType = "number";

        return (
          <>
            <Box display="flex" alignItems="center" mb={1} gap={1}>
              <ToggleButtonGroup
                value={mode}
                exclusive
                size="small"
                onChange={(e, value) => {
                  if (value) {
                    setInputMode((prev) => ({ ...prev, [field]: value }));
                  }
                }}
              >
                <ToggleButton size="small" value="exact">
                  Exact
                </ToggleButton>
                <ToggleButton size="small" value="range">
                  Range
                </ToggleButton>
              </ToggleButtonGroup>
            </Box>
            {mode === "exact" ? (
              <TextField
                label="Exact Match"
                type={inputType}
                fullWidth
                size="small"
                margin="dense"
                onChange={(e) => handleChange(field, "value", [e.target.value])}
              />
            ) : (
              <Box display="flex" gap={1}>
                <TextField
                  label="Range Start"
                  type={inputType}
                  fullWidth
                  size="small"
                  margin="dense"
                  value={filterValue.value?.[0] || ""}
                  onChange={(e) =>
                    handleChange(field, "value", [
                      e.target.value,
                      filterValue.value?.[1] || "",
                    ])
                  }
                />
                <TextField
                  label="Range End"
                  type={inputType}
                  fullWidth
                  size="small"
                  margin="dense"
                  value={filterValue.value?.[1] || ""}
                  onChange={(e) =>
                    handleChange(field, "value", [
                      filterValue.value?.[0] || "",
                      e.target.value,
                    ])
                  }
                />
              </Box>
            )}
          </>
        );
      }

      case "date": {
        return (
          <>
            <Box display="flex" alignItems="center" mb={1}>
              <FormControlLabel
                control={
                  <Box display="flex" alignItems="center" mb={1} gap={1}>
                    <ToggleButtonGroup
                      value={mode}
                      exclusive
                      size="small"
                      onChange={(e, value) => {
                        if (value) {
                          setInputMode((prev) => ({ ...prev, [field]: value }));
                        }
                      }}
                    >
                      <ToggleButton size="small" value="exact">
                        Exact
                      </ToggleButton>
                      <ToggleButton size="small" value="range">
                        Range
                      </ToggleButton>
                    </ToggleButtonGroup>
                  </Box>
                }
                label=""
              />
            </Box>

            {mode === "exact" ? (
              <DatePicker
                label="Exact Match"
                value={filterValue.value ? new Date(filterValue.value) : null}
                onChange={(newValue) =>
                  handleChange(field, "value", [
                    format(newValue as Date, "yyyy-MM-dd") || "",
                  ])
                }
                slotProps={{ textField: { fullWidth: true, margin: "dense" } }}
              />
            ) : (
              <Box display="flex" gap={1}>
                <DatePicker
                  label="Start Date"
                  value={
                    filterValue.value?.[0]
                      ? new Date(filterValue.value[0])
                      : null
                  }
                  onChange={(newValue) =>
                    handleChange(field, "value", [
                      format(newValue as Date, "yyyy-MM-dd") || "",
                      filterValue.value?.[1] || "",
                    ])
                  }
                  slotProps={{
                    textField: { fullWidth: true, margin: "dense" },
                  }}
                />
                <DatePicker
                  label="End Date"
                  value={
                    filterValue.value?.[1]
                      ? new Date(filterValue.value[1])
                      : null
                  }
                  onChange={(newValue) =>
                    handleChange(field, "value", [
                      filterValue.value?.[0] || "",
                      format(newValue as Date, "yyyy-MM-dd") || "",
                    ])
                  }
                  slotProps={{
                    textField: { fullWidth: true, margin: "dense" },
                  }}
                />
              </Box>
            )}
          </>
        );
      }
      case "str": {
        return (
          <>
            <Box display="flex" alignItems="center" mb={1}>
              <FormControlLabel
                control={
                  <Box display="flex" alignItems="center" mb={1} gap={1}>
                    <ToggleButtonGroup
                      value={mode}
                      exclusive
                      size="small"
                      onChange={(e, value) => {
                        if (value) {
                          setInputMode((prev) => ({ ...prev, [field]: value }));
                        }
                      }}
                    >
                      <ToggleButton size="small" value="exact">
                        Exact
                      </ToggleButton>
                      <ToggleButton size="small" value="pattern">
                        Pattern
                      </ToggleButton>
                    </ToggleButtonGroup>
                  </Box>
                }
                label=""
              />
            </Box>

            {mode === "exact" ? (
              <TextField
                label="Exact Match"
                fullWidth
                margin="dense"
                size="small"
                value={filterValue.value || ""}
                onChange={(e) => handleChange(field, "value", e.target.value)}
              />
            ) : (
              <TextField
                label="Pattern Match"
                fullWidth
                size="small"
                margin="dense"
                value={filterValue.value || ""}
                onChange={(e) => handleChange(field, "value", e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Button
                        size="small"
                        variant="text"
                        onClick={() =>
                          setPatternType((prev) => ({
                            ...prev,
                            [field]:
                              patternType[field] === "prefix"
                                ? "suffix"
                                : "prefix",
                          }))
                        }
                      >
                        {patternType[field] === "prefix"
                          ? "Starts With"
                          : "Ends With"}
                      </Button>
                    </InputAdornment>
                  ),
                }}
              />
            )}
          </>
        );
      }

      case "bool":
        return (
          <FormControlLabel
            control={
              <Checkbox
                checked={filterValue.value === true}
                onChange={(e) => handleChange(field, "value", e.target.checked)}
              />
            }
            label="True"
          />
        );
      default:
        return null;
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <IconButton onClick={handleMainClick}>
        <FilterAltOutlined />
      </IconButton>

      {/* First-level menu: list of filterable fields */}
      <Menu anchorEl={anchorEl} open={open} onClose={handleCloseAll}>
        {Object.entries(allowedFilters).map(([field, type]) => (
          <MenuItem sx={{ gap:10,justifyContent:"flex-start" }} key={field} onClick={(e) => handleFieldClick(e, field)} >
           <Typography variant="body2">{field}</Typography>
           <MenuItemIcon type={type} />
          </MenuItem>
        ))}

        <Button fullWidth onClick={() => {
          applyFilter(filters);
          handleCloseAll();
        }}>Apply</Button>
      </Menu>

      {/* Second-level menu: field inputs */}
      <Menu
        anchorEl={fieldMenuAnchorEl}
        open={fieldOpen}
        onClose={() => setFieldMenuAnchorEl(null)}
        anchorOrigin={{ horizontal: "right", vertical: "top" }}
        transformOrigin={{ horizontal: "left", vertical: "top" }}
      >
        {selectedField && (
          <Box padding={2} width={300}>
            <Box display="flex" alignItems="center" mb={1} gap={1}>
              <Typography variant="h6" gutterBottom>
                {selectedField}
              </Typography>

              <Tooltip title="Clear">
                <IconButton
                  onClick={() => {
                    handleChange(selectedField, "value", undefined);
                  }}
                >
                  <Clear />
                </IconButton>
              </Tooltip>
            </Box>
            {renderInput(selectedField, allowedFilters[selectedField])}
            <Box mt={2}>
              <Button
                variant="contained"
                onClick={() => setFieldMenuAnchorEl(null)}
                fullWidth
              >
                Apply
              </Button>
            </Box>
          </Box>
        )}
      </Menu>

    </LocalizationProvider>
  );
};
function reviveFunctions(input: any): any {
  if (Array.isArray(input)) {
    return input.map(reviveFunctions);
  }

  if (input && typeof input === "object") {
    const revived: any = {};
    for (const key of Object.keys(input)) {
      const value = input[key];
      if (typeof value === "string") {
        const trimmed = value.trim();

        // Match typical function expressions: function(...) { ... }
        const isFunctionExpr = /^function\s*\([\s\S]*?\)\s*\{[\s\S]*\}$/.test(
          trimmed
        );

        if (isFunctionExpr) {
          try {
            revived[key] = eval(
              `(function() { const echarts = window.echarts; return ${trimmed}; })()`
            );
            continue;
          } catch (e) {
            console.warn(`Error parsing function at key "${key}":`, e);
          }
        }

        // Match immediately-invoked function expressions: (function() { ... })()
        const isIIFE =
          /^\(\s*function\s*\([\s\S]*?\)\s*\{[\s\S]*\}\s*\)\s*\(\s*\)\s*$/.test(
            trimmed
          );

        if (isIIFE) {
          try {
            revived[key] = eval(
              `(function() { const echarts = window.echarts; return ${trimmed}; })()`
            );
            continue;
          } catch (e) {
            console.warn(`Error parsing IIFE at key "${key}":`, e);
          }
        }

        revived[key] = value;
      } else {
        revived[key] = reviveFunctions(value); // Recurse into nested object
      }
    }
    return revived;
  }

  return input;
}

const Chart: React.FC<ChartProps> = ({ chartId,height }) => {
  const [filters, setFilters] = useState({})
  const {
    data: chartData,
    isLoading: isLoadingChart,
    error: errorChart,
  } = useChartData(chartId, filters);

  if (isLoadingChart) {
    return (
      <div style={{textAlign:'center'}} className={styles.chartContainer}>
        <CircularProgress />
      </div>
    );
  }
  if (
    !chartData?.chart_data ||
    typeof chartData?.chart_data !== "object" ||
    !chartData?.chart_data?.series ||
    !chartData?.chart_data?.dataset
  ) {
    return (
      <div className={styles.chartContainer}>
        <h3>{chartData?.chart_instance?.name}</h3>
        <div>Loading or invalid chart data</div>
      </div>
    );
  }

  const revivedOptions = reviveFunctions(chartData?.chart_data);
  return (
    <div className={styles.chartContainer}>
      <Box sx={{ display: "flex", justifyContent: "space-between" }}>
        {/* <h3>{chartData?.chart_instance?.name}</h3> */}
        <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
         {chartData?.chart_instance?.description && <Tooltip title={chartData?.chart_instance?.description}>
            <IconButton>
              <InfoOutlined/>
            </IconButton>
          </Tooltip>}
        {Object.keys(chartData?.chart_instance?.filter_config).length > 0 && (
          <FilterMenu
            allowedFilters={chartData?.chart_instance?.filter_config}
            applyFilter={(f) => setFilters(Object.keys(f).reduce((acc,key)=>({...acc,[key]:f[key].value}),{}))}
          />
        )}
        </Box>
      </Box>
      <ReactECharts
        option={revivedOptions}
        style={{ width: "100%" }}
        notMerge={true}
        lazyUpdate={true}
      />
    </div>
  );
};

export default Chart;
