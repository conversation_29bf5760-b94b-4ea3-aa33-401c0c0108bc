import ErrorMessage from "../components/ErrorMessage";
import { useEmbedUrl } from "../hooks/useEmbedUrl";
import { useEffect, useRef, useState } from "react";

const Reports = () => {
  const { data, error, isLoading } = useEmbedUrl();
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // useEffect(() => {
  //   const handleResize = () => {
  //     if (iframeRef.current) {
  //       iframeRef.current.style.height = `${window.innerHeight * 0.9}px`; // 90% of viewport height
  //     }
  //   };

  //   window.addEventListener("resize", handleResize);
  //   handleResize(); // Set initial height

  //   return () => window.removeEventListener("resize", handleResize);
  // }, []);

  if (isLoading) {
    return <p>Loading...</p>;
  }

  if (error) {
    return (
      <ErrorMessage
        message={"You are not authorized to access the resource."}
      />
    );
  }

  return (
    <div>
      {data && data.embedUrl ? (
        <iframe
          ref={iframeRef}
          src={data.embedUrl}
          width="100%"
          style={{ border: "none" }}
          height="2000px"
          title="QuickSight Dashboard"
        ></iframe>
      ) : (
        <p>Embed URL is not available.</p>
      )}
    </div>
  );
};

export default Reports;
