export const getAccessToken = () => {
  return localStorage.getItem("accessToken");
};

export const getCurrentUser = () => {
  const userString = localStorage.getItem("user");
  return userString ? JSON.parse(userString) : null;
};

export const getCurrentUserId = () => {
  const user = getCurrentUser();
  return user?.id || null;
};

export const clearAuthData = () => {
  localStorage.removeItem("accessToken");
  localStorage.removeItem("refreshToken");
  localStorage.removeItem("user");
};
