const possibleFormActions: string[] = [
  "onChange", // Triggered when a form element value changes.
  "onBlur", // Triggered when an element loses focus.
  "onFocus", // Triggered when an element gains focus.
  "onClick", // Triggered when an element is clicked.
  "onSelect", // Triggered when text is selected in an input field.

  // Specific to <textarea>
  "onInput", // Triggered when input changes in a textarea.
  "onKeyDown", // Triggered when a key is pressed down in a textarea.
  "onKeyPress", // Triggered when a key is pressed in a textarea.
  "onKeyUp", // Triggered when a key is released in a textarea.

  // Specific to <select> and <option>
  "onChange", // Triggered when a selection is made from a <select> dropdown.

  // Specific to <input type="radio">
  "onChange", // Triggered when a radio button is selected.
  "onFocus", // Triggered when a radio button gains focus.
  "onBlur", // Triggered when a radio button loses focus.
  "onClick", // Triggered when a radio button is clicked.

  // Other general events that might apply to form elements
  "onSubmit", // Triggered when the form is submitted.
  "onReset", // Triggered when the form is reset.
  "onInvalid", // Triggered when an input field is invalid (e.g., validation fails).
];

export const actionType = [...new Set(possibleFormActions)];
