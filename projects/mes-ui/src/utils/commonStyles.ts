import { SxProps, Theme } from '@mui/material/styles';

// Common TextField styles for consistent appearance across the application
export const textFieldStyle: SxProps<Theme> = {
  '& .MuiOutlinedInput-root': {
    height: '36px',
    borderRadius: '3px',
    fontSize: '14px',
    backgroundColor: '#fff',
    '& input': {
      padding: '8px 12px',
    },
    '& fieldset': {
      borderColor: '#e0e0e0',
    },
    '&:hover fieldset': {
      borderColor: '#bdbdbd',
    },
    '&.Mui-focused fieldset': {
      borderColor: '#1976d2',
      borderWidth: '1px',
    },
  },
  '& .MuiInputLabel-root': {
    fontSize: '13px',
    transform: 'translate(14px, 10px) scale(1)',
    '&.MuiInputLabel-shrink': {
      transform: 'translate(14px, -6px) scale(0.75)',
    },
  },
  '& .MuiFormHelperText-root': {
    marginTop: '2px',
    fontSize: '11px',
  },
};

// Style for multiline text fields (like description fields)
export const multilineTextFieldStyle: SxProps<Theme> = {
  '& .MuiOutlinedInput-root': {
    borderRadius: '3px',
    fontSize: '14px',
    backgroundColor: '#fff',
    padding: '0',
    '& textarea': {
      padding: '8px 12px',
    },
    '& fieldset': {
      borderColor: '#e0e0e0',
    },
    '&:hover fieldset': {
      borderColor: '#bdbdbd',
    },
    '&.Mui-focused fieldset': {
      borderColor: '#1976d2',
      borderWidth: '1px',
    },
  },
  '& .MuiInputLabel-root': {
    fontSize: '13px',
    '&.MuiInputLabel-shrink': {
      transform: 'translate(14px, -6px) scale(0.75)',
    },
  },
  '& .MuiFormHelperText-root': {
    marginTop: '2px',
    fontSize: '11px',
  },
};

// Common button styles
export const outlinedButtonStyle: SxProps<Theme> = {
  textTransform: "none",
  borderColor: '#e0e0e0',
  color: '#333',
  borderRadius: '3px',
  padding: '6px 14px',
  fontSize: '13px',
  fontWeight: 500,
  minWidth: '80px',
  height: '32px',
  '&:hover': {
    borderColor: '#bdbdbd',
    backgroundColor: 'rgba(0, 0, 0, 0.04)'
  }
};

export const containedButtonStyle: SxProps<Theme> = {
  textTransform: "none",
  fontWeight: 500,
  fontSize: '13px',
  borderRadius: '3px',
  padding: '6px 14px',
  minWidth: '100px',
  height: '32px',
  bgcolor: '#000',
  '&:hover': {
    bgcolor: '#333'
  }
};

// Modal header style
export const modalHeaderStyle: SxProps<Theme> = {
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  p: 1.5,
  px: 2,
  borderBottom: '1px solid #eee',
  '& .MuiTypography-root': {
    fontSize: '15px',
    fontWeight: 500
  },
  '& .MuiIconButton-root': {
    padding: '4px',
    '& .MuiSvgIcon-root': {
      fontSize: '18px'
    }
  }
};

// Modal container style
export const modalContainerStyle: SxProps<Theme> = {
  position: "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: 450,
  bgcolor: "#fff",
  boxShadow: "0px 4px 20px rgba(0, 0, 0, 0.1)",
  p: 0,
  borderRadius: 1,
  overflow: 'hidden'
};

// Section header style with bottom border
export const sectionHeaderStyle: SxProps<Theme> = {
  p: 1.5,
  pl: 2,
  borderBottom: '1px solid #e2e8f0',
  bgcolor: '#0f172a',
  color: 'white',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  gap: 1,
  '& .MuiTypography-root': {
    fontWeight: 600,
    fontSize: '0.95rem'
  }
};
