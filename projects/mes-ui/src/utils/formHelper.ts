import { fetchProductBySerialNumber } from "../hooks/useForm";
import axiosInstance from "./axiosInstance";

/**
 * Parses a serial number and extracts its components.
 * @param serialNumber - The serial number to parse.
 * @returns An object containing partNumber, po, and sequentialNumber.
 */
export function parseSerialNumber(serialNumber: string): {
  partNumber: string;
  po: string;
  sequentialNumber: string;
} {
  // Split the serial number by the delimiter #
  const [partSection, rest] = serialNumber.split("#");

  // Extract Part Number (before '#')
  const partNumber = partSection || "";

  // Extract PO and Sequential Number (after '#')
  const po = rest?.slice(-11, -4) || "";
  const sequentialNumber = rest?.slice(-4) || "";

  return {
    partNumber,
    po,
    sequentialNumber,
  };
}

export async function getProductDataBySerialNumber(
  serialNumber: string,
  showSnackbar: (
    message: string,
    severity?: "error" | "success" | "warning" | "info" | undefined
  ) => void
) {
  try {
    const { partNumber, po } = parseSerialNumber(serialNumber);
    const data = await fetchProductBySerialNumber(serialNumber);
    if (data) {
      return {
        partNumber,
        po,
        productData: data,
      };
    }
    return {
      partNumber,
      po,
      productData: null,
    };
  } catch (e: any) {
    showSnackbar(
      `Product part with number ${serialNumber} not found `,
      "error"
    );
    console.log(e);
    return {
      partNumber: "",
      po: "",
      productData: null,
    };
  }
}
export async function fetchEventByFilterCrtieria(
  filter: Record<string, string | number | Date>
) {
  try {
    const params: { [key: string]: any } = { ...filter };
    const response = await axiosInstance.get("/operation/api/events", {
      params,
    });
    return response.data;
  } catch (e: any) {
    console.log(e);
    return null;
  }
}

/**
 * Converts a date string with a timezone to UTC and formats it as "YYYY-MM-DD HH:mm:ss".
 * If an error occurs, it returns the original date string.
 * @param dateStr - The date string with a timezone offset (e.g., "2024-12-23T20:54:49.766000+05:30").
 * @returns {string} - The formatted UTC date and time or the original input string in case of error.
 */
export function formatToUtcDateTime(dateTimeString: string): string {
  try {
    return (
      dateTimeString.split("T")[0] +
      " " +
      dateTimeString.split("T")[1].split(".")[0]
    );
  } catch (error) {
    console.error("Error parsing date:", error);
    return dateTimeString; // Return the original string if there's an error
  }
}

export const initializeFormValues = (fields: any[]): any => {
  return fields.reduce((acc: any, field: any) => {
    acc[field.name] = "";
    return acc;
  }, {});
};

export const initializeNestedValues = (fields: any[]): any => {
  const nestedFields: any = fields.find(
    (field: any) => field.type === "component" && field.form_fields?.length
  );
  if (nestedFields?.form_fields?.length)
    return initializeFormValues(nestedFields.form_fields);
};
