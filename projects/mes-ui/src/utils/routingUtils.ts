import { Node, Edge, MarkerType } from 'reactflow';
import { RoutingSchema } from '../hooks/useRoutingApi';

/**
 * Creates a React Flow edge with consistent styling
 */
export const createEdge = (
  source: string,
  target: string,
  label: string,
  color: string
): Edge => ({
  id: `edge-${source}-${target}`,
  source,
  target,
  type: "smoothstep",
  animated: true,
  label,
  style: {
    stroke: color,
    strokeWidth: 3,
    strokeDasharray: source.includes("rework") ? "5,5" : "none",
  },
  labelStyle: {
    fill: color,
    fontSize: 12,
    fontWeight: "bold",
    backgroundColor: "#ffffff",
    padding: "2px",
    borderRadius: "3px",
  },
  markerEnd: {
    type: MarkerType.ArrowClosed,
    width: 14,
    height: 14,
    color: "#000000",
  },
  // Use the handle positions that match the CustomNode component
  sourceHandle: null, // Let React Flow determine the best source handle
  targetHandle: null, // Let React Flow determine the best target handle
});

/**
 * Generates a style object for a node based on its type and position in the flow
 */
export const getNodeStyle = (
  isStart: boolean,
  isEnd: boolean,
  type: string
): React.CSSProperties => {
  const baseStyle: React.CSSProperties = {
    padding: "10px 20px",
    borderRadius: 8,
    fontSize: 20,
    color: "#fff",
    textAlign: "center",
    fontWeight: "bold",
  };

  const styles: Record<string, React.CSSProperties> = {
    machine: { backgroundColor: "#3498db" },
    scanner: { backgroundColor: "#2ecc71" },
    rework_station: { backgroundColor: "#e74c3c" },
    condition: { backgroundColor: "#f1c40f" },
  };

  if (isStart)
    return { ...baseStyle, backgroundColor: "#16a085", fontWeight: "bold" };
  if (isEnd)
    return { ...baseStyle, backgroundColor: "#8e44ad", fontWeight: "bold" };
  return { ...baseStyle, ...(styles[type] || { backgroundColor: "#95a5a6" }) };
};

/**
 * Converts a routing schema to React Flow nodes and edges
 */
export function convertRoutingToFlow(routingSchema: RoutingSchema): {
  nodes: Node[];
  edges: Edge[];
} {
  const nodes: Node[] = [];
  const edges: Edge[] = [];
  const { components, route } = routingSchema;
  const { start, end } = route;

  // Sort nodes in a logical order for display
  const sortedKeys = sortNodesForDisplay(routingSchema);

  // Calculate node positions
  const nodeLevels = calculateNodeLevels(sortedKeys, route);

  // Create nodes
  createNodes(sortedKeys, components, start, end, nodeLevels, nodes);

  // Create edges
  createEdges(route, edges);

  return { nodes, edges };
}

/**
 * Sorts nodes in a logical order for display
 */
function sortNodesForDisplay(routingSchema: RoutingSchema): string[] {
  const { components, route } = routingSchema;
  const { start, end } = route;

  const sortedKeys: string[] = [];
  const visited = new Set();

  const traverse = (node: string) => {
    if (visited.has(node) || !components[node]) return;
    visited.add(node);
    sortedKeys.push(node);
    const connection = route.connections[node];
    if (connection) {
      if (connection.towards?.default && connection.towards.default !== end) {
        traverse(connection.towards.default);
      }
      if (connection.towards_many)
        connection.towards_many.forEach((target) => traverse(target));
      if (connection.towards?.conditions) {
        connection.towards.conditions.forEach((condition) => {
          if (condition.target && condition.target !== end)
            traverse(condition.target);
        });
      }
    }
  };

  traverse(start);

  if (!visited.has(end) && components[end]) {
    sortedKeys.push(end);
  }

  return sortedKeys;
}

/**
 * Calculates node levels for positioning
 */
function calculateNodeLevels(
  sortedKeys: string[],
  route: RoutingSchema['route']
): Record<string, number> {
  let level = 0;
  const nodeLevels: Record<string, number> = {};

  sortedKeys.forEach((key: string, index: number) => {
    if (index > 0 && route.connections[sortedKeys[index - 1]]) {
      level++;
    }
    nodeLevels[key] = level;
  });

  return nodeLevels;
}

/**
 * Creates nodes from the sorted keys
 */
function createNodes(
  sortedKeys: string[],
  components: RoutingSchema['components'],
  start: string,
  end: string,
  nodeLevels: Record<string, number>,
  nodes: Node[]
): void {
  const yOffset = 100;
  const xOffset = 200;
  const defaultSpacingY = 100;
  const defaultSpacingX = 200;

  sortedKeys.forEach((key: string) => {
    nodes.push({
      id: key,
      type: "customNode",
      data: {
        label: components[key].name,
        type: components[key].node_type,
        isStart: key === start,
        isEnd: key === end,
      },
      position: components[key].position || {
        x: xOffset + nodeLevels[key] * defaultSpacingX,
        y: yOffset + nodeLevels[key] * defaultSpacingY,
      },
      style: getNodeStyle(
        key === start,
        key === end,
        components[key].node_type
      ),
    });
  });
}

/**
 * Creates edges from the route connections
 */
function createEdges(
  route: RoutingSchema['route'],
  edges: Edge[]
): void {
  Object.keys(route.connections).forEach((key) => {
    const connection = route.connections[key];

    // Handle multi-path connections
    if (connection.towards_many) {
      connection.towards_many.forEach((target) => {
        const edge = createEdge(key, target, "Multi-path", "#33ff57");
        // Assign specific handles for multi-path connections
        edge.sourceHandle = "right"; // Use right handle for outgoing connections
        edge.targetHandle = "left";  // Use left handle for incoming connections
        edges.push(edge);
      });
    }

    // Handle default connections
    if (connection.towards?.default) {
      const edge = createEdge(
        key,
        connection.towards.default,
        connection.towards.route_type || "Default",
        connection.towards.route_type === "main" ? "#007bff" : "#e74c3c"
      );
      // Assign specific handles for default connections
      edge.sourceHandle = "bottom"; // Use bottom handle for outgoing default connections
      edge.targetHandle = "top";    // Use top handle for incoming default connections
      edges.push(edge);
    }

    // Handle conditional connections
    if (connection.towards?.conditions) {
      connection.towards.conditions.forEach((condition) => {
        const edge = createEdge(
          key,
          condition.target,
          `Condition: ${condition.operator}`,
          "#f1c40f"
        );
        // Assign specific handles for conditional connections
        edge.sourceHandle = "right"; // Use right handle for outgoing conditional connections
        edge.targetHandle = "left";  // Use left handle for incoming conditional connections
        edges.push(edge);
      });
    }
  });
}
