import { fetchFormConfig, fetchReferenceCategories } from "../../hooks/useForm";
import { ApiFormConfig } from "../../interfaces/form-config.api.interface";
import { initializeFormValues, initializeNestedValues } from "../formHelper";

const configCategoryMapping: Record<string, string> = {
  repair_action: "repair_action",
  fault_type: "fault_types",
  fault_cause: "fault_cause",
  make: "solder_paste_make",
  stencil_washing_status: "stencil_washing_status",
  aoi_person: "aoi_persons",
  approved_by: "droppage_approved_persons",
};

export async function fetchAndProcessFormConfig(
  formId: number,
  setFormConfig: React.Dispatch<React.SetStateAction<ApiFormConfig | null>>,
  setInitialConfig: React.Dispatch<React.SetStateAction<ApiFormConfig | null>>,
  setFormValues: React.Dispatch<any>,
  setNestedFieldValues: React.Dispatch<any>,
  setError: React.Dispatch<React.SetStateAction<boolean>>
) {
  try {
    const response: any = await fetchFormConfig(formId);
    if (response?.form_schema?.form_fields?.length) {
      const hasFaultFields = (fields: any[]): boolean =>
        fields.some(
          (field) =>
            [
              "fault_type",
              "fault_cause",
              "repair_action",
              "make",
              "stencil_washing_status",
              "aoi_person",
              "approved_by",
            ].includes(field.name) ||
            (field.type === "component" &&
              field.form_fields?.length &&
              hasFaultFields(field.form_fields))
        );

      if (hasFaultFields(response.form_schema.form_fields)) {
        const categoriesResponse = await fetchReferenceCategories();
        if (categoriesResponse?.results?.length) {
          const categories = categoriesResponse.results;

          const processFields = (fields: any[]): any[] =>
            fields.map((field) => {
              if (field.type === "component" && field.form_fields?.length) {
                return {
                  ...field,
                  form_fields: processFields(field.form_fields),
                };
              } else if (
                [
                  "fault_type",
                  "fault_cause",
                  "repair_action",
                  "make",
                  "stencil_washing_status",
                  "aoi_person",
                  "approved_by",
                ].includes(field.name)
              ) {
                const categoryCode = configCategoryMapping[field.name];

                const category = categories.find(
                  (cat: any) => cat.code === categoryCode
                );

                if (category) {
                  return {
                    ...field,
                    options: category.values.map((item: any) => ({
                      label: item.value,
                      value: item.code,
                    })),
                  };
                }
              }
              return field;
            });

          response.form_schema.form_fields = processFields(
            response.form_schema.form_fields
          );
        }
      }
      setFormConfig(response);
      setInitialConfig(response);
      setFormValues(initializeFormValues(response.form_schema.form_fields));
      setNestedFieldValues(
        initializeNestedValues(response.form_schema.form_fields)
      );
    }
  } catch (error) {
    setError(true);
    console.error("Error fetching form config:", error);
  }
}
