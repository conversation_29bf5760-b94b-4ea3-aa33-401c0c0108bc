import { Node, <PERSON> } from "reactflow";
import { v4 as uuidv4 } from "uuid";
import EL<PERSON> from "elkjs/lib/elk.bundled.js";

const elk = new ELK();
const spacingX = 200; // Horizontal spacing
const spacingY = 150; // Vertical spacing

interface ComponentSchema {
  type: string;
}

interface RoutingSchema {
  components: Record<string, ComponentSchema>;
  route: {
    connections: Record<
      string,
      {
        towards?: { default?: string; route_type?: string; conditions?: any[] };
        towards_many?: string[];
      }
    >;
  };
}

export async function convertRoutingToFlow(
  routingSchema: RoutingSchema
): Promise<{
  nodes: Node[];
  edges: Edge[];
}> {
  const nodes: Node[] = [];
  const edges: Edge[] = [];

  const { components, route } = routingSchema;

  // 1️⃣ **Create Nodes**
  Object.keys(components).forEach((key) => {
    nodes.push({
      id: key,
      type: "custom", // Use custom node
      data: { label: key, type: components[key].type },
      position: { x: 0, y: 0 }, // Position will be calculated later
    });
  });

  // 2️⃣ **Create Edges**
  Object.keys(route.connections).forEach((key) => {
    const connection = route.connections[key];

    if (connection.towards_many) {
      connection.towards_many.forEach((target) => {
        edges.push(createEdge(key, target, "multi-path", "#999"));
      });
    }

    if (connection.towards?.default) {
      edges.push(
        createEdge(
          key,
          connection.towards.default,
          connection.towards.route_type || "",
          "#007bff"
        )
      );
    }

    if (connection.towards?.conditions) {
      connection.towards.conditions.forEach((condition) => {
        edges.push(
          createEdge(
            key,
            condition.target,
            `If ${condition.left.path} == ${condition.right.value}`,
            "red",
            true
          )
        );
      });
    }
  });

  // 3️⃣ **Use Elk.js for Automatic Layout**
  const graph = {
    id: "root",
    layoutOptions: {
      "elk.direction": window.innerWidth < 768 ? "DOWN" : "RIGHT", // Mobile = TB, Desktop = LR
      "elk.spacing.nodeNode": `${spacingX}`, // Space between nodes
      "elk.layered.spacing.nodeNodeBetweenLayers": `${spacingY}`,
    },
    children: nodes.map((node) => ({ id: node.id, width: 150, height: 50 })),
    edges: edges.map((edge) => ({
      id: edge.id,
      sources: [edge.source],
      targets: [edge.target],
    })),
  };

  const layout = await elk.layout(graph);

  // 4️⃣ **Apply computed positions**
  layout.children?.forEach((node) => {
    const foundNode = nodes.find((n) => n.id === node.id);
    if (foundNode) {
      foundNode.position = { x: node.x || 0, y: node.y || 0 };
    }
  });

  return { nodes, edges };
}

const createEdge = (
  source: string,
  target: string,
  label: string,
  stroke: string,
  dashed = false
): Edge => ({
  id: uuidv4(),
  source,
  target,
  animated: true,
  label,
  style: { stroke, strokeDasharray: dashed ? "5,5" : "none" },
});
