import * as Yup from "yup";

const getMultiSelectValidation = (fieldLabel: string, isRequired: boolean) => {
  let schema = Yup.array().of(Yup.string());
  if (isRequired) {
    schema = schema
      .of(Yup.string().required("Each selected value must be valid"))
      .min(1, `${fieldLabel} must have at least one selected value`)
      .required(`${fieldLabel} is required`);
  }
  return schema;
};

const getStringValidation = (fieldLabel: string, isRequired: boolean) => {
  return isRequired
    ? Yup.string().required(`${fieldLabel} is required`)
    : Yup.string();
};

const getSerialNumberValidation = () => {
  return Yup.string()
    .matches(/#/, "Serial number must include a hash (#)")
    .test(
      "length",
      "Serial number must be at least 15 characters long",
      (value: any) => value?.length >= 15
    )
    .required("Serial number is required");
};

// Recursive Validation for Nested Component Fields
const getComponentValidation = (field: any) => {
  return Yup.array()
    .of(
      Yup.object().shape(
        field.form_fields.reduce((nestedAcc: any, nestedField: any) => {
          if (nestedField.required) {
            nestedAcc[nestedField.name] =
              nestedField.type === "multi-select"
                ? getMultiSelectValidation(nestedField.label, true)
                : getStringValidation(nestedField.label, true);
          } else {
            nestedAcc[nestedField.name] =
              nestedField.type === "multi-select"
                ? getMultiSelectValidation(nestedField.label, false)
                : getStringValidation(nestedField.label, false);
          }
          return nestedAcc;
        }, {})
      )
    )
    .min(
      1,
      `${field.label} must contain at least one object with required fields`
    )
    .required(`${field.label} is required`);
};

// Main Validation Schema
export const getValidationSchema = (formConfig: any) => {
  return Yup.object().shape(
    formConfig?.form_schema?.form_fields?.reduce((acc: any, field: any) => {
      if (field.required) {
        if (field.type === "multi-select") {
          acc[field.name] = getMultiSelectValidation(field.label, true);
        } else if (field.name === "serial_number") {
          acc[field.name] = getSerialNumberValidation();
        } else if (field.name === "sign_in_user") {
          acc[field.name] = getStringValidation(field.label, true);
        } else if (field.type === "component" && field.form_fields?.length) {
          acc[field.name] = getComponentValidation(field);
        } else {
          acc[field.name] = getStringValidation(field.label, true);
        }
      } else {
        acc[field.name] =
          field.type === "multi-select"
            ? getMultiSelectValidation(field.label, false)
            : getStringValidation(field.label, false);
      }
      return acc;
    }, {} as Record<string, Yup.Schema<any>>)
  );
};
