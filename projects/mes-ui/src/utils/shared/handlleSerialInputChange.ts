export const handleSerialNumberChange = (
  handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void,
  formValues: Record<string, any>,
  serial_number?: string
) => {
  const updateObject: Record<string, object> = {
    serial_number: { serial_number: serial_number ?? "" },
    part_number: { isVisible: false, part_number: "" },
    po: { isVisible: false, po: "" },
    part_description: {
      isVisible: false,
      part_description: "",
    },
  };

  if (Object.keys(formValues).includes("failed_stage")) {
    updateObject["failed_stage"] = {
      fault_reported: "",
      isVisible: false,
    };
  }

  if (Object.keys(formValues).includes("fault_reported")) {
    updateObject["fault_reported"] = {
      fault_reported: "",
      isVisible: false,
    };
  }

  if (Object.keys(formValues).includes("fault_details")) {
    updateObject["fault_details"] = {
      fault_details: [],
    };
    updateObject["fault_cause"] = {
      fault_cause: "",
    };
    updateObject["fault_type"] = {
      fault_type: "",
    };
    updateObject["issue_type"] = {
      issue_type: "",
    };
    updateObject["component_location"] = {
      issue_type: "",
    };
  }

  handleFormConfigUpdate(updateObject);
};
