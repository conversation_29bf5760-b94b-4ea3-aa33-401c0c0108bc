{"routing_schema": {"id": "r1", "name": "material-m1-routing", "product_id": "m1", "type": "material-type", "components": {"p0": {"type": "machine", "meta": {}}, "p1": {"type": "machine"}, "p2": {"type": "scanner"}, "r1": {"type": "rework_station"}, "r2": {"type": "rework_station"}, "p3": {"type": "computer", "allowed_producers": ["p1", "p2"], "comment": "allowed_producers states the inputs that are allowed to connect to this process block"}}, "route": {"start": "p0", "end": "p3", "connections": {"p0": {"start": true, "towards": {"default": "", "route_type": "main", "conditions": null}, "towards_many": ["p1", "p1`", "p2`"]}, "p1": {"towards": {"default": "p2", "route_type": "main", "conditions": null}}, "p2": {"towards": {"default": "p3", "route_type": "main", "conditions": [{"operator": "equals", "left": {"type": "prop", "node": "p2", "path": "event.inspection_status"}, "right": {"type": "value", "value": "failed"}, "target": "r1", "route_type": "rework"}]}}, "r1": {"towards": {"default": "r2", "route_type": "rework", "conditions": null}}, "r2": {"towards": {"default": "p2", "route_type": "rework", "conditions": null}}, "p3": {"towards": {"end": true, "default": "end", "route_type": "main", "conditions": null}}}}}}