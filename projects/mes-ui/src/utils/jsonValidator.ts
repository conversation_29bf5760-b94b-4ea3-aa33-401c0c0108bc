/**
 * Utility functions for JSON validation and schema checking
 */

/**
 * Validates if a string is a valid JSON with balanced brackets
 * @param jsonString The JSON string to validate
 * @returns Object with validation result and error message if any
 */
export const validateJson = (jsonString: string): { isValid: boolean; error?: string } => {
  if (!jsonString || jsonString.trim() === '') {
    return { isValid: false, error: 'JSON is empty' };
  }

  // Check for balanced brackets
  const bracketPairs: Record<string, string> = {
    '{': '}',
    '[': ']',
    '(': ')'
  };
  
  const stack: string[] = [];
  
  for (let i = 0; i < jsonString.length; i++) {
    const char = jsonString[i];
    
    // If opening bracket, push to stack
    if ('{[('.includes(char)) {
      stack.push(char);
    } 
    // If closing bracket, check if it matches the last opening bracket
    else if ('}])'.includes(char)) {
      const lastOpenBracket = stack.pop();
      if (!lastOpenBracket || bracketPairs[lastOpenBracket] !== char) {
        return { 
          isValid: false, 
          error: `Unbalanced brackets at position ${i}: found '${char}' without matching opening bracket` 
        };
      }
    }
  }
  
  // Check if all brackets were closed
  if (stack.length > 0) {
    return { 
      isValid: false, 
      error: `Unbalanced brackets: unclosed '${stack.pop()}' brackets` 
    };
  }
  
  // Try to parse the JSON
  try {
    JSON.parse(jsonString);
    return { isValid: true };
  } catch (error) {
    if (error instanceof Error) {
      return { isValid: false, error: `Invalid JSON: ${error.message}` };
    }
    return { isValid: false, error: 'Invalid JSON format' };
  }
};

/**
 * Validates if a JSON object conforms to the routing schema
 * @param json The parsed JSON object
 * @returns Object with validation result and error message if any
 */
export const validateRoutingSchema = (json: any): { isValid: boolean; error?: string } => {
  if (!json) {
    return { isValid: false, error: 'JSON is empty' };
  }
  
  // Check schema structure
  if (!json.schema) {
    return { isValid: false, error: 'Missing "schema" property' };
  }
  
  if (!json.schema.routing_schema) {
    return { isValid: false, error: 'Missing "schema.routing_schema" property' };
  }
  
  const schema = json.schema.routing_schema;
  
  if (!schema.route) {
    return { isValid: false, error: 'Missing "schema.routing_schema.route" property' };
  }
  
  if (!schema.components) {
    return { isValid: false, error: 'Missing "schema.routing_schema.components" property' };
  }
  
  // Check route data
  const route = schema.route;
  if (!route.start) {
    return { isValid: false, error: 'Missing "route.start" property' };
  }
  
  if (!route.end) {
    return { isValid: false, error: 'Missing "route.end" property' };
  }
  
  if (!route.connections || typeof route.connections !== 'object') {
    return { isValid: false, error: 'Missing or invalid "route.connections" property' };
  }
  
  // Check if start and end nodes exist in components
  const components = schema.components;
  if (!components[route.start]) {
    return { isValid: false, error: `Start node "${route.start}" not found in components` };
  }
  
  if (route.end !== 'end' && !components[route.end]) {
    return { isValid: false, error: `End node "${route.end}" not found in components` };
  }
  
  return { isValid: true };
};

/**
 * Format a JSON string with proper indentation
 * @param jsonString The JSON string to format
 * @returns Formatted JSON string or error message
 */
export const formatJson = (jsonString: string): { formatted: string; error?: string } => {
  try {
    const parsed = JSON.parse(jsonString);
    return { formatted: JSON.stringify(parsed, null, 2) };
  } catch (error) {
    if (error instanceof Error) {
      return { formatted: jsonString, error: `Cannot format invalid JSON: ${error.message}` };
    }
    return { formatted: jsonString, error: 'Cannot format invalid JSON' };
  }
};
