import { createTheme } from "@mui/material/styles";

export const theme = createTheme({
  palette: {
    primary: {
      main: "#1a73e8", // Primary color - blue from Circuit Flow Factory
    },
    secondary: {
      main: "#00a389", // Secondary color - teal from Circuit Flow Factory
    },
    background: {
      default: "#f8f9fa", // Page background
      paper: "#ffffff", // Card background
    },
    text: {
      primary: "#333333",
      secondary: "#555555",
    },
  },
  typography: {
    fontFamily: "Roboto, Arial, sans-serif",
    fontSize: 15,
    h4: {
      fontWeight: 600,
    },
    h6: {
      fontWeight: 500,
      fontSize: "1.1rem",
    },
    body1: {
      fontSize: "0.9375rem", // 15px
    },
    button: {
      textTransform: "none",
      fontWeight: 500,
    },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: "none", // Prevent all caps
          borderRadius: 8, // Rounded buttons
          fontWeight: 500,
          padding: "8px 16px",
        },
        contained: {
          boxShadow: "0 2px 8px rgba(26, 115, 232, 0.2)",
          "&:hover": {
            boxShadow: "0 4px 12px rgba(26, 115, 232, 0.3)",
          },
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        root: {
          padding: "14px 16px",
          fontSize: "0.9375rem",
        },
        head: {
          fontWeight: 600,
          backgroundColor: "#f8f9fa",
          color: "#555",
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: "0 4px 20px rgba(0, 0, 0, 0.05)",
        },
      },
    },
    MuiTablePagination: {
      styleOverrides: {
        root: {
          backgroundColor: "transparent",
          borderTop: "1px solid #e2e8f0",
        },
        toolbar: {
          backgroundColor: "#f8fafc",
          borderBottomLeftRadius: "8px",
          borderBottomRightRadius: "8px",
        },
        selectLabel: {
          fontSize: "13px",
          color: "#64748b",
        },
        displayedRows: {
          fontSize: "13px",
          color: "#64748b",
        },
        select: {
          fontSize: "13px",
        },
        actions: {
          "& .MuiIconButton-root": {
            color: "#64748b",
            padding: "4px",
          },
        },
      },
    },
  },
});
