import { SelectChangeEvent } from "@mui/material";
import { FieldConfig } from "../pages/FormCreator";
import {
  getProductDataBySerialNumber,
  formatToUtcDateTime,
} from "../utils/formHelper";

export const handlerList: string[] = [
  "handleSubmit",
  "handleChange",
  "handleBlur",
  "handleFocus",
];

export const handlers: Record<
  number,
  Record<
    string,
    (
      values: Record<string, any>,
      field: FieldConfig,
      event:
        | React.ChangeEvent<
            HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
          >
        | SelectChangeEvent<string | string[]>,
      setFieldValue: (field: string, value: any) => void,
      handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void,
      setModalOpen: React.Dispatch<React.SetStateAction<boolean>>,
      setFormErrors: React.Dispatch<any>,
      showSnackbar: (
        message: string,
        severity?: "error" | "success" | "warning" | "info" | undefined
      ) => void,
      formId: number
    ) => void
  >
> = {
  1: {
    handleSubmit: (values, field, event) => {
      console.log("Form 1 submit handler triggered", { values, field });
    },
    handleChange: (
      values,
      field,
      event,
      setFieldValue,
      handleFormConfigUpdate,
      setModalOpen
    ) => {
      const { checked, type } = event.target as any;
      if (type === "checkbox") {
        if (field.name === "wrong_component") {
          if (checked) {
            handleFormConfigUpdate({
              wrong_component_name: {
                readonly: false,
                required: true,
              },
            });
          } else {
            handleFormConfigUpdate({
              wrong_component_name: {
                readonly: true,
                wrong_component_name: "",
                required: false,
              },
            });
          }
        }
        if (field.name === "missing_component") {
          if (checked) {
            handleFormConfigUpdate({
              missing_component_name: { readonly: false, required: true },
            });
          } else {
            handleFormConfigUpdate({
              missing_component_name: {
                readonly: true,
                required: false,
                missing_component_name: "",
              },
            });
          }
        }
        if (field.name === "wrong_polarity") {
          if (checked) {
            handleFormConfigUpdate({
              wrong_polarity_component: { readonly: false, required: true },
            });
          } else {
            handleFormConfigUpdate({
              wrong_polarity_component: {
                readonly: true,
                required: false,
                wrong_polarity_component: "",
              },
            });
          }
        }
      }
    },
    handleBlur: async (
      values,
      field,
      event,
      setFieldValue,
      handleFormConfigUpdate,
      setModalOpen,
      setFormErrors,
      showSnackbar,
      formId
    ) => {
      if (
        values.serial_number.trim().toString().length > 15 &&
        !values["part_description"]
      ) {
        const { partNumber, po, productData } =
          await getProductDataBySerialNumber(
            values.serial_number,
            showSnackbar
          );
        if (productData) {
          if (productData?.past_events?.length) {
            if (
              productData.past_events.some(
                (data: any) => data.form_details?.id === formId
              )
            ) {
              setModalOpen(true);
            }
          }
          handleFormConfigUpdate({
            part_number: { isVisible: true, part_number: partNumber },
            po: { isVisible: true, po: po },
            part_description: {
              isVisible: true,
              part_description: productData?.description ?? "",
            },
            wrong_component_name: {
              options:
                productData?.components?.map((comp: any) => ({
                  value: comp?.component?.code,
                  label: comp?.component?.name,
                })) ?? [],
            },
            missing_component_name: {
              options:
                productData?.components?.map((comp: any) => ({
                  value: comp?.component?.code,
                  label: comp?.component?.name,
                })) ?? [],
            },
            wrong_polarity_component: {
              options:
                productData?.components?.map((comp: any) => ({
                  value: comp?.component?.code,
                  label: comp?.component?.name,
                })) ?? [],
            },
          });
        } else {
          handleFormConfigUpdate({
            serial_number: { serial_number: "" },
            part_number: { isVisible: false, part_number: "" },
            po: { isVisible: false, po: "" },
            part_description: {
              isVisible: false,
              part_description: "",
            },
          });
        }
      } else {
        handleFormConfigUpdate({
          part_number: { isVisible: false, part_number: "" },
          po: { isVisible: false, po: "" },
          part_description: {
            isVisible: false,
            part_description: "",
          },
        });
      }
    },

    handleFocus: (values, field) => {
      console.log("Form 2 focus handler triggered", { field });
    },

    handleClick: async (
      values,
      field,
      event,
      setFieldValue,
      handleFormConfigUpdate,
      setModalOpen,
      formId
    ) => {
      const { clickedState } = event as any;
      if (clickedState === "fail") {
        handleFormConfigUpdate({
          missing_component: { isVisible: true },
          form_status: {
            form_status: "fail",
          },
          is_corrected: { isVisible: true, is_corrected: "" },
          wrong_polarity: {
            isVisible: true,
          },
          wrong_component: { isVisible: true },
          missing_component_name: {
            isVisible: true,
          },
          wrong_polarity_component: {
            isVisible: true,
          },
          wrong_component_name: {
            isVisible: true,
          },
        });
      } else {
        handleFormConfigUpdate({
          missing_component: { isVisible: false, missing_component: "" },
          wrong_component: { isVisible: false, wrong_component: "" },
          wrong_polarity: {
            isVisible: false,
            wrong_polarity: "",
          },
          wrong_polarity_component: {
            isVisible: false,
            wrong_polarity_component: "",
            required: false,
            readonly: true,
          },
          missing_component_name: {
            isVisible: false,
            missing_component_name: "",
            required: false,
            readonly: true,
          },
          wrong_component_name: {
            isVisible: false,
            wrong_component_name: "",
            required: false,
            readonly: true,
          },
          form_status: {
            form_status: "",
          },
          is_corrected: { isVisible: false, is_corrected: "" },
        });
      }
    },
  },

  2: {
    handleSubmit: (values, field, _event) => {
      console.log("Form 3 submit handler triggered", { values, field });
    },
    handleChange: (values, field, event, setFieldValue) => {
      console.log("Form 3 change handler triggered", {
        field,
        value: event.target.value,
      });
    },
    handleBlur: async (
      values,
      field,
      event,
      setFieldValue,
      handleFormConfigUpdate,
      setModalOpen,
      setFormErrors,
      showSnackbar,
      formId
    ) => {
      console.log({ field, values, event });
      // HE317171-35.12#D92410529460638
      if (
        values.serial_number.toString().length > 15 &&
        !values["part_description"]
      ) {
        const { partNumber, po, productData } =
          await getProductDataBySerialNumber(
            values.serial_number,
            showSnackbar
          );
        if (productData) {
          if (productData?.past_events?.length) {
            if (
              productData.past_events.some(
                (data: any) => data.form_details?.id === formId
              )
            ) {
              setModalOpen(true);
            }
          }
          handleFormConfigUpdate({
            part_number: { isVisible: true, part_number: partNumber },
            po: { isVisible: true, po: po },
            part_description: {
              isVisible: true,
              part_description: productData?.description ?? "",
            },
            component_location: {
              options:
                productData?.components?.map((comp: any) => ({
                  value: comp?.component?.code,
                  label: comp?.component?.name,
                })) ?? [],
              readonly: false,
              required: true,
            },
          });
        } else {
          handleFormConfigUpdate({
            serial_number: { serial_number: "" },
            part_number: { isVisible: false, part_number: "" },
            po: { isVisible: false, po: "" },
            part_description: {
              isVisible: false,
              part_description: "",
            },
            component_location: {
              readonly: true,
              required: false,
            },
          });
        }
      } else {
        handleFormConfigUpdate({
          part_number: { isVisible: false, part_number: "" },
          po: { isVisible: false, po: "" },
          part_description: {
            isVisible: false,
            part_description: "",
          },
        });
      }
    },

    handleFocus: (values, field, _event) => {
      console.log("Form 3 focus handler triggered", { field });
    },

    handleClick: async (
      values,
      field,
      event,
      setFieldValue,
      handleFormConfigUpdate,
      setModalOpen
    ) => {
      const { clickedState } = event as any;
      if (clickedState === "fail") {
        handleFormConfigUpdate({
          component_location: {
            isVisible: true,
            required: true,
          },
          fault_cause: {
            isVisible: true,
            required: true,
          },
          form_status: {
            form_status: "fail",
          },
        });
      } else {
        handleFormConfigUpdate({
          component_location: {
            isVisible: false,
            required: false,
            component_location: "",
          },
          fault_cause: {
            isVisible: false,
            required: false,
            fault_cause: "",
          },
          form_status: {
            form_status: "",
          },
        });
      }
    },
  },
  3: {
    handleSubmit: (values, field, _event) => {
      console.log("Form 3 submit handler triggered", { values, field });
    },
    handleChange: (values, field, event, setFieldValue) => {
      console.log("Form 3 change handler triggered", {
        field,
        value: event.target.value,
      });
    },
    handleBlur: async (
      values,
      field,
      event,
      setFieldValue,
      handleFormConfigUpdate,
      setModalOpen,
      setFormErrors,
      showSnackbar,
      formId
    ) => {
      console.log({ field, values, event });
      // HE317171-35.12#D92410529460638
      if (
        values.serial_number.toString().length > 15 &&
        !values["part_description"]
      ) {
        const { partNumber, po, productData } =
          await getProductDataBySerialNumber(
            values.serial_number,
            showSnackbar
          );

        if (productData) {
          if (productData?.past_events?.length) {
            if (
              productData.past_events.some(
                (data: any) => data.form_details?.id === formId
              )
            ) {
              setModalOpen(true);
            }
          }
          handleFormConfigUpdate({
            part_number: { isVisible: true, part_number: partNumber },
            po: { isVisible: true, po: po },
            part_description: {
              isVisible: true,
              part_description: productData?.description ?? "",
            },
          });
        } else {
          handleFormConfigUpdate({
            serial_number: { serial_number: "" },
            part_number: { isVisible: false, part_number: "" },
            po: { isVisible: false, po: "" },
            part_description: {
              isVisible: false,
              part_description: "",
            },
          });
        }
      } else {
        handleFormConfigUpdate({
          part_number: { isVisible: false, part_number: "" },
          po: { isVisible: false, po: "" },
          part_description: {
            isVisible: false,
            part_description: "",
          },
        });
      }
    },
    handleFocus: (values, field, _event) => {
      console.log("Form 3 focus handler triggered", { field });
    },

    handleClick: async (
      values,
      field,
      event,
      setFieldValue,
      handleFormConfigUpdate,
      setModalOpen
    ) => {
      const { clickedState } = event as any;
      if (clickedState === "fail") {
        handleFormConfigUpdate({
          fault_type: {
            isVisible: true,
            required: true,
          },
          form_status: {
            form_status: "fail",
          },
        });
      } else {
        handleFormConfigUpdate({
          fault_type: {
            isVisible: false,
            required: false,
            fault_type: "",
          },
          form_status: {
            form_status: "",
          },
        });
      }
    },
  },
  4: {
    handleSubmit: (values, field, _event) => {
      console.log("Form 3 submit handler triggered", { values, field });
    },
    handleChange: (values, field, event, setFieldValue) => {
      console.log("Form 3 change handler triggered", {
        field,
        value: event.target.value,
      });
    },
    handleBlur: async (
      values,
      field,
      event,
      setFieldValue,
      handleFormConfigUpdate,
      setModalOpen,
      setFormErrors,
      showSnackbar,
      formId
    ) => {
      if (
        values.serial_number.toString().length > 15 &&
        !values["part_description"]
      ) {
        // HE317171-35.12#D92410529460638
        const { partNumber, po, productData } =
          await getProductDataBySerialNumber(
            values.serial_number,
            showSnackbar
          );

        if (productData?.past_events?.length) {
          if (
            productData.past_events.some(
              (data: any) => data.form_details?.id === formId
            )
          ) {
            setModalOpen(true);
          }

          const updateObject: Record<string, any> = {
            part_number: { isVisible: true, part_number: partNumber },
            po: { isVisible: true, po: po },
            part_description: {
              isVisible: true,
              part_description: productData?.description ?? "",
            },
            failed_timestamp: {
              isVisible: true,
              failed_timestamp: productData?.last_failed_event?.timestamp
                ? formatToUtcDateTime(productData?.last_failed_event?.timestamp)
                : "",
            },
            failed_by: {
              isVisible: true,
              failed_by:
                productData?.last_failed_event?.event_data?.sign_in_user ?? "",
            },
            failed_stage: {
              failed_stage:
                productData?.last_failed_event?.form_details?.name ?? "",
              isVisible: true,
            },
            component_location: {
              options:
                productData?.components?.map((comp: any) => ({
                  value: comp?.component?.code,
                  label: comp?.component?.name,
                })) ?? [],
              readonly: false,
              required: true,
            },
          };

          if (productData?.last_failed_event?.form_details?.id === 3) {
            const fault_type =
              productData?.last_failed_event?.event_data?.fault_type ?? "";
            const fault_cause =
              productData?.last_failed_event?.event_data?.fault_cause ?? "";

            updateObject["fault_reported"] = {
              fault_reported: fault_cause || fault_type,
              isVisible: true,
            };
            updateObject["fault_cause"] = {
              fault_cause: fault_cause,
              readonly: false,
              required: fault_cause ? true : false,
            };
            updateObject["fault_type"] = {
              fault_type: fault_type,
              readonly: false,
              required: fault_type ? true : false,
            };
          }

          if (productData?.last_failed_event?.form_details?.id === 2) {
            const fault_data =
              productData?.last_failed_event?.event_data?.fault_cause ?? "";
            const fault_cause = Array.isArray(fault_data)
              ? fault_data[0]
              : fault_data;

            updateObject["fault_reported"] = {
              fault_reported: fault_cause,
              isVisible: true,
              readonly: false,
            };

            updateObject["fault_cause"] = {
              fault_cause: fault_cause,
              readonly: false,
              required: true,
            };

            updateObject["fault_type"] = {
              fault_type: "",
              readonly: true,
              required: false,
            };
          }

          handleFormConfigUpdate(updateObject);
        } else {
          handleFormConfigUpdate({
            part_number: { isVisible: false, part_number: "" },
            po: { isVisible: false, po: "" },
            part_description: {
              isVisible: false,
              part_description: "",
            },
            fault_reported: {
              fault_reported: "",
              isVisible: false,
            },
            failed_stage: {
              failed_stage: "",
              isVisible: false,
            },
            fault_cause: {
              fault_cause: "",
              readonly: true,
              required: true,
            },
            fault_type: {
              fault_type: "",
              readonly: true,
              required: true,
            },
            issue_type: {
              issue_type: "",
            },
            component_location: {
              component_location: "",
              required: true,
            },
            failed_timestamp: {
              isVisible: false,
              failed_timestamp: "",
            },
            failed_by: {
              isVisible: false,
              failed_by: "",
            },
          });
        }
      } else {
        handleFormConfigUpdate({
          part_number: { isVisible: false, part_number: "" },
          po: { isVisible: false, po: "" },
          part_description: {
            isVisible: false,
            part_description: "",
          },
          fault_reported: {
            fault_reported: "",
            isVisible: false,
          },
          failed_stage: {
            failed_stage: "",
            isVisible: false,
          },
          fault_cause: {
            fault_cause: "",
            readonly: true,
            required: true,
          },
          fault_type: {
            fault_type: "",
            readonly: true,
            required: true,
          },
          issue_type: {
            issue_type: "",
          },
          component_location: {
            component_location: "",
            required: true,
          },
        });
      }
    },
    handleFocus: (values, field, _event) => {
      console.log("Form 3 focus handler triggered", { field });
    },

    handleClick: async (
      values,
      field,
      event,
      setFieldValue,
      handleFormConfigUpdate,
      handleSubmitButtonVisibilty
    ) => {
      console.log({ values });
      if (values.form_status === "fail") {
        handleSubmitButtonVisibilty(true);
      } else {
        handleSubmitButtonVisibilty(false);
      }
    },
  },
  5: {
    handleSubmit: (values, field, _event) => {
      console.log("Form 3 submit handler triggered", { values, field });
    },
    handleChange: (values, field, event, setFieldValue) => {
      console.log("Form 3 change handler triggered", {
        field,
        value: event.target.value,
      });
    },
    handleBlur: async (
      values,
      field,
      event,
      setFieldValue,
      handleFormConfigUpdate,
      setModalOpen,
      setFormErrors,
      showSnackbar,
      formId
    ) => {
      console.log({ field, values, event });
      // HE317171-35.12#D92410529460638
      if (
        values.serial_number.toString().length > 15 &&
        !values["part_description"]
      ) {
        const { partNumber, po, productData } =
          await getProductDataBySerialNumber(
            values.serial_number,
            showSnackbar
          );

        if (productData) {
          if (productData?.past_events?.length) {
            if (
              productData.past_events.some(
                (data: any) => data.form_details?.id === formId
              )
            ) {
              setModalOpen(true);
            }
          }

          const formInfo = productData?.past_events?.find(
            (data: any) => data.form_details?.id === 4
          );

          const { event_data } = formInfo;

          handleFormConfigUpdate({
            part_number: { isVisible: true, part_number: partNumber },
            po: { isVisible: true, po: po },
            part_description: {
              isVisible: true,
              part_description: productData?.description ?? "",
            },
            failed_stage: {
              isVisible: true,
              failed_stage: event_data?.failed_stage,
            },
            fault_reported: {
              isVisible: true,
              fault_reported: event_data?.fault_reported ?? "",
            },
            failed_timestamp: {
              isVisible: true,
              failed_timestamp: event_data?.failed_timestamp ?? "",
            },
            failed_by: {
              isVisible: true,
              failed_by: event_data?.failed_by ?? "",
            },
            fault_type: {
              isVisible: true,
              fault_type: event_data?.fault_details
                ?.map((o: any) => o.fault_type)
                ?.join(", "),
            },
            fault_cause: {
              isVisible: true,
              fault_cause: event_data?.fault_details
                ?.map((o: any) => o.fault_cause)
                ?.join(", "),
            },
            issue_type: {
              isVisible: true,
              issue_type: event_data?.fault_details
                ?.map((o: any) => o.issue_type)
                ?.join(", "),
            },
            component_location: {
              isVisible: true,
              component_location: event_data?.fault_details
                ?.map((o: any) => o?.component_location?.join(","))
                .join(", "),
            },
            repair_action: {
              isVisible: true,
              repair_action: event_data?.fault_details
                ?.map((o: any) => o.repair_action)
                ?.join(", "),
            },
            pcb_type: { isVisible: true, pcb_type: "" },
            defect_generation_area: {
              isVisible: true,
              defect_generation_area: "",
            },
            detection_skippage: { isVisible: true, detection_skippage: "" },
            operator_responsible: {
              isVisible: true,
              operator_responsible: "",
            },
            operator_occurance: {
              isVisible: true,
              operator_occurance: "",
            },
            remarks: { isVisible: true, remarks: "" },
          });
        } else {
          handleFormConfigUpdate({
            serial_number: { serial_number: "" },
            part_number: { isVisible: false, part_number: "" },
            po: { isVisible: false, po: "" },
            part_description: {
              isVisible: false,
              part_description: "",
            },
            failed_stage: { isVisible: false, failed_stage: "" },
            fault_reported: { isVisible: false, fault_reported: "" },
            failed_timestamp: { isVisible: false, failed_timestamp: "" },
            failed_by: { isVisible: false, failed_by: "" },
            fault_type: { isVisible: false, fault_type: "" },
            fault_cause: { isVisible: false, fault_cause: "" },
            issue_type: { isVisible: false, issue_type: "" },
            component_location: {
              isVisible: false,
              component_location: "",
            },
            repair_action: { isVisible: false, repair_action: "" },
            pcb_type: { isVisible: false, pcb_type: "" },
            defect_generation_area: {
              isVisible: false,
              defect_generation_area: "",
            },
            detection_skippage: { isVisible: false, detection_skippage: "" },
            operator_responsible: {
              isVisible: false,
              operator_responsible: "",
            },
            remarks: { isVisible: false, remarks: "" },
            operator_occurance: {
              isVisible: false,
              operator_occurance: "",
            },
          });
        }
      } else {
        handleFormConfigUpdate({
          part_number: { isVisible: false, part_number: "" },
          po: { isVisible: false, po: "" },
          part_description: {
            isVisible: false,
            part_description: "",
          },
          failed_stage: { isVisible: false, failed_stage: "" },
          fault_reported: { isVisible: false, fault_reported: "" },
          failed_timestamp: { isVisible: false, failed_timestamp: "" },
          failed_by: { isVisible: false, failed_by: "" },
          fault_type: { isVisible: false, fault_type: "" },
          fault_cause: { isVisible: false, fault_cause: "" },
          issue_type: { isVisible: false, issue_type: "" },
          component_location: {
            isVisible: false,
            component_location: "",
          },
          repair_action: { isVisible: false, repair_action: "" },
          pcb_type: { isVisible: false, pcb_type: "" },
          defect_generation_area: {
            isVisible: false,
            defect_generation_area: "",
          },
          detection_skippage: { isVisible: false, detection_skippage: "" },
          operator_responsible: {
            isVisible: false,
            operator_responsible: "",
          },
          remarks: { isVisible: false, remarks: "" },
          operator_occurance: {
            isVisible: false,
            operator_occurance: "",
          },
        });
      }
    },
    handleFocus: (values, field, _event) => {
      console.log("Form 3 focus handler triggered", { field });
    },

    handleClick: async (
      values,
      field,
      event,
      setFieldValue,
      handleFormConfigUpdate,
      setModalOpen
    ) => {},
  },
  6: {
    handleSubmit: (values, field, _event) => {
      console.log("Form 3 submit handler triggered", { values, field });
    },
    handleChange: (values, field, event, setFieldValue) => {
      console.log("Form 3 change handler triggered", {
        field,
        value: event.target.value,
      });
    },
    handleBlur: async (
      values,
      field,
      event,
      setFieldValue,
      handleFormConfigUpdate,
      setModalOpen,
      setFormErrors,
      showSnackbar,
      formId
    ) => {
      console.log({ field, values, event });
      // HE317171-35.12#D92410529460638
      if (values.gr_number.trim().length > 0) {
        // const { partNumber, po, productData } =
        //   await getProductDataBySerialNumber(
        //     values.serial_number,
        //     showSnackbar
        //   );

        if (formId === 6) {
          // if (productData?.past_events?.length) {
          //   if (
          //     productData.past_events.some(
          //       (data: any) => data.form_details?.id === formId
          //     )
          //   ) {
          //     setModalOpen(true);
          //   }
          // }

          // const formInfo = productData?.past_events?.find(
          //   (data: any) => data.form_details?.id === 4
          // );

          // const { event_data } = formInfo;

          handleFormConfigUpdate({
            box_number: { isVisible: true, options: [] },
            make: {
              isVisible: true,
              make: "test",
            },
            expiry_date: {
              isVisible: true,
              expiry_date: "12-12-2025",
            },
            refrigerator_in_timestamp: {
              isVisible: true,
              refrigerator_in_timestamp: "12-12-2025",
            },
          });
        } else {
          handleFormConfigUpdate({
            box_number: { isVisible: false, options: [] },
            make: {
              isVisible: false,
              make: "test",
            },
            expiry_date: {
              isVisible: false,
              expiry_date: "",
            },
            refrigerator_in_timestamp: {
              isVisible: false,
              refrigerator_in_timestamp: "",
            },
          });
        }
      } else {
        handleFormConfigUpdate({
          box_number: { isVisible: false, options: [] },
          make: {
            isVisible: false,
            make: "test",
          },
          expiry_date: {
            isVisible: false,
            expiry_date: "",
          },
          refrigerator_in_timestamp: {
            isVisible: false,
            refrigerator_in_timestamp: "",
          },
        });
      }
    },
    handleFocus: (values, field, _event) => {
      console.log("Form 3 focus handler triggered", { field });
    },

    handleClick: async (
      values,
      field,
      event,
      setFieldValue,
      handleFormConfigUpdate,
      setModalOpen
    ) => {},
  },
  7: {
    handleSubmit: (values, field, _event) => {
      console.log("Form 3 submit handler triggered", { values, field });
    },
    handleChange: (values, field, event, setFieldValue) => {
      console.log("Form 3 change handler triggered", {
        field,
        value: event.target.value,
      });
    },
    handleBlur: async (
      values,
      field,
      event,
      setFieldValue,
      handleFormConfigUpdate,
      setModalOpen,
      setFormErrors,
      showSnackbar,
      formId
    ) => {
      console.log({ field, values, event });
      // HE317171-35.12#D92410529460638
      if (values.gr_number.trim().length > 0) {
        // const { partNumber, po, productData } =
        //   await getProductDataBySerialNumber(
        //     values.serial_number,
        //     showSnackbar
        //   );

        if (formId === 6) {
          // if (productData?.past_events?.length) {
          //   if (
          //     productData.past_events.some(
          //       (data: any) => data.form_details?.id === formId
          //     )
          //   ) {
          //     setModalOpen(true);
          //   }
          // }

          // const formInfo = productData?.past_events?.find(
          //   (data: any) => data.form_details?.id === 4
          // );

          // const { event_data } = formInfo;

          handleFormConfigUpdate({
            box_number: { isVisible: true, options: [] },
            make: {
              isVisible: true,
              make: "test",
            },
            expiry_date: {
              isVisible: true,
              expiry_date: "12-12-2025",
            },
            refrigerator_in_timestamp: {
              isVisible: true,
              refrigerator_in_timestamp: "12-12-2025",
            },
            refrigerator_out_timestamp: {
              isVisible: true,
              refrigerator_out_timestamp: "12-12-2025",
            },
          });
        } else {
          handleFormConfigUpdate({
            box_number: { isVisible: false, options: [] },
            make: {
              isVisible: false,
              make: "test",
            },
            expiry_date: {
              isVisible: false,
              expiry_date: "",
            },
            refrigerator_in_timestamp: {
              isVisible: false,
              refrigerator_in_timestamp: "",
            },
            refrigerator_out_timestamp: {
              isVisible: false,
              refrigerator_out_timestamp: "",
            },
          });
        }
      } else {
        handleFormConfigUpdate({
          box_number: { isVisible: false, options: [] },
          make: {
            isVisible: false,
            make: "test",
          },
          expiry_date: {
            isVisible: false,
            expiry_date: "",
          },
          refrigerator_in_timestamp: {
            isVisible: false,
            refrigerator_in_timestamp: "",
          },
          refrigerator_out_timestamp: {
            isVisible: false,
            refrigerator_out_timestamp: "",
          },
        });
      }
    },
    handleFocus: (values, field, _event) => {
      console.log("Form 3 focus handler triggered", { field });
    },

    handleClick: async (
      values,
      field,
      event,
      setFieldValue,
      handleFormConfigUpdate,
      setModalOpen
    ) => {},
  },
  8: {
    handleSubmit: (values, field, _event) => {
      console.log("Form 3 submit handler triggered", { values, field });
    },
    handleChange: (values, field, event, setFieldValue) => {
      console.log("Form 3 change handler triggered", {
        field,
        value: event.target.value,
      });
    },
    handleBlur: async (
      values,
      field,
      event,
      setFieldValue,
      handleFormConfigUpdate,
      setModalOpen,
      setFormErrors,
      showSnackbar,
      formId
    ) => {
      console.log({ field, values, event });
      // HE317171-35.12#D92410529460638
      if (values.gr_number.trim().length > 0) {
        // const { partNumber, po, productData } =
        //   await getProductDataBySerialNumber(
        //     values.serial_number,
        //     showSnackbar
        //   );

        if (formId === 6) {
          // if (productData?.past_events?.length) {
          //   if (
          //     productData.past_events.some(
          //       (data: any) => data.form_details?.id === formId
          //     )
          //   ) {
          //     setModalOpen(true);
          //   }
          // }

          // const formInfo = productData?.past_events?.find(
          //   (data: any) => data.form_details?.id === 4
          // );

          // const { event_data } = formInfo;

          handleFormConfigUpdate({
            box_number: { isVisible: true, options: [] },
            make: {
              isVisible: true,
              make: "test",
            },
            expiry_date: {
              isVisible: true,
              expiry_date: "12-12-2025",
            },
            refrigerator_in_timestamp: {
              isVisible: true,
              refrigerator_in_timestamp: "12-12-2025",
            },
            refrigerator_out_timestamp: {
              isVisible: true,
              refrigerator_out_timestamp: "12-12-2025",
            },
          });
        } else {
          handleFormConfigUpdate({
            box_number: { isVisible: false, options: [] },
            make: {
              isVisible: false,
              make: "test",
            },
            expiry_date: {
              isVisible: false,
              expiry_date: "",
            },
            refrigerator_in_timestamp: {
              isVisible: false,
              refrigerator_in_timestamp: "",
            },
            refrigerator_out_timestamp: {
              isVisible: false,
              refrigerator_out_timestamp: "",
            },
          });
        }
      } else {
        handleFormConfigUpdate({
          box_number: { isVisible: false, options: [] },
          make: {
            isVisible: false,
            make: "test",
          },
          expiry_date: {
            isVisible: false,
            expiry_date: "",
          },
          refrigerator_in_timestamp: {
            isVisible: false,
            refrigerator_in_timestamp: "",
          },
          refrigerator_out_timestamp: {
            isVisible: false,
            refrigerator_out_timestamp: "",
          },
        });
      }
    },
    handleFocus: (values, field, _event) => {
      console.log("Form 3 focus handler triggered", { field });
    },

    handleClick: async (
      values,
      field,
      event,
      setFieldValue,
      handleFormConfigUpdate,
      setModalOpen
    ) => {},
  },
};
