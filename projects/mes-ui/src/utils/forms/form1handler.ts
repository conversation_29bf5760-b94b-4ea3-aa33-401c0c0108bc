import { FieldConfig } from "../../pages/FormCreator";
import { getProductDataBySerialNumber } from "../formHelper";
import { IFormHandler } from "./baseFormHandle";

export class Form1Handler implements IFormHandler {
  constructor(private formCode: string) {}

  handleChange(
    values: Record<string, any>,
    field: FieldConfig,
    event: any,
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void,
    setModalOpen: React.Dispatch<
      React.SetStateAction<{
        title: string;
        isOpen: boolean;
      }>
    >,
    setFormErrors: React.Dispatch<any>,
    showSnackbar: (
      message: string,
      severity?: "error" | "success" | "warning" | "info" | undefined
    ) => void,
    formId: number
  ) {
    const { checked, type } = event.target as any;
    if (type === "checkbox") {
      if (field.name === "wrong_component") {
        if (checked) {
          handleFormConfigUpdate({
            wrong_component_name: {
              readonly: false,
              required: true,
            },
          });
        } else {
          handleFormConfigUpdate({
            wrong_component_name: {
              readonly: true,
              wrong_component_name: "",
              required: false,
            },
          });
        }
      }
      if (field.name === "missing_component") {
        if (checked) {
          handleFormConfigUpdate({
            missing_component_name: { readonly: false, required: true },
          });
        } else {
          handleFormConfigUpdate({
            missing_component_name: {
              readonly: true,
              required: false,
              missing_component_name: "",
            },
          });
        }
      }
      if (field.name === "wrong_polarity") {
        if (checked) {
          handleFormConfigUpdate({
            wrong_polarity_component: { readonly: false, required: true },
          });
        } else {
          handleFormConfigUpdate({
            wrong_polarity_component: {
              readonly: true,
              required: false,
              wrong_polarity_component: "",
            },
          });
        }
      }
    }
  }

  async handleBlur(
    values: Record<string, any>,
    field: FieldConfig,
    event: any,
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void,
    setModalOpen: React.Dispatch<
      React.SetStateAction<{
        title: string;
        isOpen: boolean;
      }>
    >,
    setFormErrors: React.Dispatch<any>,
    showSnackbar: (
      message: string,
      severity?: "error" | "success" | "warning" | "info" | undefined
    ) => void,
    formId: number
  ) {
    if (
      values.serial_number.trim().toString().length > 15 &&
      !values["part_description"]
    ) {
      const { partNumber, po, productData } =
        await getProductDataBySerialNumber(values.serial_number, showSnackbar);
      if (productData) {
        if (productData?.past_events?.length) {
          if (
            productData.past_events.some(
              (data: any) => data.form_details?.id === formId
            )
          ) {
            setModalOpen((prev) => ({ ...prev, isOpen: true }));
          }
        }
        handleFormConfigUpdate({
          part_number: { isVisible: true, part_number: partNumber },
          po: { isVisible: true, po: po },
          part_description: {
            isVisible: true,
            part_description: productData?.description ?? "",
          },
          wrong_component_name: {
            options:
              productData?.components?.map((comp: any) => ({
                value: comp?.component?.code,
                label: comp?.component?.name,
              })) ?? [],
          },
          missing_component_name: {
            options:
              productData?.components?.map((comp: any) => ({
                value: comp?.component?.code,
                label: comp?.component?.name,
              })) ?? [],
          },
          wrong_polarity_component: {
            options:
              productData?.components?.map((comp: any) => ({
                value: comp?.component?.code,
                label: comp?.component?.name,
              })) ?? [],
          },
        });
      } else {
        handleFormConfigUpdate({
          serial_number: { serial_number: "" },
          part_number: { isVisible: false, part_number: "" },
          po: { isVisible: false, po: "" },
          part_description: {
            isVisible: false,
            part_description: "",
          },
        });
      }
    } else {
      handleFormConfigUpdate({
        part_number: { isVisible: false, part_number: "" },
        po: { isVisible: false, po: "" },
        part_description: {
          isVisible: false,
          part_description: "",
        },
      });
    }
  }

  async handleClick(
    values: Record<string, any>,
    field: FieldConfig,
    event: any,
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void,
    setModalOpen: React.Dispatch<
      React.SetStateAction<{
        title: string;
        isOpen: boolean;
      }>
    >,
    setFormErrors: React.Dispatch<any>,
    showSnackbar: (
      message: string,
      severity?: "error" | "success" | "warning" | "info" | undefined
    ) => void,
    formId: number
  ) {
    const { clickedState } = event as any;
    if (clickedState === "fail") {
      handleFormConfigUpdate({
        missing_component: { isVisible: true },
        form_status: {
          form_status: "fail",
        },
        is_corrected: { isVisible: true, is_corrected: "" },
        wrong_polarity: {
          isVisible: true,
        },
        wrong_component: { isVisible: true },
        missing_component_name: {
          isVisible: true,
        },
        wrong_polarity_component: {
          isVisible: true,
        },
        wrong_component_name: {
          isVisible: true,
        },
      });
    } else {
      handleFormConfigUpdate({
        missing_component: { isVisible: false, missing_component: "" },
        wrong_component: { isVisible: false, wrong_component: "" },
        wrong_polarity: {
          isVisible: false,
          wrong_polarity: "",
        },
        wrong_polarity_component: {
          isVisible: false,
          wrong_polarity_component: "",
          required: false,
          readonly: true,
        },
        missing_component_name: {
          isVisible: false,
          missing_component_name: "",
          required: false,
          readonly: true,
        },
        wrong_component_name: {
          isVisible: false,
          wrong_component_name: "",
          required: false,
          readonly: true,
        },
        form_status: {
          form_status: "",
        },
        is_corrected: { isVisible: false, is_corrected: "" },
      });
    }
  }

  handleSubmit(values: Record<string, any>) {}

  handleFocus(
    values: Record<string, any>,
    field: FieldConfig,
    event: React.FocusEvent<any>,
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void,
    setModalOpen: React.Dispatch<
      React.SetStateAction<{
        title: string;
        isOpen: boolean;
      }>
    >,
    setFormErrors: React.Dispatch<any>,
    showSnackbar: (
      message: string,
      severity?: "error" | "success" | "warning" | "info" | undefined
    ) => void,
    formId: number
  ) {}

  onModalCancel(fieldConfigUpdates: Record<string, any>) {}
}
