import { FieldConfig } from "../../pages/FormCreator";
import { fetchEventByFilterCrtieria, formatToUtcDateTime } from "../formHelper";
import { IFormHandler } from "./baseFormHandle";

export class Form11Handler implements <PERSON>ormHandler {
  constructor(private formCode: string) {}

  handleSubmit(values: Record<string, any>) {
    console.log(`Form ${this.formCode} submit triggered`, { values });
  }

  handleChange(
    values: Record<string, any>,
    field: FieldConfig,
    event: React.ChangeEvent<any>,
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void,
    setModalOpen: React.Dispatch<
      React.SetStateAction<{
        title: string;
        isOpen: boolean;
      }>
    >,
    setFormErrors: React.Dispatch<any>,
    showSnackbar: (
      message: string,
      severity?: "error" | "success" | "warning" | "info" | undefined
    ) => void,
    formId: number
  ) {
    const { name, value } = event.target;
    if (
      name === "stencil_part_number" &&
      value &&
      value !== values?.stencil_part_number
    ) {
      handleFormConfigUpdate({
        stencil_in_timestamp: {
          isVisible: false,
          stencil_in_timestamp: "",
        },
        stencil_washing_status: {
          isVisible: false,
          stencil_washing_status: "",
        },
        material_present: {
          isVisible: false,
          material_present: "",
        },
      });
    }
  }

  async handleBlur(
    values: Record<string, any>,
    field: FieldConfig,
    event: React.FocusEvent<any>,
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void,
    setModalOpen: React.Dispatch<
      React.SetStateAction<{
        title: string;
        isOpen: boolean;
      }>
    >,
    setFormErrors: React.Dispatch<any>,
    showSnackbar: (
      message: string,
      severity?: "error" | "success" | "warning" | "info" | undefined
    ) => void,
    formId: number
  ) {
    console.log(`Form ${this.formCode} handleBlur triggered`, { values });
    if (values.stencil_part_number.trim().length > 0) {
      const defaultBoardNumber = 1;

      const [res, isExist] = await Promise.all([
        fetchEventByFilterCrtieria({
          serial_number: `${values.stencil_part_number}#${defaultBoardNumber}`,
        }),
        fetchEventByFilterCrtieria({
          form: formId,
          serial_number: `${values.stencil_part_number}#${defaultBoardNumber}`,
        }),
      ]);

      if (res?.results?.data?.length) {
        if (isExist?.results?.data?.length) {
          setModalOpen({
            title: `Already exist Stencil no (${values?.stencil_part_number}). Do you wish to continue?`,
            isOpen: true,
          });
        }
        const resData = res.results.data[0];
        const { event_data } = resData;
        handleFormConfigUpdate({
          stencil_in_timestamp: {
            isVisible: true,
            stencil_in_timestamp: formatToUtcDateTime(resData.timestamp),
          },
          stencil_washing_status: {
            isVisible: true,
            stencil_washing_status: event_data?.stencil_washing_status ?? "",
          },
          material_present: {
            isVisible: true,
            material_present: event_data?.material_present ?? "",
          },
        });
      } else {
        showSnackbar("No events exist for input serial number", "error");
        handleFormConfigUpdate({
          stencil_in_timestamp: {
            isVisible: false,
            stencil_in_timestamp: "",
          },
          stencil_washing_status: {
            isVisible: false,
            stencil_washing_status: "",
          },
          material_present: {
            isVisible: false,
            material_present: "",
          },
        });
      }
    } else {
      handleFormConfigUpdate({
        stencil_in_timestamp: {
          isVisible: false,
          stencil_in_timestamp: "",
        },
        stencil_washing_status: {
          isVisible: false,
          stencil_washing_status: "",
        },
        material_present: {
          isVisible: false,
          material_present: "",
        },
      });
    }
  }

  handleFocus(
    values: Record<string, any>,
    field: FieldConfig,
    event: React.FocusEvent<any>,
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void,
    setModalOpen: React.Dispatch<
      React.SetStateAction<{
        title: string;
        isOpen: boolean;
      }>
    >,
    setFormErrors: React.Dispatch<any>,
    showSnackbar: (
      message: string,
      severity?: "error" | "success" | "warning" | "info" | undefined
    ) => void,
    formId: number
  ) {
    console.log(`Form ${this.formCode} handleFocus triggered`, { field });
  }

  handleClick(
    values: Record<string, any>,
    field: FieldConfig,
    event: React.MouseEvent<any>,
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void,
    setModalOpen: React.Dispatch<
      React.SetStateAction<{
        title: string;
        isOpen: boolean;
      }>
    >,
    setFormErrors: React.Dispatch<any>,
    showSnackbar: (
      message: string,
      severity?: "error" | "success" | "warning" | "info" | undefined
    ) => void,
    formId: number
  ) {
    console.log(`Form ${this.formCode} handleClick triggered`);
  }

  onModalClose(
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void
  ) {
    handleFormConfigUpdate({
      stencil_in_timestamp: {
        isVisible: false,
        stencil_in_timestamp: "",
      },
      stencil_part_number: {
        stencil_part_number: "",
      },
      stencil_washing_status: {
        isVisible: false,
        stencil_washing_status: "",
      },
      material_present: {
        isVisible: false,
        material_present: "",
      },
    });
  }
}
