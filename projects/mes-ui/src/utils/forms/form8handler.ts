import { FieldConfig } from "../../pages/FormCreator";
import {
  fetchEventByFilterCrtieria,
  formatToUtcDateTime,
  getProductDataBySerialNumber,
} from "../formHelper";
import { <PERSON>orm<PERSON>andler } from "./baseFormHandle";

export class Form<PERSON><PERSON><PERSON>ler implements <PERSON>ormHandler {
  constructor(private formCode: string) {}

  handleSubmit(values: Record<string, any>) {
    console.log(`Form ${this.formCode} submit triggered`, { values });
  }

  async handleChange(
    values: Record<string, any>,
    field: FieldConfig,
    event: React.ChangeEvent<any>,
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void,
    setModalOpen: React.Dispatch<
      React.SetStateAction<{
        title: string;
        isOpen: boolean;
      }>
    >,
    setFormErrors: React.Dispatch<any>,
    showSnackbar: (
      message: string,
      severity?: "error" | "success" | "warning" | "info" | undefined
    ) => void,
    formId: number
  ) {
    const { value, name } = event.target;

    if (
      value &&
      values?.gr_number &&
      value !== values.box_number &&
      name === "box_number"
    ) {
      const [box, res] = await Promise.all([
        fetchEventByFilterCrtieria({
          form_code: "solder_paste_out",
          serial_number: `${values.gr_number}#${value}`,
        }),
        fetchEventByFilterCrtieria({
          form: formId,
          serial_number: `${values.gr_number}#${value}`,
        }),
      ]);

      if (box?.results?.data?.length) {
        const resData = box.results.data[0];
        const { event_data } = resData;

        if (res?.results?.data?.length) {
          setModalOpen({
            title: `Already exist GR.no(${values?.gr_number}) and Box no(${value}). Do you wish to continue?`,
            isOpen: true,
          });
        }

        handleFormConfigUpdate({
          make: {
            isVisible: true,
            make: event_data?.make ?? "",
          },
          expiry_date: {
            isVisible: true,
            expiry_date: event_data?.expiry_date?.split("T")[0],
          },
          refrigerator_in_timestamp: {
            isVisible: true,
            refrigerator_in_timestamp: event_data?.refrigerator_in_timestamp,
          },
          refrigerator_out_timestamp: {
            isVisible: true,
            refrigerator_out_timestamp: formatToUtcDateTime(resData.timestamp),
          },
        });
      } else {
        showSnackbar(
          "No info available correspond to box and gr number",
          "error"
        );
        handleFormConfigUpdate({
          box_number: { box_number: "" },
          make: {
            isVisible: false,
            make: "",
          },
          expiry_date: {
            isVisible: false,
            expiry_date: "",
          },
          refrigerator_in_timestamp: {
            isVisible: false,
            refrigerator_in_timestamp: "",
          },
          refrigerator_out_timestamp: {
            isVisible: false,
            refrigerator_out_timestamp: "",
          },
        });
      }
    }

    if (name === "gr_number" && value !== values?.gr_number) {
      handleFormConfigUpdate({
        box_number: { isVisible: false, options: [], box_number: "" },
        make: {
          isVisible: false,
          make: "",
        },
        expiry_date: {
          isVisible: false,
          expiry_date: "",
        },
        refrigerator_in_timestamp: {
          isVisible: false,
          refrigerator_in_timestamp: "",
        },
        refrigerator_out_timestamp: {
          isVisible: false,
          refrigerator_out_timestamp: "",
        },
      });
    }
  }

  async handleBlur(
    values: Record<string, any>,
    field: FieldConfig,
    event: React.FocusEvent<any>,
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void,
    setModalOpen: React.Dispatch<
      React.SetStateAction<{
        title: string;
        isOpen: boolean;
      }>
    >,
    setFormErrors: React.Dispatch<any>,
    showSnackbar: (
      message: string,
      severity?: "error" | "success" | "warning" | "info" | undefined
    ) => void,
    formId: number
  ) {
    console.log(`Form ${this.formCode} handleBlur triggered`, { values });
    if (values.gr_number.trim().length > 0) {
      const defaultBoxNumber = 1;
      const box = await fetchEventByFilterCrtieria({
        form_code: "solder_paste_in",
        serial_number: `${values.gr_number}#${defaultBoxNumber}`,
      });

      if (box?.results?.data?.length) {
        handleFormConfigUpdate({
          box_number: {
            isVisible: true,
            options: Array.from(
              { length: box?.results?.data?.[0]?.event_data?.box_numbers || 0 },
              (_, i) => ({
                label: `Box ${i + 1}`,
                value: i + 1,
              })
            ),
          },
        });
      } else {
        showSnackbar("No events exist for input G.R number", "error");
        handleFormConfigUpdate({
          box_number: { isVisible: false, options: [], box_number: "" },
          make: {
            isVisible: false,
            make: "",
          },
          expiry_date: {
            isVisible: false,
            expiry_date: "",
          },
          refrigerator_in_timestamp: {
            isVisible: false,
            refrigerator_in_timestamp: "",
          },
          refrigerator_out_timestamp: {
            isVisible: false,
            refrigerator_out_timestamp: "",
          },
        });
      }
    } else {
      handleFormConfigUpdate({
        box_number: { isVisible: false, options: [], box_number: "" },
        make: {
          isVisible: false,
          make: "",
        },
        expiry_date: {
          isVisible: false,
          expiry_date: "",
        },
        refrigerator_in_timestamp: {
          isVisible: false,
          refrigerator_in_timestamp: "",
        },
        refrigerator_out_timestamp: {
          isVisible: false,
          refrigerator_out_timestamp: "",
        },
      });
    }
  }

  handleFocus(
    values: Record<string, any>,
    field: FieldConfig,
    event: React.FocusEvent<any>,
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void,
    setModalOpen: React.Dispatch<
      React.SetStateAction<{
        title: string;
        isOpen: boolean;
      }>
    >,
    setFormErrors: React.Dispatch<any>,
    showSnackbar: (
      message: string,
      severity?: "error" | "success" | "warning" | "info" | undefined
    ) => void,
    formId: number
  ) {
    console.log(`Form ${this.formCode} handleFocus triggered`, { field });
  }

  handleClick(
    values: Record<string, any>,
    field: FieldConfig,
    event: React.MouseEvent<any>,
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void,
    setModalOpen: React.Dispatch<
      React.SetStateAction<{
        title: string;
        isOpen: boolean;
      }>
    >,
    setFormErrors: React.Dispatch<any>,
    showSnackbar: (
      message: string,
      severity?: "error" | "success" | "warning" | "info" | undefined
    ) => void,
    formId: number
  ) {
    console.log(`Form ${this.formCode} handleClick triggered`);
  }

  onModalClose(
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void
  ) {
    handleFormConfigUpdate({
      box_number: { isVisible: false, options: [], box_number: "" },
      gr_number: { gr_number: "" },
      make: {
        isVisible: false,
        make: "",
      },
      expiry_date: {
        isVisible: false,
        expiry_date: "",
      },
      refrigerator_in_timestamp: {
        isVisible: false,
        refrigerator_in_timestamp: "",
      },
      refrigerator_out_timestamp: {
        isVisible: false,
        refrigerator_out_timestamp: "",
      },
    });
  }
}
