import { FieldConfig } from "../../pages/FormCreator";
import {
  fetchEventByFilterCrtieria,
  getProductDataBySerialNumber,
} from "../formHelper";
import { IFormHandler } from "./baseFormHandle";

export class Form6<PERSON><PERSON>ler implements IFormHandler {
  constructor(private formCode: string) {}

  handleSubmit(values: Record<string, any>) {
    console.log(`Form ${this.formCode} submit triggered`, { values });
  }

  handleChange(
    values: Record<string, any>,
    field: FieldConfig,
    event: React.ChangeEvent<any>,
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void,
    setModalOpen: React.Dispatch<
      React.SetStateAction<{
        title: string;
        isOpen: boolean;
      }>
    >,
    setFormErrors: React.Dispatch<any>,
    showSnackbar: (
      message: string,
      severity?: "error" | "success" | "warning" | "info" | undefined
    ) => void,
    formId: number
  ) {
    handleFormConfigUpdate({
      box_numbers: {
        isVisible: false,
        box_number: "",
      },
      make: {
        isVisible: false,
        make: "",
      },
      expiry_date: {
        isVisible: false,
        expiry_date: "",
      },
    });
  }

  async handleBlur(
    values: Record<string, any>,
    field: FieldConfig,
    event: React.FocusEvent<any>,
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void,
    setModalOpen: React.Dispatch<
      React.SetStateAction<{
        title: string;
        isOpen: boolean;
      }>
    >,
    setFormErrors: React.Dispatch<any>,
    showSnackbar: (
      message: string,
      severity?: "error" | "success" | "warning" | "info" | undefined
    ) => void,
    formId: number
  ) {
    console.log(`Form ${this.formCode} handleBlur triggered`, { values });
    if (values.gr_number.trim().length > 0) {
      const defaultBoxNumber = 1;
      const res = await fetchEventByFilterCrtieria({
        form_code: "solder_paste_in",
        serial_number: `${values.gr_number}#${defaultBoxNumber}`,
      });

      if (res?.results?.data?.length) {
        setModalOpen({
          title: `Already exist GR.no(${values?.gr_number}). Do you wish to continue?`,
          isOpen: true,
        });
      }
      handleFormConfigUpdate({
        box_numbers: {
          isVisible: true,
        },
        make: {
          isVisible: true,
        },
        expiry_date: {
          isVisible: true,
        },
      });
    } else {
      handleFormConfigUpdate({
        box_numbers: {
          isVisible: false,
          box_number: "",
        },
        make: {
          isVisible: false,
          make: "",
        },
        expiry_date: {
          isVisible: false,
          expiry_date: "",
        },
      });
    }
  }

  handleFocus(
    values: Record<string, any>,
    field: FieldConfig,
    event: React.FocusEvent<any>,
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void,
    setModalOpen: React.Dispatch<
      React.SetStateAction<{
        title: string;
        isOpen: boolean;
      }>
    >,
    setFormErrors: React.Dispatch<any>,
    showSnackbar: (
      message: string,
      severity?: "error" | "success" | "warning" | "info" | undefined
    ) => void,
    formId: number
  ) {
    console.log(`Form ${this.formCode} handleFocus triggered`, { field });
  }

  handleClick(
    values: Record<string, any>,
    field: FieldConfig,
    event: React.MouseEvent<any>,
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void,
    setModalOpen: React.Dispatch<
      React.SetStateAction<{
        title: string;
        isOpen: boolean;
      }>
    >,
    setFormErrors: React.Dispatch<any>,
    showSnackbar: (
      message: string,
      severity?: "error" | "success" | "warning" | "info" | undefined
    ) => void,
    formId: number
  ) {
    console.log(`Form ${this.formCode} handleClick triggered`);
  }

  onModalClose(
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void
  ) {
    handleFormConfigUpdate({
      gr_number: {
        gr_number: "",
      },
      box_numbers: {
        isVisible: false,
        box_number: "",
      },
      make: {
        isVisible: false,
        make: "",
      },
      expiry_date: {
        isVisible: false,
        expiry_date: "",
      },
    });
  }
}
