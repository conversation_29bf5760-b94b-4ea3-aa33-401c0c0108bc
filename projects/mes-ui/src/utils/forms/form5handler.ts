import { FieldConfig } from "../../pages/FormCreator";
import { getProductDataBySerialNumber } from "../formHelper";
import { IFormHandler } from "./baseFormHandle";

export class Form5<PERSON>andler implements IFormHandler {
  constructor(private formCode: string) {}

  handleSubmit(values: Record<string, any>) {
    console.log(`Form ${this.formCode} submit triggered`, { values });
  }

  handleChange(
    values: Record<string, any>,
    field: FieldConfig,
    event: React.ChangeEvent<any>,
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void,
    setModalOpen: React.Dispatch<
      React.SetStateAction<{
        title: string;
        isOpen: boolean;
      }>
    >,
    setFormErrors: React.Dispatch<any>,
    showSnackbar: (
      message: string,
      severity?: "error" | "success" | "warning" | "info" | undefined
    ) => void,
    formId: number
  ) {
    const { name, value } = event.target;
    if (name === "serial_number" && value !== values?.serial_number) {
      handleFormConfigUpdate({
        part_number: { isVisible: false, part_number: "" },
        po: { isVisible: false, po: "" },
        part_description: {
          isVisible: false,
          part_description: "",
        },
        failed_stage: { isVisible: false, failed_stage: "" },
        fault_reported: { isVisible: false, fault_reported: "" },
        failed_timestamp: { isVisible: false, failed_timestamp: "" },
        failed_by: { isVisible: false, failed_by: "" },
        fault_type: { isVisible: false, fault_type: "" },
        fault_cause: { isVisible: false, fault_cause: "" },
        issue_type: { isVisible: false, issue_type: "" },
        component_location: {
          isVisible: false,
          component_location: "",
        },
        repair_action: { isVisible: false, repair_action: "" },
        pcb_type: { isVisible: false, pcb_type: "" },
        defect_generation_area: {
          isVisible: false,
          defect_generation_area: "",
        },
        detection_skippage: { isVisible: false, detection_skippage: "" },
        operator_responsible: {
          isVisible: false,
          operator_responsible: "",
        },
        remarks: { isVisible: false, remarks: "" },
        operator_occurance: {
          isVisible: false,
          operator_occurance: "",
        },
      });
    }
  }

  async handleBlur(
    values: Record<string, any>,
    field: FieldConfig,
    event: React.FocusEvent<any>,
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void,
    setModalOpen: React.Dispatch<
      React.SetStateAction<{
        title: string;
        isOpen: boolean;
      }>
    >,
    setFormErrors: React.Dispatch<any>,
    showSnackbar: (
      message: string,
      severity?: "error" | "success" | "warning" | "info" | undefined
    ) => void,
    formId: number
  ) {
    console.log(`Form ${this.formCode} handleBlur triggered`, { values });
    console.log({ field, values, event });
    // HE317171-35.12#D92410529460638
    if (
      values.serial_number.toString().length > 15 &&
      !values["part_description"]
    ) {
      const { partNumber, po, productData } =
        await getProductDataBySerialNumber(values.serial_number, showSnackbar);

      if (productData) {
        if (productData?.past_events?.length) {
          if (
            productData.past_events.some(
              (data: any) => data.form_details?.id === formId
            )
          ) {
            setModalOpen((p) => ({ ...p, isOpen: true }));
          }
        }

        const formInfo = productData?.past_events?.find(
          (data: any) => data.form_details?.id === 4
        );

        if (formInfo) {
          const { event_data } = formInfo;

          handleFormConfigUpdate({
            part_number: { isVisible: true, part_number: partNumber },
            po: { isVisible: true, po: po },
            part_description: {
              isVisible: true,
              part_description: productData?.description ?? "",
            },
            failed_stage: {
              isVisible: true,
              failed_stage: event_data?.failed_stage,
            },
            fault_reported: {
              isVisible: true,
              fault_reported: event_data?.fault_reported ?? "",
            },
            failed_timestamp: {
              isVisible: true,
              failed_timestamp: event_data?.failed_timestamp ?? "",
            },
            failed_by: {
              isVisible: true,
              failed_by: event_data?.failed_by ?? "",
            },
            fault_type: {
              isVisible: true,
              fault_type: event_data?.fault_details
                ?.map((o: any) => o.fault_type)
                ?.join(", "),
            },
            fault_cause: {
              isVisible: true,
              fault_cause: event_data?.fault_details
                ?.map((o: any) => o.fault_cause)
                ?.join(", "),
            },
            issue_type: {
              isVisible: true,
              issue_type: event_data?.fault_details
                ?.map((o: any) => o.issue_type)
                ?.join(", "),
            },
            component_location: {
              isVisible: true,
              component_location: event_data?.fault_details
                ?.map((o: any) => o?.component_location?.join(","))
                .join(", "),
            },
            repair_action: {
              isVisible: true,
              repair_action: event_data?.fault_details
                ?.map((o: any) => o.repair_action)
                ?.join(", "),
            },
            pcb_type: { isVisible: true, pcb_type: "" },
            defect_generation_area: {
              isVisible: true,
              defect_generation_area: "",
            },
            detection_skippage: { isVisible: true, detection_skippage: "" },
            operator_responsible: {
              isVisible: true,
              operator_responsible: "",
            },
            operator_occurance: {
              isVisible: true,
              operator_occurance: "",
            },
            remarks: { isVisible: true, remarks: "" },
          });
        } else {
          showSnackbar(
            "No Past Rework events exist for input seril number",
            "error"
          );
        }
      } else {
        handleFormConfigUpdate({
          serial_number: { serial_number: "" },
          part_number: { isVisible: false, part_number: "" },
          po: { isVisible: false, po: "" },
          part_description: {
            isVisible: false,
            part_description: "",
          },
          failed_stage: { isVisible: false, failed_stage: "" },
          fault_reported: { isVisible: false, fault_reported: "" },
          failed_timestamp: { isVisible: false, failed_timestamp: "" },
          failed_by: { isVisible: false, failed_by: "" },
          fault_type: { isVisible: false, fault_type: "" },
          fault_cause: { isVisible: false, fault_cause: "" },
          issue_type: { isVisible: false, issue_type: "" },
          component_location: {
            isVisible: false,
            component_location: "",
          },
          repair_action: { isVisible: false, repair_action: "" },
          pcb_type: { isVisible: false, pcb_type: "" },
          defect_generation_area: {
            isVisible: false,
            defect_generation_area: "",
          },
          detection_skippage: { isVisible: false, detection_skippage: "" },
          operator_responsible: {
            isVisible: false,
            operator_responsible: "",
          },
          remarks: { isVisible: false, remarks: "" },
          operator_occurance: {
            isVisible: false,
            operator_occurance: "",
          },
        });
      }
    } else {
      handleFormConfigUpdate({
        part_number: { isVisible: false, part_number: "" },
        po: { isVisible: false, po: "" },
        part_description: {
          isVisible: false,
          part_description: "",
        },
        failed_stage: { isVisible: false, failed_stage: "" },
        fault_reported: { isVisible: false, fault_reported: "" },
        failed_timestamp: { isVisible: false, failed_timestamp: "" },
        failed_by: { isVisible: false, failed_by: "" },
        fault_type: { isVisible: false, fault_type: "" },
        fault_cause: { isVisible: false, fault_cause: "" },
        issue_type: { isVisible: false, issue_type: "" },
        component_location: {
          isVisible: false,
          component_location: "",
        },
        repair_action: { isVisible: false, repair_action: "" },
        pcb_type: { isVisible: false, pcb_type: "" },
        defect_generation_area: {
          isVisible: false,
          defect_generation_area: "",
        },
        detection_skippage: { isVisible: false, detection_skippage: "" },
        operator_responsible: {
          isVisible: false,
          operator_responsible: "",
        },
        remarks: { isVisible: false, remarks: "" },
        operator_occurance: {
          isVisible: false,
          operator_occurance: "",
        },
      });
    }
  }

  handleFocus(
    values: Record<string, any>,
    field: FieldConfig,
    event: React.FocusEvent<any>,
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void,
    setModalOpen: React.Dispatch<
      React.SetStateAction<{
        title: string;
        isOpen: boolean;
      }>
    >,
    setFormErrors: React.Dispatch<any>,
    showSnackbar: (
      message: string,
      severity?: "error" | "success" | "warning" | "info" | undefined
    ) => void,
    formId: number
  ) {
    console.log(`Form ${this.formCode} handleFocus triggered`, { field });
  }

  handleClick(
    values: Record<string, any>,
    field: FieldConfig,
    event: React.MouseEvent<any>,
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void,
    setModalOpen: React.Dispatch<
      React.SetStateAction<{
        title: string;
        isOpen: boolean;
      }>
    >,
    setFormErrors: React.Dispatch<any>,
    showSnackbar: (
      message: string,
      severity?: "error" | "success" | "warning" | "info" | undefined
    ) => void,
    formId: number
  ) {
    console.log(`Form ${this.formCode} handleClick triggered`);
  }

  onModalClose(
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void
  ) {
    handleFormConfigUpdate({
      part_number: { isVisible: false, part_number: "" },
      po: { isVisible: false, po: "" },
      part_description: {
        isVisible: false,
        part_description: "",
      },
      failed_stage: { isVisible: false, failed_stage: "" },
      fault_reported: { isVisible: false, fault_reported: "" },
      failed_timestamp: { isVisible: false, failed_timestamp: "" },
      failed_by: { isVisible: false, failed_by: "" },
      fault_type: { isVisible: false, fault_type: "" },
      fault_cause: { isVisible: false, fault_cause: "" },
      issue_type: { isVisible: false, issue_type: "" },
      component_location: {
        isVisible: false,
        component_location: "",
      },
      repair_action: { isVisible: false, repair_action: "" },
      pcb_type: { isVisible: false, pcb_type: "" },
      defect_generation_area: {
        isVisible: false,
        defect_generation_area: "",
      },
      detection_skippage: { isVisible: false, detection_skippage: "" },
      operator_responsible: {
        isVisible: false,
        operator_responsible: "",
      },
      remarks: { isVisible: false, remarks: "" },
      operator_occurance: {
        isVisible: false,
        operator_occurance: "",
      },
    });
  }
}
