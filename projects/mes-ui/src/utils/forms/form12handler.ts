import { FieldConfig } from "../../pages/FormCreator";
import { getProductDataBySerialNumber } from "../formHelper";
import { IFormHandler } from "./baseFormHandle";

export class Form12<PERSON><PERSON>ler implements IFormHandler {
  constructor(private formCode: string) {}

  handleSubmit(values: Record<string, any>) {
    console.log(`Form ${this.formCode} submit triggered`, { values });
  }

  handleChange(
    values: Record<string, any>,
    field: FieldConfig,
    event: React.ChangeEvent<any>,
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void,
    setModalOpen: React.Dispatch<
      React.SetStateAction<{
        title: string;
        isOpen: boolean;
      }>
    >,
    setFormErrors: React.Dispatch<any>,
    showSnackbar: (
      message: string,
      severity?: "error" | "success" | "warning" | "info" | undefined
    ) => void,
    formId: number
  ) {
    const { name, value } = event.target;
    if (name === "serial_number" && value && value !== values?.serial_number) {
      handleFormConfigUpdate({
        po_number: { isVisible: false, po_number: "" },
        assembly_number: { isVisible: false, assembly_number: "" },
      });
    }
  }

  async handleBlur(
    values: Record<string, any>,
    field: FieldConfig,
    event: React.FocusEvent<any>,
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void,
    setModalOpen: React.Dispatch<
      React.SetStateAction<{
        title: string;
        isOpen: boolean;
      }>
    >,
    setFormErrors: React.Dispatch<any>,
    showSnackbar: (
      message: string,
      severity?: "error" | "success" | "warning" | "info" | undefined
    ) => void,
    formId: number
  ) {
    // HE317171-35.12#D92410529460638
    if (values.serial_number.trim().toString().length) {
      const { partNumber, po, productData } =
        await getProductDataBySerialNumber(values.serial_number, showSnackbar);
      if (productData) {
        if (productData?.past_events?.length) {
          if (
            productData.past_events.some(
              (data: any) => data.form_details?.id === formId
            )
          ) {
            setModalOpen((prev) => ({ ...prev, isOpen: true }));
          }
        }
        handleFormConfigUpdate({
          po_number: { isVisible: true, po_number: partNumber },
          assembly_number: { isVisible: true, assembly_number: po },
        });
      } else {
        handleFormConfigUpdate({
          po_number: { isVisible: false, po_number: "" },
          assembly_number: { isVisible: false, assembly_number: "" },
        });
      }
    } else {
      handleFormConfigUpdate({
        po_number: { isVisible: false, po_number: "" },
        assembly_number: { isVisible: false, assembly_number: "" },
      });
    }
  }

  handleFocus(
    values: Record<string, any>,
    field: FieldConfig,
    event: React.FocusEvent<any>,
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void,
    setModalOpen: React.Dispatch<
      React.SetStateAction<{
        title: string;
        isOpen: boolean;
      }>
    >,
    setFormErrors: React.Dispatch<any>,
    showSnackbar: (
      message: string,
      severity?: "error" | "success" | "warning" | "info" | undefined
    ) => void,
    formId: number
  ) {
    console.log(`Form ${this.formCode} handleFocus triggered`, { field });
  }

  handleClick(
    values: Record<string, any>,
    field: FieldConfig,
    event: React.MouseEvent<any>,
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void,
    setModalOpen: React.Dispatch<
      React.SetStateAction<{
        title: string;
        isOpen: boolean;
      }>
    >,
    setFormErrors: React.Dispatch<any>,
    showSnackbar: (
      message: string,
      severity?: "error" | "success" | "warning" | "info" | undefined
    ) => void,
    formId: number
  ) {
    console.log(`Form ${this.formCode} handleClick triggered`);
  }

  onModalClose(
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void
  ) {
    handleFormConfigUpdate({
      po_number: { isVisible: false, po_number: "" },
      assembly_number: { isVisible: false, assembly_number: "" },
    });
  }
}
