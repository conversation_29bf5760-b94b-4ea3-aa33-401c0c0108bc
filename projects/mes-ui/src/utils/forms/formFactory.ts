import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./baseFormHandle";
import { Form10Hand<PERSON> } from "./form10handler";
import { Form11Handler } from "./form11handler";
import { Form12Handler } from "./form12handler";
import { Form1Handler } from "./form1handler";
import { Form2Handler } from "./form2handler";
import { Form3Handler } from "./form3handler";
import { Form4Handler } from "./form4handler";
import { Form5Handler } from "./form5handler";
import { Form6Handler } from "./form6handler";
import { Form7Handler } from "./form7handler";
import { Form8Handler } from "./form8handler";
import { Form9Handler } from "./form9handler";

export class FormHandlerFactory {
  private static handlers: { [key: string]: IFormHandler } = {};

  static getHandler(formCode: string): IFormHandler | null {
    // Check if the handler already exists in the object
    if (!this.handlers[formCode]) {
      // Create a new handler based on the formId

      switch (formCode) {
        case "pre_wave":
          this.handlers[formCode] = new Form1Handler(formCode);
          break;
        case "post_wave":
          this.handlers[formCode] = new Form2Handler(formCode);
          break;
        case "test_form":
          this.handlers[formCode] = new Form3Handler(formCode);
          break;
        case "rework_station":
          this.handlers[formCode] = new Form4Handler(formCode);
          break;
        case "analysis_form":
          this.handlers[formCode] = new Form5Handler(formCode);
          break;
        case "solder_paste_in":
          this.handlers[formCode] = new Form6Handler(formCode);
          break;
        case "solder_paste_out":
          this.handlers[formCode] = new Form7Handler(formCode);
          break;
        case "solder_paste_mixer":
          this.handlers[formCode] = new Form8Handler(formCode);
          break;
        case "solder_paste_viscosity":
          this.handlers[formCode] = new Form9Handler(formCode);
          break;
        case "stencil_wash_in":
          this.handlers[formCode] = new Form10Handler(formCode);
          break;
        case "stencil_wash_out":
          this.handlers[formCode] = new Form11Handler(formCode);
          break;
        case "drop_component_placement":
          this.handlers[formCode] = new Form12Handler(formCode);
          break;
        default:
          this.handlers[formCode] = new BaseFormHandler(formCode);
      }
    }

    // Retrieve the handler from the object and return it
    const handler = this.handlers[formCode];

    // If the handler doesn't exist (although this shouldn't happen), return null
    if (!handler) {
      console.warn(`Handler for formId ${formCode} could not be created.`);
      return null;
    }

    return handler;
  }
}
