import { FieldConfig } from "../../pages/FormCreator";
import { getProductDataBySerialNumber } from "../formHelper";
import { IFormHandler } from "./baseFormHandle";

export class Form3Handler implements IFormHandler {
  constructor(private formCode: string) {}

  handleSubmit(values: Record<string, any>) {
    console.log(`Form ${this.formCode} submit triggered`, { values });
  }

  handleChange(
    values: Record<string, any>,
    field: FieldConfig,
    event: React.ChangeEvent<any>,
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void,
    setModalOpen: React.Dispatch<
      React.SetStateAction<{
        title: string;
        isOpen: boolean;
      }>
    >,
    setFormErrors: React.Dispatch<any>,
    showSnackbar: (
      message: string,
      severity?: "error" | "success" | "warning" | "info" | undefined
    ) => void,
    formId: number
  ) {
    console.log(`Form ${this.formCode} handleChange triggered`, { field });
  }

  async handleBlur(
    values: Record<string, any>,
    field: FieldConfig,
    event: React.FocusEvent<any>,
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void,
    setModalOpen: React.Dispatch<
      React.SetStateAction<{
        title: string;
        isOpen: boolean;
      }>
    >,
    setFormErrors: React.Dispatch<any>,
    showSnackbar: (
      message: string,
      severity?: "error" | "success" | "warning" | "info" | undefined
    ) => void,
    formId: number
  ) {
    console.log(`Form ${this.formCode} handleBlur triggered`, { values });
    if (
      values.serial_number.toString().length > 15 &&
      !values["part_description"]
    ) {
      const { partNumber, po, productData } =
        await getProductDataBySerialNumber(values.serial_number, showSnackbar);

      if (productData) {
        if (productData?.past_events?.length) {
          if (
            productData.past_events.some(
              (data: any) => data.form_details?.id === formId
            )
          ) {
            setModalOpen((prev) => ({ ...prev, isOpen: true }));
          }
        }
        handleFormConfigUpdate({
          part_number: { isVisible: true, part_number: partNumber },
          po: { isVisible: true, po: po },
          part_description: {
            isVisible: true,
            part_description: productData?.description ?? "",
          },
        });
      } else {
        handleFormConfigUpdate({
          serial_number: { serial_number: "" },
          part_number: { isVisible: false, part_number: "" },
          po: { isVisible: false, po: "" },
          part_description: {
            isVisible: false,
            part_description: "",
          },
        });
      }
    } else {
      handleFormConfigUpdate({
        part_number: { isVisible: false, part_number: "" },
        po: { isVisible: false, po: "" },
        part_description: {
          isVisible: false,
          part_description: "",
        },
      });
    }
  }

  handleFocus(
    values: Record<string, any>,
    field: FieldConfig,
    event: React.FocusEvent<any>,
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void,
    setModalOpen: React.Dispatch<
      React.SetStateAction<{
        title: string;
        isOpen: boolean;
      }>
    >,
    setFormErrors: React.Dispatch<any>,
    showSnackbar: (
      message: string,
      severity?: "error" | "success" | "warning" | "info" | undefined
    ) => void,
    formId: number
  ) {
    console.log(`Form ${this.formCode} handleFocus triggered`, { field });
  }

  handleClick(
    values: Record<string, any>,
    field: FieldConfig,
    event: React.MouseEvent<any>,
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void,
    setModalOpen: React.Dispatch<
      React.SetStateAction<{
        title: string;
        isOpen: boolean;
      }>
    >,
    setFormErrors: React.Dispatch<any>,
    showSnackbar: (
      message: string,
      severity?: "error" | "success" | "warning" | "info" | undefined
    ) => void,
    formId: number
  ) {
    console.log(`Form ${this.formCode} handleClick triggered`);
    const { clickedState } = event as any;
    if (clickedState === "fail") {
      handleFormConfigUpdate({
        fault_type: {
          isVisible: true,
          required: true,
        },
        form_status: {
          form_status: "fail",
        },
      });
    } else {
      handleFormConfigUpdate({
        fault_type: {
          isVisible: false,
          required: false,
          fault_type: "",
        },
        form_status: {
          form_status: "",
        },
      });
    }
  }
}
