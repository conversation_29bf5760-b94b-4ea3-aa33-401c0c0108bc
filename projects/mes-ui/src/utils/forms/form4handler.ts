import { FieldConfig } from "../../pages/FormCreator";
import {
  formatToUtcDateTime,
  getProductDataBySerialNumber,
} from "../formHelper";
import { IFormHandler } from "./baseFormHandle";

export class Form4<PERSON><PERSON>ler implements IForm<PERSON>andler {
  constructor(private formCode: string) {}

  handleSubmit(values: Record<string, any>) {
    console.log(`Form ${this.formCode} submit triggered`, { values });
  }

  handleChange(
    values: Record<string, any>,
    field: FieldConfig,
    event: React.ChangeEvent<any>,
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void,
    setModalOpen: React.Dispatch<
      React.SetStateAction<{
        title: string;
        isOpen: boolean;
      }>
    >,
    setFormErrors: React.Dispatch<any>,
    showSnackbar: (
      message: string,
      severity?: "error" | "success" | "warning" | "info" | undefined
    ) => void,
    formId: number
  ) {
    console.log(`Form ${this.formCode} handleChange triggered`, {
      field,
    });
  }

  async handleBlur(
    values: Record<string, any>,
    field: FieldConfig,
    event: React.FocusEvent<any>,
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void,
    setModalOpen: React.Dispatch<
      React.SetStateAction<{
        title: string;
        isOpen: boolean;
      }>
    >,
    setFormErrors: React.Dispatch<any>,
    showSnackbar: (
      message: string,
      severity?: "error" | "success" | "warning" | "info" | undefined
    ) => void,
    formId: number
  ) {
    console.log(`Form ${this.formCode} handleBlur triggered`, { values });
    if (
      values.serial_number.toString().length > 15 &&
      !values["part_description"]
    ) {
      // HE317171-35.12#D92410529460638
      const { partNumber, po, productData } =
        await getProductDataBySerialNumber(values.serial_number, showSnackbar);

      if (productData?.past_events?.length) {
        if (
          productData.past_events.some(
            (data: any) => data.form_details?.id === formId
          )
        ) {
          setModalOpen((p) => ({ ...p, isOpen: true }));
        }

        const updateObject: Record<string, any> = {
          part_number: { isVisible: true, part_number: partNumber },
          po: { isVisible: true, po: po },
          part_description: {
            isVisible: true,
            part_description: productData?.description ?? "",
          },
          failed_timestamp: {
            isVisible: true,
            failed_timestamp: productData?.last_failed_event?.timestamp
              ? formatToUtcDateTime(productData?.last_failed_event?.timestamp)
              : "",
          },
          failed_by: {
            isVisible: true,
            failed_by:
              productData?.last_failed_event?.event_data?.sign_in_user ?? "",
          },
          failed_stage: {
            failed_stage:
              productData?.last_failed_event?.form_details?.name ?? "",
            isVisible: true,
          },
          component_location: {
            options:
              productData?.components?.map((comp: any) => ({
                value: comp?.component?.code,
                label: comp?.component?.name,
              })) ?? [],
            readonly: false,
            required: true,
          },
        };

        if (
          productData?.last_failed_event?.form_details?.code === "test_form"
        ) {
          const fault_type =
            productData?.last_failed_event?.event_data?.fault_type ?? "";
          const fault_cause =
            productData?.last_failed_event?.event_data?.fault_cause ?? "";

          updateObject["fault_reported"] = {
            fault_reported: fault_cause || fault_type,
            isVisible: true,
          };
          updateObject["fault_cause"] = {
            fault_cause: fault_cause,
            readonly: false,
            required: fault_cause ? true : false,
          };
          updateObject["fault_type"] = {
            fault_type: fault_type,
            readonly: false,
            required: fault_type ? true : false,
          };
        }

        if (
          productData?.last_failed_event?.form_details?.code === "post_wave"
        ) {
          const fault_data =
            productData?.last_failed_event?.event_data?.fault_cause ?? "";
          const fault_cause = Array.isArray(fault_data)
            ? fault_data[0]
            : fault_data;

          updateObject["fault_reported"] = {
            fault_reported: fault_cause,
            isVisible: true,
            readonly: false,
          };

          updateObject["fault_cause"] = {
            fault_cause: fault_cause,
            readonly: false,
            required: true,
          };

          updateObject["fault_type"] = {
            fault_type: "",
            readonly: true,
            required: false,
          };
        }

        handleFormConfigUpdate(updateObject);
      } else {
        handleFormConfigUpdate({
          part_number: { isVisible: false, part_number: "" },
          po: { isVisible: false, po: "" },
          part_description: {
            isVisible: false,
            part_description: "",
          },
          fault_reported: {
            fault_reported: "",
            isVisible: false,
          },
          failed_stage: {
            failed_stage: "",
            isVisible: false,
          },
          fault_cause: {
            fault_cause: "",
            readonly: true,
            required: true,
          },
          fault_type: {
            fault_type: "",
            readonly: true,
            required: true,
          },
          issue_type: {
            issue_type: "",
          },
          component_location: {
            component_location: "",
            required: true,
          },
          failed_timestamp: {
            isVisible: false,
            failed_timestamp: "",
          },
          failed_by: {
            isVisible: false,
            failed_by: "",
          },
        });
      }
    } else {
      handleFormConfigUpdate({
        part_number: { isVisible: false, part_number: "" },
        po: { isVisible: false, po: "" },
        part_description: {
          isVisible: false,
          part_description: "",
        },
        fault_reported: {
          fault_reported: "",
          isVisible: false,
        },
        failed_stage: {
          failed_stage: "",
          isVisible: false,
        },
        fault_cause: {
          fault_cause: "",
          readonly: true,
          required: true,
        },
        fault_type: {
          fault_type: "",
          readonly: true,
          required: true,
        },
        issue_type: {
          issue_type: "",
        },
        component_location: {
          component_location: "",
          required: true,
        },
      });
    }
  }

  handleFocus(
    values: Record<string, any>,
    field: FieldConfig,
    event: React.FocusEvent<any>,
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void,
    setModalOpen: React.Dispatch<
      React.SetStateAction<{
        title: string;
        isOpen: boolean;
      }>
    >,
    setFormErrors: React.Dispatch<any>,
    showSnackbar: (
      message: string,
      severity?: "error" | "success" | "warning" | "info" | undefined
    ) => void,
    formId: number
  ) {
    console.log(`Form ${this.formCode} handleFocus triggered`, { field });
  }

  handleClick(
    values: Record<string, any>,
    field: FieldConfig,
    event: React.MouseEvent<any>,
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void,
    setModalOpen: React.Dispatch<
      React.SetStateAction<{
        title: string;
        isOpen: boolean;
      }>
    >,
    setFormErrors: React.Dispatch<any>,
    showSnackbar: (
      message: string,
      severity?: "error" | "success" | "warning" | "info" | undefined
    ) => void,
    formId: number,
    handleSubmitButtonVisibilty?: React.Dispatch<React.SetStateAction<boolean>>
  ) {
    console.log(`Form ${this.formCode} handleClick triggered`);
    if (values.form_status === "fail") {
      handleSubmitButtonVisibilty?.(true);
    } else {
      handleSubmitButtonVisibilty?.(false);
    }
  }

  onModalClose(
    handleFormConfigUpdate: (fieldConfigUpdates: Record<string, any>) => void
  ) {
    handleFormConfigUpdate({
      part_number: { isVisible: false, part_number: "" },
      po: { isVisible: false, po: "" },
      part_description: {
        isVisible: false,
        part_description: "",
      },
      fault_reported: {
        fault_reported: "",
        isVisible: false,
      },
      failed_stage: {
        failed_stage: "",
        isVisible: false,
      },
      fault_cause: {
        fault_cause: "",
        readonly: true,
        required: true,
      },
      fault_type: {
        fault_type: "",
        readonly: true,
        required: true,
      },
      issue_type: {
        issue_type: "",
      },
      component_location: {
        component_location: "",
        required: true,
      },
      failed_timestamp: {
        isVisible: false,
        failed_timestamp: "",
      },
      failed_by: {
        isVisible: false,
        failed_by: "",
      },
    });
  }
}
