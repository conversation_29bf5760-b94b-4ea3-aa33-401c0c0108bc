import axios, { AxiosError } from "axios";
import { getAccessToken, clearAuthData } from "./authUtils"; // Import functions for token management

const ADMIN_BECKEND_TEST_URL = `https://2979-103-103-59-28.ngrok-free.app/mes_trace`;
const ADMIN_BECKEND_PROD_URL = `/mes_trace`;
export const BASE_URL = ``;
// export const BASE_URL = `https://51d5-103-103-56-211.ngrok-free.app/`

const axiosInstance = axios.create({
  baseURL: `${ADMIN_BECKEND_PROD_URL}`,
  headers: {
    "Content-Type": "application/json",
    "Access-Control-Allow-Origin": "*",
    "ngrok-skip-browser-warning": "true",
  },
});

axiosInstance.interceptors.request.use(
  (config) => {
    const accessToken = getAccessToken();
    if (accessToken) {
      config.headers.Authorization = `Bearer ${accessToken}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

axiosInstance.interceptors.response.use(
  (response) => response,
  async (error: AxiosError) => {
    if (error.response) {
      const { status, data } = error.response as any;

      if (status === 401) {
        clearAuthData();

        if (window.location.pathname !== "/login") {
          window.location.href = "/login";
          window.location.reload();
        }
      }

      return Promise.reject(data?.error ?? "An error occurred. Please try again.");
    }
    return Promise.reject(error);
  }
);

export default axiosInstance;
