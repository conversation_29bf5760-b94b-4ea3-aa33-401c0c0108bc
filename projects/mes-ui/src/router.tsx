import { Suspense } from "react";
import { Routes, Route } from "react-router-dom";
import { screens } from "./config/route"; // Assuming you have a screens config
import NotFound from "./pages/NotFound";
import ProtectedRoute from "./components/ProtectedRoute";
import PublicRoute from "./components/PublicRoute";

export default function AppRouter() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <Routes>
        {screens.map((route, index) => {
          const { component: Component, path, requiredRole, type } = route;
          // if (type === "protected") {
          //   return (
          //     <Route
          //       key={index}
          //       path={path}
          //       element={
          //         <ProtectedRoute
          //           requiredRole={requiredRole}
          //           routeType={type}
          //           component={Component}
          //         />
          //       }
          //     />
          //   );
          // }
          // if (type === "public") {
          //   return (
          //     <Route
          //       key={index}
          //       path={path}
          //       element={
          //         <PublicRoute
          //           component={Component}
          //           redirectPath={route.redirectPath}
          //         />
          //       }
          //     />
          //   );
          // }
          return <Route key={index} path={path} element={<Component />} />;
        })}
        <Route path="*" element={<NotFound />} />
      </Routes>
    </Suspense>
  );
}
