import React, { useCallback, useEffect, useRef, useState } from "react";
import AppRouter from "./router";
import AppSidebar from "./components/Sidebar";
import Header from "./components/Header";
import Style from "./styles/App.module.scss";
import { QueryClientProvider, QueryClient } from "@tanstack/react-query";
import { SnackbarProvider } from "./context/SnackBarContext";
import { AuthProvider, useAuth } from "./context/AuthContext";
import { BrowserRouter as Router } from "react-router-dom";
import useNetworkStatus from "./hooks/useNetworkStatus";
import OfflineMessage from "./components/OfflineMsg";
import ErrorBoundary from "./components/ErrorBoundary";


const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 0,
      refetchOnWindowFocus: false, // Disable refetching on tab switch
      refetchOnReconnect: false, // Disable refetching on network reconnect
      refetchInterval: false, // Disable polling (if any)
    },
  },
});

const App: React.FC = () => {
  return (
    <AuthProvider>
      <Router>
        <ErrorBoundary>
          <AppContent />
        </ErrorBoundary>
      </Router>
    </AuthProvider>
  );
};

const AppContent: React.FC = () => {
  const { authState, clearAuthData } = useAuth();
  const isOnline = useNetworkStatus();
  // Check if we're on desktop
  const isDesktop = window.innerWidth >= 1025; // Match the desktop breakpoint
  const [isDrawerOpen, setIsDrawerOpen] = useState(isDesktop);

  // Effect to handle window resize and set drawer state accordingly
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1025) {
        setIsDrawerOpen(true); // Always open on desktop
      } else {
        setIsDrawerOpen(false); // Always closed on mobile/tablet
      }
    };

    // Initial check
    handleResize();

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Toggle drawer function for mobile/tablet views
  const toggleDrawer = () => {
    if (window.innerWidth < 1025) {
      setIsDrawerOpen(prev => !prev);
    }
  };

  const sidebarRef = useRef<HTMLDivElement | null>(null);

  const handleClickOutside = useCallback((event: MouseEvent) => {
    // Only apply on mobile and tablet
    if (window.innerWidth >= 1025) return;

    const target = event.target as HTMLElement;

    // Ignore clicks on the sidebar toggle button
    if (target.closest("#sidebar-toggle")) return;

    if (sidebarRef.current && !sidebarRef.current.contains(target)) {
      setIsDrawerOpen(false);
    }
  }, []);

  useEffect(() => {
    // Only add event listener on mobile and tablet
    if (isDrawerOpen && window.innerWidth < 1025) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isDrawerOpen, handleClickOutside]);

  // State to track if we're on mobile/tablet
  const [isMobileOrTablet, setIsMobileOrTablet] = useState(window.innerWidth < 1025);

  // Effect to update isMobileOrTablet state on window resize
  useEffect(() => {
    const handleResize = () => {
      setIsMobileOrTablet(window.innerWidth < 1025);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const isAuthenticated = !!authState?.accessToken;

  return (
    <>
      <div className={Style.app}>
        {isAuthenticated && (
          <>
            {/* Mobile/Tablet Header with sidebar toggle */}
            <Header
              onSidebarToggle={toggleDrawer}
              onLogout={clearAuthData}
              user={authState?.user || { username: '', email: '' }}
              sidebarRef={sidebarRef}
            />

            <AppSidebar
              isDrawerOpen={isDrawerOpen}
              sidebarRef={sidebarRef}
              onLogout={clearAuthData}
            />
          </>
        )}
        <div
          className={Style.main_section}
          style={{
            marginTop: isMobileOrTablet ? "60px" : "0px", // Add margin for header on mobile/tablet
            marginLeft: isAuthenticated ? undefined : "0px", // Remove margin-left when not authenticated
          }}
        >
          <QueryClientProvider client={queryClient}>
            <SnackbarProvider>
              {!isOnline ? (
                <OfflineMessage />
              ) : (
                <ErrorBoundary>
                  <AppRouter />
                </ErrorBoundary>
              )}
            </SnackbarProvider>
          </QueryClientProvider>
        </div>
      </div>
    </>
  );
};

export default App;
