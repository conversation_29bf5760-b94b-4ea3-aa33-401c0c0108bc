interface ActionConfig {
  action_type: string;
  handler: string;
}

export interface ResponsiveSize {
  desktop: string;
  tablet: string;
  mobile: string;
}

export interface FormField {
  label: string;
  name: string;
  type: string;
  required: boolean;
  placeholder?: string;
  readonly: boolean;
  regex?: string;
  options?: { value: string; label: string }[];
  position: number;
  width?: ResponsiveSize;
  height?: ResponsiveSize;
  marginRight?: ResponsiveSize;
  alignment?: "left" | "right" | "center";
  isVisible: boolean;
  actions: ActionConfig[];
  component_name?: string;
  form_fields: FormField[];
}

interface FormSchema {
  form_fields: FormField[];
}

export interface ApiFormConfig {
  id: number;
  created_at: string;
  updated_at: string;
  name: string;
  description: string;
  form_schema: FormSchema;
  code: string;
  version: number;
  is_active: boolean;
  created_by: string;
}

export interface FormListResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: ApiFormConfig[];
}
