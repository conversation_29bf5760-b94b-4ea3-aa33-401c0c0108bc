import { ResponsiveSize } from "./form-config.api.interface";

interface ActionConfig {
  action_type: string;
  handler: string;
}
export interface FieldConfig {
  label: string;
  name: string;
  type: string;
  required: boolean;
  placeholder?: string;
  readonly: boolean;
  regex?: string;
  options?: { value: string; label: string }[];
  position: number;
  width?: ResponsiveSize;
  height?: ResponsiveSize;
  marginRight?: ResponsiveSize;
  alignment?: "left" | "right" | "center";
  isVisible: boolean;
  component_name: string;
  actions: ActionConfig[];
}

export interface FormConfig {
  id: number;
  name: string;
  fields: FieldConfig[];
}
