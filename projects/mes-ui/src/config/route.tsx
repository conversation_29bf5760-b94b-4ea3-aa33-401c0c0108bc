import {
  Dashboard,
  FormConfigList,
  DynamicForm,
  FormCreator,
  SignInForm,
  UploadPage,
  RouteConfigCreation,
  ProductRoutingPage,
  ProductRoute,
  WorkOrderTable,
  RoutingTablePage,
  RoutingViewPage,
  ProductTablePage,
  ProductViewPage,
  BOMTablePage,
  ProductBOMsPage
} from "../pages";

import { RouteConfig } from "../interfaces/route.inteface";

const homePage: RouteConfig = {
  path: `/`,
  staticPath: `/`,
  name: "dashboard",
  exact: true,
  component: Dashboard,
  redirectPath: "",
  type: "protected",
};

const loginPage: RouteConfig = {
  path: `/login`,
  staticPath: `/login`,
  name: "login",
  exact: true,
  component: SignInForm,
  redirectPath: "/",
  type: "public",
};

const formConfigList: RouteConfig = {
  path: `/form-config-list`,
  staticPath: `form-config-list`,
  name: "form-config",
  exact: true,
  component: FormConfigList,
  redirectPath: "",
  type: "protected",
};

const formCreator: RouteConfig = {
  path: `/form-creator`,
  staticPath: `form-creator`,
  name: "form-creator",
  exact: true,
  component: FormCreator,
  redirectPath: "",
  type: "protected",
};

const dynamicForm: RouteConfig = {
  path: `/form/:id`,
  staticPath: `/form-config-list`,
  name: "form-config",
  exact: true,
  component: DynamicForm,
  redirectPath: "",
  type: "protected",
};

const aoiUploadPage: RouteConfig = {
  path: `/aoi-sheet-upload`,
  staticPath: `/aoi-sheet-upload`,
  name: "aoi-sheet",
  exact: true,
  component: UploadPage,
  redirectPath: "",
  type: "protected",
};

const processRouteList: RouteConfig = {
  path: `product-routing-list`,
  staticPath: `/product-routing-list`,
  name: "product-routing-list",
  exact: true,
  component: ProductRoutingPage,
  redirectPath: "",
  type: "protected",
};

const createRoute: RouteConfig = {
  path: `/routing/create`,
  staticPath: `/routing/create`,
  name: "/routing/create",
  exact: true,
  component: RouteConfigCreation,
  redirectPath: "",
  type: "protected",
};

// const dynamicRouteFlow: RouteConfig = {
//   path: `/product-routing-list/:id`,
//   staticPath: `/product-routing-list`,
//   name: "route-flow",
//   exact: true,
//   component: ProductRoutingPage,
//   redirectPath: "",
//   type: "protected",
// };

const productRoute: RouteConfig = {
  path: `/product-routes`,
  staticPath: `product-routes`,
  name: "product-routes`",
  exact: true,
  component: ProductRoute,
  redirectPath: "",
  type: "protected",
};
const workOrderTable: RouteConfig = {
  path: `/work-order-table`,
  staticPath: `work-order-table`,
  name: `work-order-table`,
  exact: true,
  component: WorkOrderTable,
  redirectPath: "",
  type: "protected",
};

const routingTablePage: RouteConfig = {
  path: `/routings`,
  staticPath: `/routings`,
  name: "routing-table",
  exact: true,
  component: RoutingTablePage,
  redirectPath: "",
  type: "protected",
};

const routingViewPage: RouteConfig = {
  path: `/routing/:id`,
  staticPath: `/routings`,
  name: "routing-view",
  exact: true,
  component: RoutingViewPage,
  redirectPath: "",
  type: "protected",
};

const productTablePage: RouteConfig = {
  path: `/products`,
  staticPath: `/products`,
  name: "product-table",
  exact: true,
  component: ProductTablePage,
  redirectPath: "",
  type: "protected",
};

const productViewPage: RouteConfig = {
  path: `/product/:id`,
  staticPath: `/products`,
  name: "product-view",
  exact: true,
  component: ProductViewPage,
  redirectPath: "",
  type: "protected",
};

const bomTablePage: RouteConfig = {
  path: `/boms`,
  staticPath: `/boms`,
  name: "bom-table",
  exact: true,
  component: BOMTablePage,
  redirectPath: "",
  type: "protected",
};

const productBOMsPage: RouteConfig = {
  path: `/product-boms/:productCode`,
  staticPath: `/product-boms`,
  name: "product-boms",
  exact: true,
  component: ProductBOMsPage,
  redirectPath: "",
  type: "protected",
};

export const screens = [
  homePage,
  formConfigList,
  dynamicForm,
  formCreator,
  loginPage,
  aoiUploadPage,
  processRouteList,
  createRoute,
  productRoute,
  workOrderTable,
  routingTablePage,
  routingViewPage,
  productTablePage,
  productViewPage,
  bomTablePage,
  productBOMsPage
];

export const pageRoutes: { [KEY: string]: string } = {
  GO_TO_HOME_PAGE: homePage.staticPath,
  GO_TO_FORM_CONFIG: formConfigList.staticPath,
  GO_TO_FORM_CREATOR: formCreator.staticPath,
  GO_TO_LOGIN: loginPage.staticPath,
  GO_TO_AOI_SHEET_UPLOAD: aoiUploadPage.staticPath,
  GO_TO__PROCESS_ROUTES_LIST: processRouteList.staticPath,
  GO_TO__CREATE_ROUTE: createRoute.staticPath,
  GO_TO__PRODUCT_ROUTE: productRoute.staticPath,
  GO_TO_SERIAL_TRACKING: productRoute.staticPath,
  GO_TO__WORK_ORDER_TABLE: workOrderTable.staticPath,
  GO_TO_ROUTING_TABLE: routingTablePage.staticPath,
  GO_TO_ROUTING_VIEW: routingViewPage.staticPath,
  GO_TO_PRODUCT_TABLE: productTablePage.staticPath,
  GO_TO_PRODUCT_VIEW: productViewPage.staticPath,
  GO_TO_BOM_TABLE: bomTablePage.staticPath,
  GO_TO_PRODUCT_BOMS: productBOMsPage.staticPath,
};
