export const EDGES = [
  {
    id: "edge-aoi-pb_pcb_unload",
    source: "aoi",
    target: "pb_pcb_unload",
    type: "smoothstep",
    animated: true,
    label: "main",
    style: {
      stroke: "#007bff",
      strokeWidth: 2,
      strokeDasharray: "none",
    },
    labelStyle: {
      fill: "#007bff",
      fontSize: 12,
      fontWeight: "bold",
      backgroundColor: "#ffffff",
      padding: "2px",
      borderRadius: "3px",
    },
    markerEnd: {
      type: "arrowclosed",
    },
    sourceHandle: "bottom",
    targetHandle: "top",
  },
  {
    id: "edge-aoi-pb_rework_1",
    source: "aoi",
    target: "pb_rework_1",
    type: "smoothstep",
    animated: true,
    label: "Condition: equals",
    style: {
      stroke: "#f1c40f",
      strokeWidth: 2,
      strokeDasharray: "none",
    },
    labelStyle: {
      fill: "#f1c40f",
      fontSize: 12,
      fontWeight: "bold",
      backgroundColor: "#ffffff",
      padding: "2px",
      borderRadius: "3px",
    },
    markerEnd: {
      type: "arrowclosed",
    },
    sourceHandle: "bottom",
    targetHandle: "top",
  },
  {
    id: "edge-fpt-pb_testing",
    source: "fpt",
    target: "pb_testing",
    type: "smoothstep",
    animated: true,
    label: "main",
    style: {
      stroke: "#007bff",
      strokeWidth: 2,
      strokeDasharray: "none",
    },
    labelStyle: {
      fill: "#007bff",
      fontSize: 12,
      fontWeight: "bold",
      backgroundColor: "#ffffff",
      padding: "2px",
      borderRadius: "3px",
    },
    markerEnd: {
      type: "arrowclosed",
    },
    sourceHandle: "bottom",
    targetHandle: "top",
  },
  {
    id: "edge-pdi-sap_confirmation",
    source: "pdi",
    target: "sap_confirmation",
    type: "smoothstep",
    animated: true,
    label: "main",
    style: {
      stroke: "#007bff",
      strokeWidth: 2,
      strokeDasharray: "none",
    },
    labelStyle: {
      fill: "#007bff",
      fontSize: 12,
      fontWeight: "bold",
      backgroundColor: "#ffffff",
      padding: "2px",
      borderRadius: "3px",
    },
    markerEnd: {
      type: "arrowclosed",
    },
    sourceHandle: "bottom",
    targetHandle: "top",
  },
  {
    id: "edge-spi-pick_place",
    source: "spi",
    target: "pick_place",
    type: "smoothstep",
    animated: true,
    label: "main",
    style: {
      stroke: "#007bff",
      strokeWidth: 2,
      strokeDasharray: "none",
    },
    labelStyle: {
      fill: "#007bff",
      fontSize: 12,
      fontWeight: "bold",
      backgroundColor: "#ffffff",
      padding: "2px",
      borderRadius: "3px",
    },
    markerEnd: {
      type: "arrowclosed",
    },
    sourceHandle: "bottom",
    targetHandle: "top",
  },
  {
    id: "edge-reflow-aoi",
    source: "reflow",
    target: "aoi",
    type: "smoothstep",
    animated: true,
    label: "main",
    style: {
      stroke: "#007bff",
      strokeWidth: 2,
      strokeDasharray: "none",
    },
    labelStyle: {
      fill: "#007bff",
      fontSize: 12,
      fontWeight: "bold",
      backgroundColor: "#ffffff",
      padding: "2px",
      borderRadius: "3px",
    },
    markerEnd: {
      type: "arrowclosed",
    },
    sourceHandle: "bottom",
    targetHandle: "top",
  },
  {
    id: "edge-touch_up-pb_post_wave",
    source: "touch_up",
    target: "pb_post_wave",
    type: "smoothstep",
    animated: true,
    label: "main",
    style: {
      stroke: "#007bff",
      strokeWidth: 2,
      strokeDasharray: "none",
    },
    labelStyle: {
      fill: "#007bff",
      fontSize: 12,
      fontWeight: "bold",
      backgroundColor: "#ffffff",
      padding: "2px",
      borderRadius: "3px",
    },
    markerEnd: {
      type: "arrowclosed",
    },
    sourceHandle: "bottom",
    targetHandle: "top",
  },
  {
    id: "edge-pb_depanel-pb_thc_mounting",
    source: "pb_depanel",
    target: "pb_thc_mounting",
    type: "smoothstep",
    animated: true,
    label: "main",
    style: {
      stroke: "#007bff",
      strokeWidth: 2,
      strokeDasharray: "none",
    },
    labelStyle: {
      fill: "#007bff",
      fontSize: 12,
      fontWeight: "bold",
      backgroundColor: "#ffffff",
      padding: "2px",
      borderRadius: "3px",
    },
    markerEnd: {
      type: "arrowclosed",
    },
    sourceHandle: "bottom",
    targetHandle: "top",
  },
  {
    id: "edge-pb_testing-conformal_coating",
    source: "pb_testing",
    target: "conformal_coating",
    type: "smoothstep",
    animated: true,
    label: "main",
    style: {
      stroke: "#007bff",
      strokeWidth: 2,
      strokeDasharray: "none",
    },
    labelStyle: {
      fill: "#007bff",
      fontSize: 12,
      fontWeight: "bold",
      backgroundColor: "#ffffff",
      padding: "2px",
      borderRadius: "3px",
    },
    markerEnd: {
      type: "arrowclosed",
    },
    sourceHandle: "bottom",
    targetHandle: "top",
  },
  {
    id: "edge-pb_testing-pb_rework_1",
    source: "pb_testing",
    target: "pb_rework_1",
    type: "smoothstep",
    animated: true,
    label: "Condition: equals",
    style: {
      stroke: "#f1c40f",
      strokeWidth: 2,
      strokeDasharray: "none",
    },
    labelStyle: {
      fill: "#f1c40f",
      fontSize: 12,
      fontWeight: "bold",
      backgroundColor: "#ffffff",
      padding: "2px",
      borderRadius: "3px",
    },
    markerEnd: {
      type: "arrowclosed",
    },
    sourceHandle: "bottom",
    targetHandle: "top",
  },
  {
    id: "edge-pick_place-reflow",
    source: "pick_place",
    target: "reflow",
    type: "smoothstep",
    animated: true,
    label: "main",
    style: {
      stroke: "#007bff",
      strokeWidth: 2,
      strokeDasharray: "none",
    },
    labelStyle: {
      fill: "#007bff",
      fontSize: 12,
      fontWeight: "bold",
      backgroundColor: "#ffffff",
      padding: "2px",
      borderRadius: "3px",
    },
    markerEnd: {
      type: "arrowclosed",
    },
    sourceHandle: "bottom",
    targetHandle: "top",
  },
  {
    id: "edge-pb_pre_wave-wave_soldring",
    source: "pb_pre_wave",
    target: "wave_soldring",
    type: "smoothstep",
    animated: true,
    label: "main",
    style: {
      stroke: "#007bff",
      strokeWidth: 2,
      strokeDasharray: "none",
    },
    labelStyle: {
      fill: "#007bff",
      fontSize: 12,
      fontWeight: "bold",
      backgroundColor: "#ffffff",
      padding: "2px",
      borderRadius: "3px",
    },
    markerEnd: {
      type: "arrowclosed",
    },
    sourceHandle: "bottom",
    targetHandle: "top",
  },
  {
    id: "edge-pb_rework_1-aoi",
    source: "pb_rework_1",
    target: "aoi",
    type: "smoothstep",
    animated: true,
    label: "rework",
    style: {
      stroke: "#e74c3c",
      strokeWidth: 3,
      strokeDasharray: "5,5",
    },
    labelStyle: {
      fill: "#e74c3c",
      fontSize: 12,
      fontWeight: "bold",
      backgroundColor: "#ffffff",
      padding: "2px",
      borderRadius: "3px",
    },
    markerEnd: {
      type: "arrowclosed",
    },
    sourceHandle: "bottom",
    targetHandle: "top",
  },
  {
    id: "edge-pb_rework_1-aoi",
    source: "pb_rework_1",
    target: "aoi",
    type: "smoothstep",
    animated: true,
    label: "Condition: equals",
    style: {
      stroke: "#f1c40f",
      strokeWidth: 3,
      strokeDasharray: "5,5",
    },
    labelStyle: {
      fill: "#f1c40f",
      fontSize: 12,
      fontWeight: "bold",
      backgroundColor: "#ffffff",
      padding: "2px",
      borderRadius: "3px",
    },
    markerEnd: {
      type: "arrowclosed",
    },
    sourceHandle: "bottom",
    targetHandle: "top",
  },
  {
    id: "edge-pb_rework_1-pb_rework_analysis",
    source: "pb_rework_1",
    target: "pb_rework_analysis",
    type: "smoothstep",
    animated: true,
    label: "Condition: equals",
    style: {
      stroke: "#f1c40f",
      strokeWidth: 3,
      strokeDasharray: "5,5",
    },
    labelStyle: {
      fill: "#f1c40f",
      fontSize: 12,
      fontWeight: "bold",
      backgroundColor: "#ffffff",
      padding: "2px",
      borderRadius: "3px",
    },
    markerEnd: {
      type: "arrowclosed",
    },
    sourceHandle: "bottom",
    targetHandle: "top",
  },
  {
    id: "edge-lead_cutting-touch_up",
    source: "lead_cutting",
    target: "touch_up",
    type: "smoothstep",
    animated: true,
    label: "main",
    style: {
      stroke: "#007bff",
      strokeWidth: 2,
      strokeDasharray: "none",
    },
    labelStyle: {
      fill: "#007bff",
      fontSize: 12,
      fontWeight: "bold",
      backgroundColor: "#ffffff",
      padding: "2px",
      borderRadius: "3px",
    },
    markerEnd: {
      type: "arrowclosed",
    },
    sourceHandle: "bottom",
    targetHandle: "top",
  },
  {
    id: "edge-pb_post_wave-sap_confirmation",
    source: "pb_post_wave",
    target: "sap_confirmation",
    type: "smoothstep",
    animated: true,
    label: "main",
    style: {
      stroke: "#007bff",
      strokeWidth: 2,
      strokeDasharray: "none",
    },
    labelStyle: {
      fill: "#007bff",
      fontSize: 12,
      fontWeight: "bold",
      backgroundColor: "#ffffff",
      padding: "2px",
      borderRadius: "3px",
    },
    markerEnd: {
      type: "arrowclosed",
    },
    sourceHandle: "bottom",
    targetHandle: "top",
  },
  {
    id: "edge-laser_marking-solder_paste_printing",
    source: "laser_marking",
    target: "solder_paste_printing",
    type: "smoothstep",
    animated: true,
    label: "main",
    style: {
      stroke: "#007bff",
      strokeWidth: 2,
      strokeDasharray: "none",
    },
    labelStyle: {
      fill: "#007bff",
      fontSize: 12,
      fontWeight: "bold",
      backgroundColor: "#ffffff",
      padding: "2px",
      borderRadius: "3px",
    },
    markerEnd: {
      type: "arrowclosed",
    },
    sourceHandle: "bottom",
    targetHandle: "top",
  },
  {
    id: "edge-move_to_store-end",
    source: "move_to_store",
    target: "end",
    type: "smoothstep",
    animated: true,
    label: "main",
    style: {
      stroke: "#007bff",
      strokeWidth: 2,
      strokeDasharray: "none",
    },
    labelStyle: {
      fill: "#007bff",
      fontSize: 12,
      fontWeight: "bold",
      backgroundColor: "#ffffff",
      padding: "2px",
      borderRadius: "3px",
    },
    markerEnd: {
      type: "arrowclosed",
    },
    sourceHandle: "bottom",
    targetHandle: "top",
  },
  {
    id: "edge-pb_pcb_unload-pb_depanel",
    source: "pb_pcb_unload",
    target: "pb_depanel",
    type: "smoothstep",
    animated: true,
    label: "main",
    style: {
      stroke: "#007bff",
      strokeWidth: 2,
      strokeDasharray: "none",
    },
    labelStyle: {
      fill: "#007bff",
      fontSize: 12,
      fontWeight: "bold",
      backgroundColor: "#ffffff",
      padding: "2px",
      borderRadius: "3px",
    },
    markerEnd: {
      type: "arrowclosed",
    },
    sourceHandle: "bottom",
    targetHandle: "top",
  },
  {
    id: "edge-wave_soldring-lead_cutting",
    source: "wave_soldring",
    target: "lead_cutting",
    type: "smoothstep",
    animated: true,
    label: "main",
    style: {
      stroke: "#007bff",
      strokeWidth: 2,
      strokeDasharray: "none",
    },
    labelStyle: {
      fill: "#007bff",
      fontSize: 12,
      fontWeight: "bold",
      backgroundColor: "#ffffff",
      padding: "2px",
      borderRadius: "3px",
    },
    markerEnd: {
      type: "arrowclosed",
    },
    sourceHandle: "bottom",
    targetHandle: "top",
  },
  {
    id: "edge-pb_thc_mounting-pb_pre_wave",
    source: "pb_thc_mounting",
    target: "pb_pre_wave",
    type: "smoothstep",
    animated: true,
    label: "main",
    style: {
      stroke: "#007bff",
      strokeWidth: 2,
      strokeDasharray: "none",
    },
    labelStyle: {
      fill: "#007bff",
      fontSize: 12,
      fontWeight: "bold",
      backgroundColor: "#ffffff",
      padding: "2px",
      borderRadius: "3px",
    },
    markerEnd: {
      type: "arrowclosed",
    },
    sourceHandle: "bottom",
    targetHandle: "top",
  },
  {
    id: "edge-pcb_load_feeder-laser_marking",
    source: "pcb_load_feeder",
    target: "laser_marking",
    type: "smoothstep",
    animated: true,
    label: "main",
    style: {
      stroke: "#007bff",
      strokeWidth: 2,
      strokeDasharray: "none",
    },
    labelStyle: {
      fill: "#007bff",
      fontSize: 12,
      fontWeight: "bold",
      backgroundColor: "#ffffff",
      padding: "2px",
      borderRadius: "3px",
    },
    markerEnd: {
      type: "arrowclosed",
    },
    sourceHandle: "bottom",
    targetHandle: "top",
  },
  {
    id: "edge-sap_confirmation-fpt",
    source: "sap_confirmation",
    target: "fpt",
    type: "smoothstep",
    animated: true,
    label: "main",
    style: {
      stroke: "#007bff",
      strokeWidth: 2,
      strokeDasharray: "none",
    },
    labelStyle: {
      fill: "#007bff",
      fontSize: 12,
      fontWeight: "bold",
      backgroundColor: "#ffffff",
      padding: "2px",
      borderRadius: "3px",
    },
    markerEnd: {
      type: "arrowclosed",
    },
    sourceHandle: "bottom",
    targetHandle: "top",
  },
  {
    id: "edge-sap_confirmation-move_to_store",
    source: "sap_confirmation",
    target: "move_to_store",
    type: "smoothstep",
    animated: true,
    label: "Condition: equals",
    style: {
      stroke: "#f1c40f",
      strokeWidth: 2,
      strokeDasharray: "none",
    },
    labelStyle: {
      fill: "#f1c40f",
      fontSize: 12,
      fontWeight: "bold",
      backgroundColor: "#ffffff",
      padding: "2px",
      borderRadius: "3px",
    },
    markerEnd: {
      type: "arrowclosed",
    },
    sourceHandle: "bottom",
    targetHandle: "top",
  },
  {
    id: "edge-conformal_coating-conformal_inspection",
    source: "conformal_coating",
    target: "conformal_inspection",
    type: "smoothstep",
    animated: true,
    label: "main",
    style: {
      stroke: "#007bff",
      strokeWidth: 2,
      strokeDasharray: "none",
    },
    labelStyle: {
      fill: "#007bff",
      fontSize: 12,
      fontWeight: "bold",
      backgroundColor: "#ffffff",
      padding: "2px",
      borderRadius: "3px",
    },
    markerEnd: {
      type: "arrowclosed",
    },
    sourceHandle: "bottom",
    targetHandle: "top",
  },
  {
    id: "edge-pb_rework_analysis-pb_testing",
    source: "pb_rework_analysis",
    target: "pb_testing",
    type: "smoothstep",
    animated: true,
    label: "rework",
    style: {
      stroke: "#e74c3c",
      strokeWidth: 3,
      strokeDasharray: "5,5",
    },
    labelStyle: {
      fill: "#e74c3c",
      fontSize: 12,
      fontWeight: "bold",
      backgroundColor: "#ffffff",
      padding: "2px",
      borderRadius: "3px",
    },
    markerEnd: {
      type: "arrowclosed",
    },
    sourceHandle: "bottom",
    targetHandle: "top",
  },
  {
    id: "edge-conformal_inspection-pdi",
    source: "conformal_inspection",
    target: "pdi",
    type: "smoothstep",
    animated: true,
    label: "main",
    style: {
      stroke: "#007bff",
      strokeWidth: 2,
      strokeDasharray: "none",
    },
    labelStyle: {
      fill: "#007bff",
      fontSize: 12,
      fontWeight: "bold",
      backgroundColor: "#ffffff",
      padding: "2px",
      borderRadius: "3px",
    },
    markerEnd: {
      type: "arrowclosed",
    },
    sourceHandle: "bottom",
    targetHandle: "top",
  },
  {
    id: "edge-solder_paste_printing-spi",
    source: "solder_paste_printing",
    target: "spi",
    type: "smoothstep",
    animated: true,
    label: "main",
    style: {
      stroke: "#007bff",
      strokeWidth: 2,
      strokeDasharray: "none",
    },
    labelStyle: {
      fill: "#007bff",
      fontSize: 12,
      fontWeight: "bold",
      backgroundColor: "#ffffff",
      padding: "2px",
      borderRadius: "3px",
    },
    markerEnd: {
      type: "arrowclosed",
    },
    sourceHandle: "bottom",
    targetHandle: "top",
  },
];

export const NODE = [
  {
    id: "pcb_load_feeder",
    type: "customNode",
    data: {
      label: "pcb_load_feeder",
      type: "machine",
      isStart: true,
      isEnd: false,
    },
    position: {
      x: 0,
      y: 0,
    },
    style: {
      padding: 10,
      borderRadius: 8,
      color: "#fff",
      textAlign: "center",
      backgroundColor: "#16a085",
      fontWeight: "bold",
    },
  },
  {
    id: "laser_marking",
    type: "customNode",
    data: {
      label: "laser_marking",
      type: "machine",
      isStart: false,
      isEnd: false,
    },
    position: {
      x: 400,
      y: 0,
    },
    style: {
      padding: 10,
      borderRadius: 8,
      color: "#fff",
      textAlign: "center",
      backgroundColor: "#3498db",
    },
  },
  {
    id: "solder_paste_printing",
    type: "customNode",
    data: {
      label: "solder_paste_printing",
      type: "machine",
      isStart: false,
      isEnd: false,
    },
    position: {
      x: 700,
      y: 0,
    },
    style: {
      padding: 10,
      borderRadius: 8,
      color: "#fff",
      textAlign: "center",
      backgroundColor: "#3498db",
    },
  },
  {
    id: "spi",
    type: "customNode",
    data: {
      label: "spi",
      type: "machine",
      isStart: false,
      isEnd: false,
    },
    position: {
      x: 1100,
      y: 0,
    },
    style: {
      padding: 10,
      borderRadius: 8,
      color: "#fff",
      textAlign: "center",
      backgroundColor: "#3498db",
    },
  },
  {
    id: "pick_place",
    type: "customNode",
    data: {
      label: "pick_place",
      type: "machine",
      isStart: false,
      isEnd: false,
    },
    position: {
      x: 1300,
      y: 0,
    },
    style: {
      padding: 10,
      borderRadius: 8,
      color: "#fff",
      textAlign: "center",
      backgroundColor: "#3498db",
    },
  },
  {
    id: "reflow",
    type: "customNode",
    data: {
      label: "reflow",
      type: "machine",
      isStart: false,
      isEnd: false,
    },
    position: {
      x: 0,
      y: 400,
    },
    style: {
      padding: 10,
      borderRadius: 8,
      color: "#fff",
      textAlign: "center",
      backgroundColor: "#3498db",
    },
  },
  {
    id: "aoi",
    type: "customNode",
    data: {
      label: "aoi",
      type: "machine",
      isStart: false,
      isEnd: false,
    },
    position: {
      x: 300,
      y: 400,
    },
    style: {
      padding: 10,
      borderRadius: 8,
      color: "#fff",
      textAlign: "center",
      backgroundColor: "#3498db",
    },
  },
  {
    id: "pb_pcb_unload",
    type: "customNode",
    data: {
      label: "pb_pcb_unload",
      type: "machine",
      isStart: false,
      isEnd: false,
    },
    position: {
      x: 600,
      y: 400,
    },
    style: {
      padding: 10,
      borderRadius: 8,
      color: "#fff",
      textAlign: "center",
      backgroundColor: "#3498db",
    },
  },
  {
    id: "pb_depanel",
    type: "customNode",
    data: {
      label: "pb_depanel",
      type: "machine",
      isStart: false,
      isEnd: false,
    },
    position: {
      x: 900,
      y: 400,
    },
    style: {
      padding: 10,
      borderRadius: 8,
      color: "#fff",
      textAlign: "center",
      backgroundColor: "#3498db",
    },
  },
  {
    id: "pb_thc_mounting",
    type: "customNode",
    data: {
      label: "pb_thc_mounting",
      type: "machine",
      isStart: false,
      isEnd: false,
    },
    position: {
      x: 1200,
      y: 400,
    },
    style: {
      padding: 10,
      borderRadius: 8,
      color: "#fff",
      textAlign: "center",
      backgroundColor: "#3498db",
    },
  },
  {
    id: "pb_pre_wave",
    type: "customNode",
    data: {
      label: "pb_pre_wave",
      type: "machine",
      isStart: false,
      isEnd: false,
    },
    position: {
      x: 0,
      y: 600,
    },
    style: {
      padding: 10,
      borderRadius: 8,
      color: "#fff",
      textAlign: "center",
      backgroundColor: "#3498db",
    },
  },
  {
    id: "wave_soldring",
    type: "customNode",
    data: {
      label: "wave_soldring",
      type: "machine",
      isStart: false,
      isEnd: false,
    },
    position: {
      x: 300,
      y: 600,
    },
    style: {
      padding: 10,
      borderRadius: 8,
      color: "#fff",
      textAlign: "center",
      backgroundColor: "#3498db",
    },
  },
  {
    id: "lead_cutting",
    type: "customNode",
    data: {
      label: "lead_cutting",
      type: "machine",
      isStart: false,
      isEnd: false,
    },
    position: {
      x: 600,
      y: 600,
    },
    style: {
      padding: 10,
      borderRadius: 8,
      color: "#fff",
      textAlign: "center",
      backgroundColor: "#3498db",
    },
  },
  {
    id: "touch_up",
    type: "customNode",
    data: {
      label: "touch_up",
      type: "machine",
      isStart: false,
      isEnd: false,
    },
    position: {
      x: 900,
      y: 600,
    },
    style: {
      padding: 10,
      borderRadius: 8,
      color: "#fff",
      textAlign: "center",
      backgroundColor: "#3498db",
    },
  },
  {
    id: "pb_post_wave",
    type: "customNode",
    data: {
      label: "pb_post_wave",
      type: "machine",
      isStart: false,
      isEnd: false,
    },
    position: {
      x: 1200,
      y: 600,
    },
    style: {
      padding: 10,
      borderRadius: 8,
      color: "#fff",
      textAlign: "center",
      backgroundColor: "#3498db",
    },
  },
  {
    id: "sap_confirmation",
    type: "customNode",
    data: {
      label: "sap_confirmation",
      type: "machine",
      isStart: false,
      isEnd: false,
    },
    position: {
      x: 0,
      y: 800,
    },
    style: {
      padding: 10,
      borderRadius: 8,
      color: "#fff",
      textAlign: "center",
      backgroundColor: "#3498db",
    },
  },
  {
    id: "fpt",
    type: "customNode",
    data: {
      label: "fpt",
      type: "machine",
      isStart: false,
      isEnd: false,
    },
    position: {
      x: 300,
      y: 800,
    },
    style: {
      padding: 10,
      borderRadius: 8,
      color: "#fff",
      textAlign: "center",
      backgroundColor: "#3498db",
    },
  },
  {
    id: "pb_testing",
    type: "customNode",
    data: {
      label: "pb_testing",
      type: "machine",
      isStart: false,
      isEnd: false,
    },
    position: {
      x: 600,
      y: 800,
    },
    style: {
      padding: 10,
      borderRadius: 8,
      color: "#fff",
      textAlign: "center",
      backgroundColor: "#3498db",
    },
  },
  {
    id: "conformal_coating",
    type: "customNode",
    data: {
      label: "conformal_coating",
      type: "machine",
      isStart: false,
      isEnd: false,
    },
    position: {
      x: 900,
      y: 800,
    },
    style: {
      padding: 10,
      borderRadius: 8,
      color: "#fff",
      textAlign: "center",
      backgroundColor: "#3498db",
    },
  },
  {
    id: "conformal_inspection",
    type: "customNode",
    data: {
      label: "conformal_inspection",
      type: "machine",
      isStart: false,
      isEnd: false,
    },
    position: {
      x: 1200,
      y: 800,
    },
    style: {
      padding: 10,
      borderRadius: 8,
      color: "#fff",
      textAlign: "center",
      backgroundColor: "#3498db",
    },
  },
  {
    id: "pdi",
    type: "customNode",
    data: {
      label: "pdi",
      type: "machine",
      isStart: false,
      isEnd: false,
    },
    position: {
      x: -200,
      y: 1000,
    },
    style: {
      padding: 10,
      borderRadius: 8,
      color: "#fff",
      textAlign: "center",
      backgroundColor: "#3498db",
    },
  },
  {
    id: "pb_rework_1",
    type: "customNode",
    data: {
      label: "pb_rework_1",
      type: "rework_station",
      isStart: false,
      isEnd: false,
    },
    position: {
      x: -100,
      y: 100,
    },
    style: {
      padding: 10,
      borderRadius: 8,
      color: "#fff",
      textAlign: "center",
      backgroundColor: "#e74c3c",
    },
  },
  {
    id: "pb_rework_analysis",
    type: "customNode",
    data: {
      label: "pb_rework_analysis",
      type: "rework_station",
      isStart: false,
      isEnd: false,
    },
    position: {
      x: 850,
      y: 280,
    },
    style: {
      padding: 10,
      borderRadius: 8,
      color: "#fff",
      textAlign: "center",
      backgroundColor: "#e74c3c",
    },
  },
  {
    id: "move_to_store",
    type: "customNode",
    data: {
      label: "move_to_store",
      type: "machine",
      isStart: false,
      isEnd: true,
    },
    position: {
      x: 200,
      y: 1060,
      color: "#fff",
      textAlign: "center",
      backgroundColor: "#8e44ad",
      fontWeight: "bold",
    },
  },
];
