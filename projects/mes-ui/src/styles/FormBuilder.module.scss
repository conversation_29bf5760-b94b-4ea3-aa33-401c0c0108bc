.optionsContainer {
  margin-top: 10px;
  margin-bottom: 10px;
}

.formBuilderContainer {
  max-width: 780px;
  margin: 20px auto;
  padding: 30px;
  background-color: #fff;
  border-radius: 20px;
}

.addOptionButton {
  margin-top: 10px;
}

.fieldItemContent {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.fieldList {
  margin-top: 16px;
}

.fieldItemRow {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.field {
  min-width: 350px;
  max-width: 350px;
}

.errorText {
  display: flex;
  align-items: center;
}
.field_wrapper {
  display: flex;
  align-items: center;
}

.optionItemRow {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.optionField {
  margin-right: 20px;
  min-width: 350px;
}
.addFieldButton {
  margin: 15px 0px;
  width: 180px;
}
