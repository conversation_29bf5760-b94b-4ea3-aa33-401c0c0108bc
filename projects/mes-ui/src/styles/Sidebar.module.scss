.drawer {
  background-color: #1a202c; // Dark background like in the image
  color: #ffffff;
  transition: width 0.3s ease, left 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100vh;
  margin-top: 0; // No margin top
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1200; // Keeps sidebar above other content
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

  // For small height screens
  @media (max-height: 700px) {
    overflow-y: auto;
  }

  // For extremely small height screens
  @media (max-height: 500px) {
    overflow-y: auto;

    // Custom scrollbar for webkit browsers
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.05);
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.2);
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }

  &.drawerOpen {
    width: 280px; // Slightly wider
    left: 0;
  }

  @include desktop {
    height: 100vh; // Full height on desktop
    // Always open on desktop
    width: 280px !important;
    left: 0 !important;
    margin-top: 0; // No margin on desktop to take full height
    top: 0;
    padding-top: 0; // No header, so no padding needed
  }

  &.drawerClose {
    width: 70px; // Slightly wider

    .name {
      display: none; // Hide text for closed drawer

      @include desktop {
        display: block; // Always show text on desktop
      }
    }

    @include desktop {
      width: 280px; // Always full width on desktop
    }
  }

  @include tablet {
    left: -280px;
    z-index: 1300; // Higher z-index to appear above content
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);

    &.drawerOpen {
      left: 0;
    }
  }

  @include mobile {
    left: -280px;
    z-index: 1300; // Higher z-index to appear above content
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);

    &.drawerOpen {
      left: 0;
    }
  }
}

// Toolbar (Mobile/Tablet Close Button)
.toolbar {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 16px;
  border-bottom: 1px solid #52575d;
}

// Sidebar Menu
.branding {
  font-weight: 500;
  border-radius: 4px;
  transition: all 0.2s ease;
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", Segoe UI Symbol, "Noto Color Emoji";
  color: rgb(255, 255, 255);
  font-size: 24px;
  padding: 24px;

  @media (max-height: 700px) {
    margin-top: 10px;
    margin-bottom: 5px;
  }
}

.stationInfo {
  padding: 0 20px;
  margin-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 15px;
}

.menu {
  list-style: none;
  padding: 0;
  margin: 0;
  flex-grow: 1;

  // For small height screens
  @media (max-height: 700px) {
    overflow-y: auto;

    // Custom scrollbar for webkit browsers
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.05);
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.2);
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }
}

.operatorInfo {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  margin-top: auto;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  flex-wrap: wrap;

  // For small height screens
  @media (max-height: 700px) {
    padding: 10px 20px;
  }

  .operatorAvatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: #4299e1;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 12px;

    // For small height screens
    @media (max-height: 700px) {
      width: 30px;
      height: 30px;
      margin-right: 10px;
    }
  }

  .operatorDetails {
    display: flex;
    flex-direction: column;
    flex: 1;
    color: rgb(255, 255, 255);
    font-weight: 400;
    border-radius: 4px;
    transition: all 0.2s ease;
    line-height: 24px;
    font-size: 16px;
    font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", Segoe UI Symbol, "Noto Color Emoji";

  }

  .logoutButton {
    color: rgb(255, 255, 255);
    font-weight: 400;
    border-radius: 4px;
    transition: all 0.2s ease;
    line-height: 24px;
    font-size: 16px;
    font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", Segoe UI Symbol, "Noto Color Emoji";
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 8px 12px;
    margin-top: 12px;
    width: 100%;
    border-radius: 4px;
    transition: background-color 0.2s;
    margin-left: 0;

    // For small height screens
    @media (max-height: 700px) {
      padding: 6px 12px;
      margin-top: 8px;
    }

    &:hover {
      background-color: rgba(255, 255, 255, 0.08);
    }
  }
}

// Menu Items
.menuItem {
  margin: 2px 12px;
  display: flex;
  align-items: center;
  border-radius: 4px;
  overflow: hidden;

  // For small height screens
  @media (max-height: 700px) {
    margin: 1px 12px;
  }

  .link {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 12px 16px;
    text-decoration: none;
    color: rgb(255, 255, 255);
    font-weight: 400;
    border-radius: 4px;
    transition: all 0.2s ease;
    line-height: 24px;
    font-size: 16px;
    font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", Segoe UI Symbol, "Noto Color Emoji";
    letter-spacing: 0.1px;
    margin: 2px 0;

    // For small height screens
    @media (max-height: 700px) {
      padding: 8px 16px;
      margin: 1px 0;
    }

    // For extremely small height screens
    @media (max-height: 500px) {
      padding: 6px 16px;
      margin: 0;
    }

    &:hover {
      background-color: #2563eb;
      color: rgb(255, 255, 255);
    }

    &.active {
      background-color: #2563eb;
      color: rgb(255, 255, 255);
      font-weight: 500;

      .icon {
        color: rgb(255, 255, 255);
      }
    }
  }

  .icon {
    margin-right: 14px;
    color: #a0aec0;
    min-width: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

// Logout Button
.logout {
  background-color: transparent;
  border: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  transition: background-color 0.3s ease, transform 0.2s ease;

  color: rgb(209, 213, 219);
  font-weight: 400;
  border-radius: 4px;
  transition: all 0.2s ease;
  line-height: 24px;
  font-size: 16px;
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", Segoe UI Symbol, "Noto Color Emoji";

  &:hover {
    background-color: #b91c1c;
    color: #ffffff;
    transform: scale(1.02);
  }

  .icon {
    margin-right: 16px;
    font-size: 1.5rem;
  }
}

// Menu Button (For Mobile & Tablet)
.menuButton {
  position: fixed;
  top: 20px;
  left: 15px;
  z-index: 1400; // Ensures menu button is always on top
  background: transparent;
  border: none;
  cursor: pointer;
}

// Overlay to Close Sidebar on Mobile
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1100;
  display: none;
}

.overlayVisible {
  display: block;
}