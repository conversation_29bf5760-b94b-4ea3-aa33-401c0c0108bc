.custom-node {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
  border-radius: 8px;
  min-width: 120px;
  text-align: center;
  box-shadow: 2px 2px 8px rgba(0, 0, 0, 0.2);
  font-family: Arial, sans-serif;
  font-size: 14px;
  position: relative;

  .node-content {
    padding: 10px;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.8);
    width: 100%;
  }

  .node-type {
    font-size: 12px;
    color: #555;
  }

  // Style for connection handles
  :global(.react-flow__handle) {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #1976d2;
    border: 2px solid white;
    transition: all 0.2s ease;

    &:hover {
      background-color: #2196f3;
      transform: scale(1.2);
    }
  }

  // Highlight valid connection points
  :global(.react-flow__handle-valid) {
    background-color: #4caf50;
  }

  // Highlight invalid connection points
  :global(.react-flow__handle-connecting) {
    background-color: #ff9800;
  }
}

// Custom colors based on node type
.machine {
  background: #3498db;
  color: white;
}

.scanner {
  background: #2ecc71;
  color: white;
}

.rework_station {
  background: #e74c3c;
  color: white;
}

.computer {
  background: #f39c12;
  color: white;
}

.unknown {
  background: #95a5a6;
  border: 2px dashed #666;
}