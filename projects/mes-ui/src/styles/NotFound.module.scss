.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  text-align: center;
  background-color: #f8f9fa;
}

.title {
  font-size: 72px; /* Large title */
  color: #dc3545; /* Bootstrap danger color */
}

.subtitle {
  font-size: 24px; /* Subtitle size */
  margin: 10px 0;
  color: #343a40; /* Dark text color */
}

.message {
  font-size: 18px;
  margin-bottom: 20px;
  color: #6c757d;
}

.button {
  padding: 10px 20px;
  font-size: 18px;
  color: #fff;
  background-color: #007bff;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s;

  &:hover {
    background-color: #0056b3;
  }
}
