/* UserMenu.module.scss */
.menu {
  .MuiMenu-paper {
    border-radius: 8px; /* Rounded corners for the menu */
    padding: 10px; /* Padding around menu items */
    min-width: 180px; /* Minimum width for the menu */
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1); /* Subtle shadow */
  }
}

.menuItem {
  padding: 10px 16px;
  border-radius: 6px; /* Rounded corners for menu items */
  transition: background-color 0.2s ease; /* Smooth transition for hover effect */
  &:hover {
    background-color: #f5f5f5; /* Light background on hover */
  }
}

.disabledMenuItem {
  color: #888; /* Grey color for disabled items */
}

.avatar {
  background-color: #3f51b5; /* Color of the avatar */
}
