.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 60px;
  background-color: #ffffff;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0px 24px 0px 16px;
  z-index: 1000; // Ensures the header is above all other components
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
  border-bottom: 1px solid #f0f0f0;

  @include desktop {
    display: none; // Hide header on desktop
  }
}

.userMenu {
  display: flex;
  align-items: center;
  gap: 15px;
}

.leftSection {
  display: flex;
  align-items: center;
}

.centerSection {
  display: flex;
  align-items: center;
  justify-content: center;
}

.title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a202c;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Can<PERSON>ell', '<PERSON><PERSON>', 'Droid Sans', 'Helvetica Neue', sans-serif;
}