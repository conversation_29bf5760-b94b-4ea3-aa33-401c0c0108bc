// Define breakpoints
$mobile-max: 767px; // Mobile: 0 - 767px
$tablet-min: 768px; // Tablet: 768px - 1024px
$tablet-max: 1024px;
$desktop-min: 1025px; // Desktop: 1025px and above

// Mobile Mixin
@mixin mobile {
  @media (max-width: $mobile-max) {
    @content;
  }
}

// Tablet Mixin
@mixin tablet {
  @media (min-width: $tablet-min) and (max-width: $tablet-max) {
    @content;
  }
}

// Desktop Mixin
@mixin desktop {
  @media (min-width: $desktop-min) {
    @content;
  }
}

@mixin button-styles($bg-color, $disabled-bg, $hover-bg) {
  width: 100%;
  max-width: 300px;
  height: 60px;
  background-color: $bg-color !important;
  font-size: 1rem;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
  transition: background 0.3s ease-in-out;

  &:hover {
    background-color: $hover-bg !important;
  }

  &:disabled {
    background-color: $disabled-bg !important;
    cursor: not-allowed;
  }

  @include desktop {
    width: 300px;
  }

  @include tablet {
    max-width: 220px;
    height: 60px;
  }

  @include mobile {
    font-size: 14px;
    height: 50px;
    width: 49%;
    font-size: 13px;
  }
}
