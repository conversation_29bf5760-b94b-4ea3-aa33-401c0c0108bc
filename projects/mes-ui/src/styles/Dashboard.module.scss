.dashboard {
  padding: 20px;
  min-height: 100vh;
  background-color: var(--background-color);
}

.tabsContainer {
  margin-bottom: 20px;
  background: var(--card-background);
  border-radius: 8px;
  padding: 0 20px;
}

.chartGrid {
  display: grid;
  grid-template-columns: repeat(var(--grid-columns, 12), 1fr);
  gap: var(--grid-gap, 16px);
  grid-auto-rows: 1fr; // ensures consistent height across rows
  
}

.chartContainer {
  // flex: 1;
  // min-width: 500px;
  background: var(--card-background);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease-in-out;
  
  &:hover {
    transform: translateY(-5px);
  }
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: var(--background-color);
}

.errorContainer {
  padding: 20px;
  min-height: 100vh;
  background-color: var(--background-color);
}

.darkMode {
  .chartContainer {
    background: var(--dark-card-background);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
}

@media (max-width: 768px) {
  .chartContainer {
    min-width: 100%;
  }
}
