.stepContainer {
  width: 100%;
  overflow-x: auto; /* Enable horizontal scrolling */
  white-space: nowrap; /* Prevent wrapping */
  padding: 10px 0;
  display: flex;
  justify-content: flex-start; /* Align content to the left */

  /* Hide scrollbar completely */
  scrollbar-width: none; /* Firefox */

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari */
  }
  @include desktop {
    max-width: 1200px;
    margin: auto;
  }
}

.stepScroll {
  display: flex;
  align-items: center;
  flex-wrap: nowrap; /* Prevent wrapping */
  min-width: fit-content; /* Ensure it fits content */
  padding: 10px;
  gap: 0; /* No extra gap between steps */
}

/* Step Boxes */
.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 120px;
  padding: 10px;
  border-radius: 10px;
  text-align: center;
  font-size: 14px;
  background: white;
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
  position: relative;
  border: 2px solid transparent;
  z-index: 2;
  word-wrap: break-word;
  overflow-wrap: break-word;
  text-overflow: ellipsis;
}

.connector {
  width: 40px;
  height: 3px;
  background: #28a745; /* Default green */
  position: relative;
  top: 6%;
  transform: translateY(-50%);
  z-index: 1;
}

/* Green Connectors for Visited Nodes */
.visited {
  background: #28a745;
}

/* Faded Connector Before Pending Node */
.pendingLine {
  background: #6c757d;
  opacity: 0.5;
}

/* Step Status */
.success {
  border: 2px solid #28a745;
  color: #28a745;
}

.failed {
  border: 2px solid #dc3545;
  color: #dc3545;
}

// .pending {
//   border: 2px dashed #ffc107;
//   color: #ffc107;
// }

.next {
  border: 2px dashed #ffc107;
  color: #ffc107;
}

/* Icons */
.icon {
  font-size: 24px;
  margin-bottom: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.successIcon {
  color: #28a745;
}

.failedIcon {
  color: #dc3545;
}

.pendingIcon {
  color: #ffc107;
}

.skeletonContainer {
  display: flex;
  gap: 12px;
  padding: 10px;
  /* Hide scrollbar completely */
  scrollbar-width: none; /* Firefox */

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari */
  }
  overflow-x: auto;
  @include desktop {
    max-width: 1200px;
    margin: auto;
  }
}

.skeletonStep {
  display: flex;
  align-items: center;
  gap: 8px;
}

.skeletonIcon {
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(90deg, #e0e0e0 25%, #f0f0f0 50%, #e0e0e0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite linear;
}

.skeletonLabel {
  width: 210px;
  height: 51px;
  border-radius: 5px;
  background: linear-gradient(90deg, #e0e0e0 25%, #f0f0f0 50%, #e0e0e0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite linear;
}

@keyframes shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.errorMessage {
  color: red;
  text-align: center;
  font-weight: bold;
}

.success_label {
  color: #28a745;
}
