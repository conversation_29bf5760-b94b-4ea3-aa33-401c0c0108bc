.eventListContainer {
  width: 100%; // Ensures it takes full width
  margin: auto;
  padding: 0; // Remove padding to match the screenshot
  overflow-x: auto;

  @include mobile {
    max-width: 100%; // Ensures no hardcoded width
    padding: 0;
    overflow-x: scroll; // Enables scrolling when needed
  }

  @include tablet {
    max-width: 100%;
    padding: 0;
  }
}

.tableContainer {
  width: 100%;
  overflow-x: auto; // Ensures scrolling when table overflows
  background-color: #fff;
  border-radius: 8px; // Improved border radius
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05); // Subtle shadow for depth
  border: 1px solid #e2e8f0; // Light border
}

.tableWrapper {
  width: 100%;
  overflow-x: auto; // Ensures table is scrollable
}

table {
  width: 100%;
  border-collapse: collapse;
  min-width: 800px; // Ensures table scrolls instead of shrinking

  @include mobile {
    min-width: 600px; // Allows horizontal scrolling
    display: block;
  }
}

th {
  font-weight: 600;
  background-color: #f8fafc; // Light gray background for header
  padding: 10px 16px; // Adjusted padding to match reference UI
  text-align: left;
  border-bottom: 1px solid #e2e8f0;
  white-space: nowrap; // Prevents breaking into new lines
  color: rgb(2, 8, 23); // Updated to match the primary color
  font-size: 16px; // Increased font size to 16px
  letter-spacing: 0.2px;
  height: 48px; // Fixed height for headers
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  line-height: 24px;
  text-transform: uppercase; // Make column headers uppercase

  &:first-child {
    border-top-left-radius: 8px;
  }

  &:last-child {
    border-top-right-radius: 8px;
  }
}

tr {
  transition: all 0.2s ease;
  height: 48px; // Fixed height for rows
}

tr:hover {
  background-color: #f1f5f9;
  cursor: pointer;

  &:last-child td {
    border-bottom: none;
  }
}

th,
td {
  white-space: nowrap; // Prevents wrapping
  padding: 10px 16px; // Adjusted padding to match reference UI
  text-align: left;
  border-bottom: 1px solid #e2e8f0; // Slightly darker border
  height: 48px; // Fixed height for consistent row heights

  @include mobile {
    padding: 10px;
  }
}

th {
  // Header specific styles are already defined above
}

td {
  // Column value styling
  font-size: 16px;
  color: rgb(2, 8, 23);
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  line-height: 24px;
  font-weight: 400;
}

// Style the action buttons to match the design
.actionButton {
  background-color: #000;
  color: white;
  border: none;
  border-radius: 3px;
  padding: 6px 12px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  text-transform: none;
  height: 32px;
  box-shadow: none;

  &:hover {
    background-color: #333;
  }
}

// Status indicators
td .status {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  box-shadow: none;
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  line-height: 20px;

  &.in-production {
    background-color: #e6f0ff;
    color: #2563eb;
    border: none;
  }

  &.pending {
    background-color: #fffbeb;
    color: #d97706;
    border: none;
  }

  &.completed {
    background-color: #ecfdf5;
    color: #059669;
    border: none;
  }
}

// 🔹 Fix for Small Screens
@media (max-width: 767px) {
  .tableContainer {
    overflow-x: auto;
  }

  .tableWrapper {
    overflow-x: scroll;
  }

  table {
    min-width: 600px; // Forces scrolling if content is too wide
    display: block;
  }

  th {
    font-size: 16px;
    padding: 6px;
    font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    line-height: 20px;
    color: rgb(2, 8, 23);
    text-transform: uppercase;
  }

  td {
    font-size: 16px;
    padding: 6px;
    font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    line-height: 24px;
    color: rgb(2, 8, 23);
    font-weight: 400;
  }
}