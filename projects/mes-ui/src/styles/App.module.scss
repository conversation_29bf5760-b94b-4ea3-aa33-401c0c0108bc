@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap");

html,
body,
#root,
.app,
.content {
  height: 100%;
  width: 100%;
  font-family: "Roboto", "<PERSON>l", sans-serif;
  font-size: 15px;
  color: #333;
}

.app {
  display: flex;
  position: relative;
}

html {
  overflow: -moz-scrollbars-none;
  -ms-overflow-style: none;
  scrollbar-width: none;
  /* Firefox */
}

html::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari */
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

a {
  text-decoration: none;
  color: black;
}

.main_section {
  flex: 1;
  padding: 16px;
  overflow: hidden;
  transition: margin-left 0.3s ease;

  @include desktop {
    margin-left: 280px; // Match the sidebar width for desktop
    padding: 16px;
    margin-top: 0; // No header
  }

  @include tablet {
    margin-left: 0;
    padding: 12px;
    margin-top: 0; // No header
  }

  @include mobile {
    margin-left: 0;
    padding: 8px;
    margin-top: 0; // No header
  }
}