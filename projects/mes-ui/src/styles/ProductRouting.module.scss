.route_wrapper {
  display: flex;
  justify-content: center;
  @include mobile {
    padding: 10px 10px;
  }
}

.header_text {
  font-size: 18px;
  text-align: center;
  margin-bottom: 20px;
  font-weight: bold;
  @include mobile {
    font-size: 13px;
    margin-bottom: 6px;
  }
}

.routeWrapper {
  display: flex;
  justify-content: center;
  width: 100%;
}

.routeContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 1000px;
  position: relative;
}

.rowContainer {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
  gap: 20px;
  @include mobile {
    gap: 20px;
    margin: 0 8px;
  }
}

.routeStep {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  padding: 10px;
  @include desktop {
    width: 24%;
  }
  @include tablet {
    width: 32%;
  }
  @include mobile {
    width: 49%;
  }
}

.stepBox {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px;
  text-align: center;
  font-size: 14px;
  background: white;
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
  border: 2px solid transparent;
  z-index: 2;
  word-wrap: break-word;
  overflow-wrap: break-word;
  text-overflow: ellipsis;
  justify-content: center;
  border-radius: 5px;
  min-height: 76px;
  min-width: 200px;
  text-align: center;
  @include mobile {
    min-height: 76px;
    min-width: 160px;
  }
}

.success {
  border: 2px solid #28a745;
  color: #28a745;
}

.failed {
  border: 2px solid #dc3545;
  color: #dc3545;
}

.next_executable {
  border: 2px dashed #ffc107;
  color: #ffc107;
}

.pending {
  border: 1px solid rgb(224, 224, 224);
}

/* Icons */
.icon {
  font-size: 24px;
  margin-bottom: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.successIcon {
  color: #28a745;
}

.failedIcon {
  color: #dc3545;
}

.pendingIcon {
  color: #ffc107;
}

/* ✅ Right Arrow (→ for even rows) */
.rightArrow {
  position: absolute;
  // color: #000000;
  @include desktop {
    right: -42px;
    margin-left: 0px;
    top: 36px;
  }
  @include tablet {
    right: -49px;
    margin-left: 0px;
    top: 36px;
  }
  @include mobile {
    right: -42px;
    top: 36px;
  }
}

/* ✅ Left Arrow (← for odd rows) */
.leftArrow {
  position: absolute;
  // color: #020000;
  transform: rotate(180deg);
  @include desktop {
    margin-right: 0px;
    left: -42px;
    top: 36px;
    margin-right: 0px;
  }
  @include tablet {
    left: -49px;
    top: 36px;
    position: absolute;
    margin-left: 0px;
  }
  @include mobile {
    right: 0;
    top: 36px;
    left: -42px;
  }
}

/* ✅ Vertical Arrow (↓ between rows) */
.verticalArrowRight {
  position: absolute;
  // color: #000000;
  @include desktop {
    position: absolute;
    top: 86px;
  }
  @include tablet {
    top: 86px;
  }
  @include mobile {
    // right: 84px;
    top: 86px;
  }
}

/* ✅ Vertical Arrow (↓ between rows) */
.verticalArrowLeft {
  position: absolute;
  // color: #155724;
  @include desktop {
    top: 86px;
  }
  @include tablet {
    margin-top: 0px;
    position: absolute;
    top: 86px;
  }
  @include mobile {
    top: 86px;
    // left: 84px;
  }
}

/* ✅ Common Connector Styles */
.connector {
  position: absolute;
  background-color: gray;
  z-index: 10;
}

// /* ✅ Horizontal Line */
// .horizontalLine {
//   @extend .connector;
//   height: 2px;
//   width: 56px;
//   top: 50%;
//   transform: translateY(-50%);
//   @include tablet {
//     width: 70px;
//   }
//   @include mobile {
//     width: 50px;
//   }
// }

.horizontalLine {
  @extend .connector;
  height: 2px;
  width: calc(100% / 4); // Adjust based on grid spacing
  top: 50%;
  transform: translateY(-50%);

  @include desktop {
    width: calc(100% / 4); // Adjust width dynamically for larger screens
  }

  @include tablet {
    width: calc(100% / 3.8);
  }

  @include mobile {
    width: 50px;
  }
}

/* ✅ Vertical Line */
.verticalLine {
  @extend .connector;
  width: 2px;
  height: 15px;
}

/* ✅ Success Line (Green) */
.successLine {
  background-color: green;
}

/* ✅ Position Adjustments */
.rightLine {
  right: -38px;
  @include tablet {
    right: -45px;
  }
}
.leftLine {
  left: -38px;
  @include tablet {
    left: -45px;
  }
}

.verticalLineRight {
  top: 88px;
}

.verticalLineLeft {
  top: 88px;
}

/* ✅ Mobile Adjustments */
@include mobile {
  .rightLine {
    right: -35px;
  }
  .leftLine {
    left: -35px;
  }

  .verticalLineRight {
    top: 88px;
  }
  .verticalLineLeft {
    top: 88px;
  }
}
