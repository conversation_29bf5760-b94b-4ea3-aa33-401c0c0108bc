.upload-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
  max-width: 100%;
  margin: 20px;
  width: 100%;

  @include mobile {
    max-width: inherit;
    width: 100%;
    max-width: inherit;
    overflow: hidden;
    padding: 15px;
    margin: 0;
  }

  @include tablet {
    padding: 15px;
  }



  .upload-section {
    width: 100%;
    max-width: 100%;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease;

    @media (min-width: 768px) {
      max-width: 500px;
      padding: 25px;
    }

    &:hover {
      transform: translateY(-2px);
    }

    .title {
      font-size: 1.5rem;
      color: #2d3748;
      margin-bottom: 15px;
      font-weight: 600;
      text-align: center;

      @media (min-width: 768px) {
        font-size: 1.75rem;
        margin-bottom: 20px;
      }
    }

    .form {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 15px;

      @media (min-width: 768px) {
        gap: 20px;
      }
    }

    .upload-box {
      width: 100%;
      border: 2px dashed #cbd5e0;
      border-radius: 10px;
      padding: 25px;
      text-align: center;
      margin-bottom: 15px;
      background-color: #f7fafc;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;

      @media (min-width: 768px) {
        padding: 30px;
        margin-bottom: 20px;
      }

      &:hover {
        border-color: #4299e1;
        background-color: #ebf8ff;
      }

      .upload-icon {
        font-size: 35px;
        color: #4299e1;
        margin-bottom: 10px;

        @media (min-width: 768px) {
          font-size: 40px;
          margin-bottom: 12px;
        }
      }

      p {
        color: #718096;
        margin: 0;
        font-size: 0.9rem;
        line-height: 1.4;

        @media (min-width: 768px) {
          font-size: 0.95rem;
        }
      }

      &.drag-active {
        border-color: #4299e1;
        background-color: #ebf8ff;

        .upload-icon {
          color: #2b6cb0;
        }
      }
    }

    .upload-button {
      width: 100%;
      padding: 10px;
      font-weight: 600;
      font-size: 0.9rem;
      border-radius: 8px;
      text-transform: none;
      background-color: #000000;
      color: white;
      transition: all 0.3s ease;

      @media (min-width: 768px) {
        padding: 12px;
        font-size: 0.95rem;
      }

      &:hover {
        background-color: #000000;
        transform: translateY(-1px);
      }

      &[disabled] {
        background-color: #cbd5e0;
        cursor: not-allowed;
      }
    }

    .status-message,
    .error-message {
      font-size: 0.9rem;
      text-align: center;
      margin-top: 10px;
      font-weight: 500;
      padding: 8px 16px;
      border-radius: 8px;
      width: 100%;

      @media (min-width: 768px) {
        font-size: 0.95rem;
        padding: 10px 16px;
      }
    }

    .status-message {
      color: #38a169;
      background-color: #e6f4ea;
    }

    .error-message {
      color: #e53e3e;
      background-color: #fee2e2;
    }
  }

  @media (min-width: 768px) {
    .upload-section {
      margin: 0 10px;
    }
  }
}