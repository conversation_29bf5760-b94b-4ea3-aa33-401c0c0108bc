import { Box, Button, Typography, TypographyProps } from "@mui/material";
import { styled } from "@mui/material/styles";

// Common container styles for all table pages
export const PageContainer = styled(Box)({
  padding: '16px 0',
});

// Common header container styles
export const HeaderContainer = styled(Box)({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  marginBottom: '16px',
  marginTop: 0,
  paddingBottom: '12px',
  borderBottom: '1px solid #e2e8f0'
});

// Common title styles
export const PageTitle = styled(Typography)<TypographyProps>(({ theme }) => ({
  fontWeight: 600,
  fontSize: '20px',
  color: '#1a202c',
  letterSpacing: '-0.5px',
  fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif',
}));

// Common action button styles
export const ActionButton = styled(Button)({
  backgroundColor: 'rgb(0, 0, 0)',
  color: 'rgb(255, 255, 255)',
  fontSize: "14px",
  lineHeight: "24px",
  padding: '8px 12px',
  borderRadius: '4px',
  textTransform: 'none',
  fontWeight: 400,
  boxShadow: 'none',
  fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif',
  '&:hover': {
    backgroundColor: 'rgb(0, 0, 0)',
  }
});

// Common filter container styles
export const FilterContainer = styled(Box)({
  display: "flex",
  alignItems: "center",
  marginBottom: '16px'
});

// Common filter text styles
export const FilterText = styled(Typography)({
  color: '#4a5568',
  fontSize: '13px'
});

// Common reset button styles
export const ResetButton = styled(Button)({
  marginLeft: '8px',
  color: '#0f172a',
  fontSize: '13px',
  textTransform: 'none',
  fontWeight: 500,
  padding: '2px 8px',
  minHeight: '24px',
  '&:hover': {
    backgroundColor: 'rgba(0, 0, 0, 0.04)',
  }
});

// Common button group container
export const ButtonGroupContainer = styled(Box)({
  display: 'flex',
  gap: '16px'
}); 