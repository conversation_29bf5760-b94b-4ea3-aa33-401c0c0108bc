// Test cases for routing schema import/export functionality

export const testCases = {
  // Test Case 1: Simple linear flow
  simpleLinearFlow: {
    id: 1,
    name: "Simple Linear Flow",
    code: "simple_linear",
    schema: {
      routing_schema: {
        id: "r1",
        name: "Simple Linear Flow",
        type: "manufacturing",
        product_id: "1",
        components: {
          start_node: {
            position: { x: 100, y: 100 },
            node_type: "machine",
            event_required: false,
            name: "Start Node"
          },
          middle_node: {
            position: { x: 300, y: 100 },
            node_type: "machine",
            event_required: false,
            name: "Middle Node"
          },
          end_node: {
            position: { x: 500, y: 100 },
            node_type: "machine",
            event_required: false,
            name: "End Node"
          }
        },
        route: {
          start: "start_node",
          end: "end_node",
          connections: {
            start_node: {
              towards: {
                default: "middle_node",
                conditions: [],
                route_type: "main"
              }
            },
            middle_node: {
              towards: {
                default: "end_node",
                conditions: [],
                route_type: "main"
              }
            },
            end_node: {
              towards: {
                default: "end",
                end: true,
                conditions: null,
                route_type: "main"
              }
            }
          }
        }
      }
    }
  },

  // Test Case 2: Flow with conditional branches
  conditionalFlow: {
    id: 2,
    name: "Conditional Flow",
    code: "conditional_flow",
    schema: {
      routing_schema: {
        id: "r2",
        name: "Conditional Flow",
        type: "manufacturing",
        product_id: "2",
        components: {
          start_node: {
            position: { x: 100, y: 100 },
            node_type: "machine",
            event_required: false,
            name: "Start Node"
          },
          inspection_node: {
            position: { x: 300, y: 100 },
            node_type: "machine",
            event_required: true,
            name: "Inspection Node"
          },
          rework_node: {
            position: { x: 300, y: 250 },
            node_type: "rework_station",
            event_required: true,
            name: "Rework Node"
          },
          end_node: {
            position: { x: 500, y: 100 },
            node_type: "machine",
            event_required: false,
            name: "End Node"
          }
        },
        route: {
          start: "start_node",
          end: "end_node",
          connections: {
            start_node: {
              towards: {
                default: "inspection_node",
                conditions: [],
                route_type: "main"
              }
            },
            inspection_node: {
              towards: {
                default: "end_node",
                conditions: [
                  {
                    left: {
                      node: "inspection_node",
                      path: "inspection_status",
                      type: "property"
                    },
                    right: {
                      type: "value",
                      value: false
                    },
                    target: "rework_node",
                    operator: "equals",
                    route_type: "rework"
                  }
                ],
                route_type: "main"
              }
            },
            rework_node: {
              towards: {
                default: "inspection_node",
                conditions: [],
                route_type: "rework"
              }
            },
            end_node: {
              towards: {
                default: "end",
                end: true,
                conditions: null,
                route_type: "main"
              }
            }
          }
        }
      }
    }
  },

  // Test Case 3: Complex flow with multiple conditional paths
  complexFlow: {
    id: 3,
    name: "Complex Flow",
    code: "complex_flow",
    schema: {
      routing_schema: {
        id: "r3",
        name: "Complex Flow",
        type: "manufacturing",
        product_id: "3",
        components: {
          pcb_load: {
            position: { x: 100, y: 100 },
            node_type: "machine",
            event_required: false,
            name: "PCB Load"
          },
          solder_paste: {
            position: { x: 250, y: 100 },
            node_type: "machine",
            event_required: false,
            name: "Solder Paste"
          },
          spi: {
            position: { x: 400, y: 100 },
            node_type: "machine",
            event_required: true,
            name: "SPI"
          },
          rework_spi: {
            position: { x: 400, y: 200 },
            node_type: "rework_station",
            event_required: true,
            name: "Rework SPI"
          },
          pick_place: {
            position: { x: 550, y: 100 },
            node_type: "machine",
            event_required: false,
            name: "Pick & Place"
          },
          reflow: {
            position: { x: 700, y: 100 },
            node_type: "machine",
            event_required: false,
            name: "Reflow"
          },
          aoi: {
            position: { x: 850, y: 100 },
            node_type: "machine",
            event_required: true,
            name: "AOI"
          },
          rework_aoi: {
            position: { x: 850, y: 200 },
            node_type: "rework_station",
            event_required: true,
            name: "Rework AOI"
          },
          final_test: {
            position: { x: 1000, y: 100 },
            node_type: "machine",
            event_required: true,
            name: "Final Test"
          }
        },
        route: {
          start: "pcb_load",
          end: "final_test",
          connections: {
            pcb_load: {
              towards: {
                default: "solder_paste",
                conditions: [],
                route_type: "main"
              }
            },
            solder_paste: {
              towards: {
                default: "spi",
                conditions: [],
                route_type: "main"
              }
            },
            spi: {
              towards: {
                default: "pick_place",
                conditions: [
                  {
                    left: {
                      node: "spi",
                      path: "inspection_status",
                      type: "property"
                    },
                    right: {
                      type: "value",
                      value: false
                    },
                    target: "rework_spi",
                    operator: "equals",
                    route_type: "rework"
                  }
                ],
                route_type: "main"
              }
            },
            rework_spi: {
              towards: {
                default: "spi",
                conditions: [],
                route_type: "rework"
              }
            },
            pick_place: {
              towards: {
                default: "reflow",
                conditions: [],
                route_type: "main"
              }
            },
            reflow: {
              towards: {
                default: "aoi",
                conditions: [],
                route_type: "main"
              }
            },
            aoi: {
              towards: {
                default: "final_test",
                conditions: [
                  {
                    left: {
                      node: "aoi",
                      path: "inspection_status",
                      type: "property"
                    },
                    right: {
                      type: "value",
                      value: false
                    },
                    target: "rework_aoi",
                    operator: "equals",
                    route_type: "rework"
                  }
                ],
                route_type: "main"
              }
            },
            rework_aoi: {
              towards: {
                default: "aoi",
                conditions: [],
                route_type: "rework"
              }
            },
            final_test: {
              towards: {
                default: "end",
                end: true,
                conditions: null,
                route_type: "main"
              }
            }
          }
        }
      }
    }
  },

  // Test Case 4: Multiple nodes connecting to end
  multipleEndConnections: {
    id: 4,
    name: "Multiple End Connections",
    code: "multi_end",
    schema: {
      routing_schema: {
        id: "r4",
        name: "Multiple End Connections",
        type: "manufacturing",
        product_id: "4",
        components: {
          start_node: {
            position: { x: 100, y: 100 },
            node_type: "machine",
            event_required: false,
            name: "Start Node"
          },
          process_a: {
            position: { x: 300, y: 50 },
            node_type: "machine",
            event_required: false,
            name: "Process A"
          },
          process_b: {
            position: { x: 300, y: 150 },
            node_type: "machine",
            event_required: false,
            name: "Process B"
          },
          end_node: {
            position: { x: 500, y: 100 },
            node_type: "machine",
            event_required: false,
            name: "End Node"
          }
        },
        route: {
          start: "start_node",
          end: "end_node",
          connections: {
            start_node: {
              towards: {
                default: "process_a",
                conditions: [
                  {
                    left: {
                      node: "start_node",
                      path: "route_selection",
                      type: "property"
                    },
                    right: {
                      type: "value",
                      value: "B"
                    },
                    target: "process_b",
                    operator: "equals",
                    route_type: "main"
                  }
                ],
                route_type: "main"
              }
            },
            process_a: {
              towards: {
                default: "end",
                conditions: [],
                route_type: "main"
              }
            },
            process_b: {
              towards: {
                default: "end",
                conditions: [],
                route_type: "main"
              }
            },
            end_node: {
              towards: {
                default: "end",
                end: true,
                conditions: null,
                route_type: "main"
              }
            }
          }
        }
      }
    }
  },

  // Test Case 5: Your PCB Routing example
  pcbRouting: {
    id: 1,
    name: "PCB Routing",
    code: "pcb_route",
    schema: {
        routing_schema: {
            id: "r1",
            name: "PCB Routing",
            type: "no-idea-for-now",
            route: {
                end: "move_to_store",
                start: "pcb_load_feeder",
                connections: {
                    "aoi": {
                        "towards": {
                            "default": "pb_pcb_unload",
                            "conditions": [
                                {
                                    "left": {
                                        "node": "aoi",
                                        "path": "inspection_status",
                                        "type": "property"
                                    },
                                    "right": {
                                        "type": "value",
                                        "value": false
                                    },
                                    "target": "pb_rework_aoi",
                                    "operator": "equals",
                                    "route_type": "main"
                                }
                            ],
                            "route_type": "main"
                        }
                    },
                    "fpt": {
                        "towards": {
                            "default": "pb_testing",
                            "conditions": [],
                            "route_type": "main"
                        }
                    },
                    "pdi": {
                        "towards": {
                            "default": "sap_confirmation_ii",
                            "conditions": [],
                            "route_type": "main"
                        }
                    },
                    "spi": {
                        "towards": {
                            "default": "pick_place",
                            "conditions": [],
                            "route_type": "main"
                        }
                    },
                    "reflow": {
                        "towards": {
                            "default": "aoi",
                            "conditions": [],
                            "route_type": "main"
                        }
                    },
                    "touch_up": {
                        "towards": {
                            "default": "pb_post_wave",
                            "conditions": [],
                            "route_type": "main"
                        }
                    },
                    "pb_depanel": {
                        "towards": {
                            "default": "pb_thc_mounting",
                            "conditions": [],
                            "route_type": "main"
                        }
                    },
                    "pb_testing": {
                        "towards": {
                            "default": "conformal_coating",
                            "conditions": [
                                {
                                    "left": {
                                        "node": "pb_testing",
                                        "path": "inspection_status",
                                        "type": "property"
                                    },
                                    "right": {
                                        "type": "value",
                                        "value": false
                                    },
                                    "target": "pb_rework_1",
                                    "operator": "equals",
                                    "route_type": "main"
                                }
                            ],
                            "route_type": "main"
                        }
                    },
                    "pick_place": {
                        "towards": {
                            "default": "reflow",
                            "conditions": [],
                            "route_type": "main"
                        }
                    },
                    "pb_pre_wave": {
                        "towards": {
                            "default": "wave_soldring",
                            "conditions": [
                                {
                                    "left": {
                                        "node": "pb_pre_wave",
                                        "path": "inspection_status",
                                        "type": "property"
                                    },
                                    "right": {
                                        "type": "value",
                                        "value": false
                                    },
                                    "target": "pb_pre_wave",
                                    "operator": "equals",
                                    "route_type": "rework"
                                }
                            ],
                            "route_type": "main"
                        }
                    },
                    "pb_rework_1": {
                        "towards": {
                            "default": "pb_testing",
                            "conditions": [],
                            "route_type": "rework"
                        }
                    },
                    "lead_cutting": {
                        "towards": {
                            "default": "touch_up",
                            "conditions": [],
                            "route_type": "main"
                        }
                    },
                    "pb_post_wave": {
                        "towards": {
                            "default": "sap_confirmation",
                            "conditions": [
                                {
                                    "left": {
                                        "node": "pb_post_wave",
                                        "path": "inspection_status",
                                        "type": "property"
                                    },
                                    "right": {
                                        "type": "value",
                                        "value": false
                                    },
                                    "target": "pb_rework_pw",
                                    "operator": "equals",
                                    "route_type": "rework"
                                }
                            ],
                            "route_type": "main"
                        }
                    },
                    "pb_rework_pw": {
                        "towards": {
                            "default": "pb_post_wave",
                            "conditions": [],
                            "route_type": "rework"
                        }
                    },
                    "laser_marking": {
                        "towards": {
                            "default": "solder_paste_printing",
                            "conditions": [],
                            "route_type": "main"
                        }
                    },
                    "move_to_store": {
                        "towards": {
                            "end": true,
                            "default": "end",
                            "conditions": null,
                            "route_type": "main"
                        }
                    },
                    "pb_pcb_unload": {
                        "towards": {
                            "default": "pb_depanel",
                            "conditions": [],
                            "route_type": "main"
                        }
                    },
                    "pb_rework_aoi": {
                        "towards": {
                            "default": "aoi",
                            "conditions": [],
                            "route_type": "rework"
                        }
                    },
                    "wave_soldring": {
                        "towards": {
                            "default": "lead_cutting",
                            "conditions": [],
                            "route_type": "main"
                        }
                    },
                    "pb_thc_mounting": {
                        "towards": {
                            "default": "pb_pre_wave",
                            "conditions": [],
                            "route_type": "main"
                        }
                    },
                    "pcb_load_feeder": {
                        "towards": {
                            "default": "laser_marking",
                            "conditions": [],
                            "route_type": "main"
                        }
                    },
                    "sap_confirmation": {
                        "towards": {
                            "default": "fpt",
                            "conditions": [],
                            "route_type": "main"
                        }
                    },
                    "conformal_coating": {
                        "towards": {
                            "default": "conformal_inspection",
                            "conditions": [],
                            "route_type": "main"
                        }
                    },
                    "sap_confirmation_ii": {
                        "towards": {
                            "default": "move_to_store",
                            "conditions": [],
                            "route_type": "main"
                        }
                    },
                    "conformal_inspection": {
                        "towards": {
                            "default": "pdi",
                            "conditions": [],
                            "route_type": "main"
                        }
                    },
                    "solder_paste_printing": {
                        "towards": {
                            "default": "spi",
                            "conditions": [],
                            "route_type": "main"
                        }
                    }
                }
            },
            components: {
                "aoi": {
                    "position": {
                        "x": 957,
                        "y": -243
                    },
                    "node_type": "machine",
                    "event_required": false,
                    "name": "AOI"
                },
                "fpt": {
                    "position": {
                        "x": 1537,
                        "y": -72
                    },
                    "node_type": "machine",
                    "event_required": false,
                    "name": "FPT"
                },
                "pdi": {
                    "position": {
                        "x": 778,
                        "y": 168
                    },
                    "node_type": "machine",
                    "event_required": false,
                    "name": "PDI"
                },
                "spi": {
                    "position": {
                        "x": 170,
                        "y": -238
                    },
                    "node_type": "machine",
                    "event_required": false,
                    "name": "SPI"
                },
                "reflow": {
                    "position": {
                        "x": 686,
                        "y": -245
                    },
                    "node_type": "machine",
                    "event_required": false,
                    "name": "Reflow"
                },
                "touch_up": {
                    "position": {
                        "x": 681,
                        "y": -73
                    },
                    "node_type": "machine",
                    "event_required": false,
                    "name": "Touch Up"
                },
                "pb_depanel": {
                    "position": {
                        "x": 1537,
                        "y": -247
                    },
                    "node_type": "machine",
                    "event_required": false,
                    "name": "Depanelling"
                },
                "pb_testing": {
                    "position": {
                        "x": -200,
                        "y": 165
                    },
                    "node_type": "machine",
                    "event_required": true,
                    "name": "Manual Testing"
                },
                "pick_place": {
                    "position": {
                        "x": 411,
                        "y": -243
                    },
                    "node_type": "machine",
                    "event_required": false,
                    "name": "Pick and Place"
                },
                "pb_pre_wave": {
                    "position": {
                        "x": -295,
                        "y": -57
                    },
                    "node_type": "machine",
                    "event_required": true,
                    "name": "Pre wave WS"
                },
                "pb_rework_1": {
                    "position": {
                        "x": -628,
                        "y": 90
                    },
                    "node_type": "rework_station",
                    "event_required": true,
                    "name": "Rework Testing"
                },
                "lead_cutting": {
                    "position": {
                        "x": 357,
                        "y": -68
                    },
                    "node_type": "machine",
                    "event_required": false,
                    "name": "Lead Cutting"
                },
                "pb_post_wave": {
                    "position": {
                        "x": 954,
                        "y": -71
                    },
                    "node_type": "machine",
                    "event_required": true,
                    "name": "Post wave WS"
                },
                "pb_rework_pw": {
                    "position": {
                        "x": 800,
                        "y": 10
                    },
                    "node_type": "machine",
                    "event_required": false,
                    "name": "Rework Postwave"
                },
                "laser_marking": {
                    "position": {
                        "x": -384,
                        "y": -240
                    },
                    "node_type": "machine",
                    "event_required": false,
                    "name": "Laser Marking"
                },
                "move_to_store": {
                    "position": {
                        "x": 1379,
                        "y": 162
                    },
                    "node_type": "machine",
                    "event_required": false,
                    "name": "Move to Store"
                },
                "pb_pcb_unload": {
                    "position": {
                        "x": 1239,
                        "y": -248
                    },
                    "node_type": "machine",
                    "event_required": false,
                    "name": "Unload & Storage"
                },
                "pb_rework_aoi": {
                    "position": {
                        "x": 1026,
                        "y": -375
                    },
                    "node_type": "machine",
                    "event_required": false,
                    "name": "Rework AOI"
                },
                "wave_soldring": {
                    "position": {
                        "x": 32,
                        "y": -59
                    },
                    "node_type": "machine",
                    "event_required": false,
                    "name": "Wave Soldring"
                },
                "pb_thc_mounting": {
                    "position": {
                        "x": -628,
                        "y": -52
                    },
                    "node_type": "machine",
                    "event_required": false,
                    "name": "THC Mounting"
                },
                "pcb_load_feeder": {
                    "position": {
                        "x": -727,
                        "y": -237
                    },
                    "node_type": "machine",
                    "event_required": false,
                    "name": "PCB load on Feeder"
                },
                "sap_confirmation": {
                    "position": {
                        "x": 1235,
                        "y": -74
                    },
                    "node_type": "machine",
                    "event_required": false,
                    "name": "SAP Confirmation"
                },
                "conformal_coating": {
                    "position": {
                        "x": 139,
                        "y": 169
                    },
                    "node_type": "machine",
                    "event_required": false,
                    "name": "Conformal Coating"
                },
                "sap_confirmation_ii": {
                    "position": {
                        "x": 1083,
                        "y": 165
                    },
                    "node_type": "machine",
                    "event_required": false,
                    "name": "Sap Confirmation II"
                },
                "conformal_inspection": {
                    "position": {
                        "x": 455,
                        "y": 170
                    },
                    "node_type": "machine",
                    "event_required": false,
                    "name": "Conformal Inspection"
                },
                "solder_paste_printing": {
                    "position": {
                        "x": -106,
                        "y": -240
                    },
                    "node_type": "machine",
                    "event_required": false,
                    "name": "Solder Paste Printing"
                }
            },
            product_id: "2"
        }
    }
  }
};

// Function to run tests
export function runTests() {
  console.log('Starting routing schema tests...');
  
  // Test each case
  Object.keys(testCases).forEach(testName => {
    console.log(`\nTesting ${testName}...`);
    const testCase = testCases[testName];
    
    // Log the test case schema
    console.log('Test Schema:', JSON.stringify(testCase, null, 2));
    
    // Here you would normally:
    // 1. Import the schema into your application
    // 2. Export it again
    // 3. Compare the original and exported schemas
    
    console.log(`Test ${testName} completed`);
  });
  
  console.log('\nAll tests completed');
}
