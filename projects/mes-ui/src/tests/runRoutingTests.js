// Test runner for routing schema import/export functionality
import { testCases } from './routingSchemaTests.js';

// Function to test import/export cycle
async function testImportExportCycle() {
  console.log('Starting import/export cycle tests...');
  
  // Mock functions for React Flow state management
  const mockSetNodes = jest.fn();
  const mockSetEdges = jest.fn();
  const mockSetStartNodeId = jest.fn();
  const mockSetEndNodeId = jest.fn();
  
  // Import the ProcessRouteList2 component
  // Note: In a real test environment, you would use Jest and React Testing Library
  // This is a simplified version for demonstration
  
  try {
    // For each test case
    for (const testName of Object.keys(testCases)) {
      console.log(`\nTesting import/export cycle for ${testName}...`);
      const testCase = testCases[testName];
      
      // Step 1: Simulate importing the schema
      console.log('Step 1: Importing schema...');
      const importedSchema = JSON.stringify(testCase);
      
      // Step 2: Parse the schema and create nodes/edges
      console.log('Step 2: Parsing schema and creating nodes/edges...');
      const schema = JSON.parse(importedSchema);
      
      // Extract routing schema
      const routingSchema = schema.schema?.routing_schema;
      if (!routingSchema) {
        console.error('Invalid schema format');
        continue;
      }
      
      // Extract components and connections
      const components = routingSchema.components;
      const connections = routingSchema.route.connections;
      const startNode = routingSchema.route.start;
      const endNode = routingSchema.route.end;
      
      // Create nodes
      const nodes = Object.keys(components).map(componentId => {
        const component = components[componentId];
        return {
          id: componentId,
          type: 'customNode',
          position: component.position || { x: 100, y: 100 },
          data: {
            label: component.name || componentId,
            nodeType: component.node_type || 'default',
            eventRequired: component.event_required || false,
            isStartNode: componentId === startNode,
            isEndNode: componentId === endNode
          }
        };
      });
      
      // Create edges
      const edges = [];
      
      // Process default connections
      Object.keys(connections).forEach(sourceId => {
        const connection = connections[sourceId];
        if (connection.towards && connection.towards.default && connection.towards.default !== 'end') {
          edges.push({
            id: `edge-${sourceId}-${connection.towards.default}-default`,
            source: sourceId,
            target: connection.towards.default,
            type: 'custom',
            data: {
              connectionType: 'default',
              routeType: connection.towards.route_type || 'main'
            }
          });
        }
      });
      
      // Process conditional connections
      Object.keys(connections).forEach(sourceId => {
        const connection = connections[sourceId];
        if (connection.towards && connection.towards.conditions) {
          const conditions = Array.isArray(connection.towards.conditions) 
            ? connection.towards.conditions 
            : [];
          
          conditions.forEach((condition, index) => {
            if (condition && condition.target) {
              edges.push({
                id: `edge-${sourceId}-${condition.target}-conditional-${index}`,
                source: sourceId,
                target: condition.target,
                type: 'custom',
                data: {
                  connectionType: 'conditional',
                  routeType: condition.route_type || 'main',
                  operator: condition.operator || 'equals',
                  conditionPath: condition.left?.path || '',
                  conditionValue: condition.right?.value
                }
              });
            }
          });
        }
      });
      
      // Process end connections
      Object.keys(connections).forEach(sourceId => {
        const connection = connections[sourceId];
        if (connection.towards && connection.towards.default === 'end') {
          if (sourceId === endNode) {
            // Special end node connection
            edges.push({
              id: `edge-${sourceId}-end`,
              source: sourceId,
              target: sourceId,
              type: 'custom',
              data: {
                connectionType: 'end',
                routeType: 'main',
                isEndConnection: true
              }
            });
          } else {
            // Connection to the end node
            edges.push({
              id: `edge-${sourceId}-${endNode}-default`,
              source: sourceId,
              target: endNode,
              type: 'custom',
              data: {
                connectionType: 'default',
                routeType: connection.towards.route_type || 'main'
              }
            });
          }
        }
      });
      
      console.log(`Created ${nodes.length} nodes and ${edges.length} edges`);
      
      // Step 3: Generate schema from nodes/edges
      console.log('Step 3: Generating schema from nodes/edges...');
      
      // Create connections structure
      const generatedConnections = {};
      
      // Group edges by source
      const edgesBySource = {};
      edges.forEach(edge => {
        const sourceId = edge.source;
        if (!edgesBySource[sourceId]) {
          edgesBySource[sourceId] = [];
        }
        edgesBySource[sourceId].push(edge);
      });
      
      // Process edges for each source node
      Object.keys(edgesBySource).forEach(sourceId => {
        const sourceEdges = edgesBySource[sourceId];
        
        // Initialize connection structure
        generatedConnections[sourceId] = {
          towards: {
            conditions: [],
            route_type: "main",
            outputs: [],
            edges: []
          }
        };
        
        // Track if we've found a default connection
        let foundDefault = false;
        
        // Process each edge
        sourceEdges.forEach(edge => {
          const targetId = edge.target;
          const edgeData = edge.data || {};
          
          // Add to outputs list
          if (!generatedConnections[sourceId].towards.outputs.includes(targetId)) {
            generatedConnections[sourceId].towards.outputs.push(targetId);
          }
          
          // Add to edges list
          generatedConnections[sourceId].towards.edges.push({
            target: targetId,
            type: edgeData.connectionType || 'default',
            route_type: edgeData.routeType || 'main'
          });
          
          // Process based on connection type
          if (edgeData.connectionType === 'default') {
            if (!foundDefault) {
              generatedConnections[sourceId].towards.default = targetId;
              generatedConnections[sourceId].towards.route_type = edgeData.routeType || 'main';
              foundDefault = true;
            }
          } else if (edgeData.connectionType === 'conditional') {
            const condition = {
              left: {
                node: sourceId,
                path: edgeData.conditionPath || "inspection_status",
                type: "property"
              },
              right: {
                type: "value",
                value: edgeData.conditionValue !== undefined ? edgeData.conditionValue : false
              },
              target: targetId,
              operator: edgeData.operator || "equals",
              route_type: edgeData.routeType || "main"
            };
            
            generatedConnections[sourceId].towards.conditions.push(condition);
          }
        });
      });
      
      // Special handling for end node
      nodes.forEach(node => {
        const nodeId = node.id;
        
        if (!generatedConnections[nodeId]) {
          // Node has no outgoing connections
          generatedConnections[nodeId] = {
            towards: {
              default: nodeId === endNode ? "end" : "",
              conditions: [],
              route_type: "main",
              outputs: [],
              edges: []
            }
          };
        }
        
        // Special handling for end node
        if (nodeId === endNode) {
          generatedConnections[nodeId] = {
            towards: {
              default: "end",
              end: true,
              conditions: [],
              route_type: "main",
              outputs: ["end"],
              edges: [{ target: "end", type: "default", route_type: "main" }]
            }
          };
        }
      });
      
      // Create the generated schema
      const generatedSchema = {
        id: schema.id || 1,
        name: schema.name || "Generated Schema",
        code: schema.code || "generated_code",
        schema: {
          routing_schema: {
            id: routingSchema.id || `r${Date.now().toString().substring(5)}`,
            name: routingSchema.name || "Generated Schema",
            type: routingSchema.type || "manufacturing",
            product_id: routingSchema.product_id || "1",
            components: {},
            route: {
              start: startNode,
              end: endNode,
              connections: generatedConnections
            }
          }
        }
      };
      
      // Add components to the schema
      nodes.forEach(node => {
        const nodeId = node.id;
        generatedSchema.schema.routing_schema.components[nodeId] = {
          position: {
            x: Math.round(node.position.x),
            y: Math.round(node.position.y)
          },
          node_type: node.data.nodeType || "machine",
          event_required: node.data.eventRequired || false,
          name: node.data.label
        };
      });
      
      // Step 4: Compare original and generated schemas
      console.log('Step 4: Comparing original and generated schemas...');
      
      // Compare route connections
      const originalConnections = routingSchema.route.connections;
      let connectionsMatch = true;
      
      // Check if all original connections exist in generated schema
      Object.keys(originalConnections).forEach(sourceId => {
        if (!generatedConnections[sourceId]) {
          console.error(`Missing connection for source ${sourceId} in generated schema`);
          connectionsMatch = false;
          return;
        }
        
        const originalConnection = originalConnections[sourceId].towards;
        const generatedConnection = generatedConnections[sourceId].towards;
        
        // Check default connection
        if (originalConnection.default !== generatedConnection.default) {
          console.error(`Default connection mismatch for ${sourceId}: 
            Original: ${originalConnection.default}, 
            Generated: ${generatedConnection.default}`);
          connectionsMatch = false;
        }
        
        // Check route_type
        if (originalConnection.route_type !== generatedConnection.route_type) {
          console.error(`Route type mismatch for ${sourceId}: 
            Original: ${originalConnection.route_type}, 
            Generated: ${generatedConnection.route_type}`);
          connectionsMatch = false;
        }
        
        // Check conditions (simplified check)
        const originalConditionsCount = Array.isArray(originalConnection.conditions) 
          ? originalConnection.conditions.length 
          : 0;
        const generatedConditionsCount = generatedConnection.conditions.length;
        
        if (originalConditionsCount !== generatedConditionsCount) {
          console.error(`Conditions count mismatch for ${sourceId}: 
            Original: ${originalConditionsCount}, 
            Generated: ${generatedConditionsCount}`);
          connectionsMatch = false;
        }
      });
      
      // Check if all nodes are preserved
      const originalComponentCount = Object.keys(components).length;
      const generatedComponentCount = Object.keys(generatedSchema.schema.routing_schema.components).length;
      
      if (originalComponentCount !== generatedComponentCount) {
        console.error(`Component count mismatch: 
          Original: ${originalComponentCount}, 
          Generated: ${generatedComponentCount}`);
        connectionsMatch = false;
      }
      
      // Report test result
      if (connectionsMatch) {
        console.log(`✅ Test ${testName} PASSED: Schema preserved through import/export cycle`);
      } else {
        console.error(`❌ Test ${testName} FAILED: Schema not preserved through import/export cycle`);
      }
    }
    
    console.log('\nAll import/export cycle tests completed');
    
  } catch (error) {
    console.error('Error running tests:', error);
  }
}

// Run the tests
testImportExportCycle();

export {
  testImportExportCycle
};
