// Simple test runner for routing schema tests
import { testCases } from './routingSchemaTests.js';

// Function to run a specific test case
function runTest(testName) {
  if (!testCases[testName]) {
    console.error(`Test case "${testName}" not found!`);
    console.log(`Available test cases: ${Object.keys(testCases).join(', ')}`);
    return;
  }
  
  console.log(`\n========================================`);
  console.log(`RUNNING TEST: ${testName}`);
  console.log(`========================================\n`);
  
  const testCase = testCases[testName];
  
  // Display the test case JSON
  console.log('TEST CASE JSON:');
  console.log(JSON.stringify(testCase, null, 2));
  
  console.log('\nTEST INSTRUCTIONS:');
  console.log('1. Copy the above JSON');
  console.log('2. Open ProcessRouteList2 page in your application');
  console.log('3. Click "Import JSON" and paste the JSON');
  console.log('4. Verify the routing diagram is created correctly with all nodes and connections');
  console.log('5. Click "Generate JSON" and compare with the original JSON');
  console.log('6. Verify that:');
  console.log('   - All nodes are correctly imported with proper positions');
  console.log('   - All connections (default and conditional) are correctly created');
  console.log('   - Start and end nodes are properly designated');
  console.log('   - Main route is sequential from start to end');
  console.log('   - Rework and conditional paths are preserved');
}

// Function to run all test cases
function runAllTests() {
  console.log('RUNNING ALL ROUTING SCHEMA TESTS');
  
  Object.keys(testCases).forEach((testName, index) => {
    console.log(`\n[Test ${index + 1} of ${Object.keys(testCases).length}]`);
    runTest(testName);
    console.log('\n-------------------------------------------\n');
  });
  
  console.log('ALL TESTS COMPLETED');
}

// Get test name from command line arguments
const testName = process.argv[2];

if (testName) {
  // Run specific test
  runTest(testName);
} else {
  // Run all tests
  runAllTests();
}
