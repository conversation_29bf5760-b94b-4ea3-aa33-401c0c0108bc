import React, { createContext, useContext, useState } from "react";

const AuthContext = createContext<any>(null);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [isOneFormUser, setIsOneFormUser] = useState(false);
  const [authState, setAuthState] = useState({
    accessToken: localStorage.getItem("accessToken") || null,
    refreshToken: localStorage.getItem("refreshToken") || null,
    user: JSON.parse(localStorage.getItem("user") || "null"),
  });

  const setAuthData = (
    accessToken: string,
    refreshToken: string,
    user: any
  ) => {
    localStorage.setItem("accessToken", accessToken);
    localStorage.setItem("refreshToken", refreshToken);
    localStorage.setItem("user", JSON.stringify(user));
    setAuthState({ accessToken, refreshToken, user });
  };

  const clearAuthData = () => {
    localStorage.removeItem("accessToken");
    localStorage.removeItem("refreshToken");
    localStorage.removeItem("user");
    setAuthState({ accessToken: null, refreshToken: null, user: null });
  };

  return (
    <AuthContext.Provider
      value={{
        authState,
        setAuthData,
        clearAuthData,
        setIsOneFormUser,
        isOneFormUser,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
