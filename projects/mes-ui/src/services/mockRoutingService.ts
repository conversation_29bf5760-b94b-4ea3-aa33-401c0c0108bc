import { RoutingItem, RoutingResponse, RoutingFilter } from '../hooks/useRoutingTable';

// Mock routing data
const mockRoutings: RoutingItem[] = [
  {
    id: 1,
    name: 'PCB Assembly Routing',
    code: 'pcb-assembly',
    schema: {
      routing_schema: {
        id: 'r1',
        name: 'PCB Assembly Routing',
        type: 'manufacturing',
        components: {
          pb_pre_wave: {
            position: { x: 20, y: 120 },
            node_type: 'machine',
            event_required: false,
            name: 'Pre Wave Station',
          },
          pb_post_wave: {
            position: { x: 240, y: 130 },
            node_type: 'machine',
            event_required: false,
            name: 'Post wave WS',
          },
          pb_testing: {
            position: { x: 480, y: 140 },
            node_type: 'machine',
            event_required: false,
            name: 'Testing WS',
          },
        },
        route: {
          start: 'pb_pre_wave',
          end: 'pb_testing',
          connections: {
            pb_pre_wave: {
              towards: {
                default: 'pb_post_wave',
                conditions: null,
                route_type: 'main',
              },
            },
            pb_post_wave: {
              towards: {
                default: 'pb_testing',
                conditions: null,
                route_type: 'main',
              },
            },
            pb_testing: {
              towards: {
                default: 'end',
                end: true,
                conditions: null,
                route_type: 'main',
              },
            },
          },
        },
      },
    },
    products_details: [
      {
        id: 1,
        name: 'PCB Board Type A',
        code: 'PCB-A',
      },
      {
        id: 2,
        name: 'PCB Board Type B',
        code: 'PCB-B',
      },
    ],
    created_by: {
      id: 1,
      username: 'admin',
    },
    created_at: '2023-04-05T10:30:45.123Z',
  },
  {
    id: 2,
    name: 'SMT Production Line',
    code: 'smt-prod',
    schema: {
      routing_schema: {
        id: 'r2',
        name: 'SMT Production Line',
        type: 'manufacturing',
        components: {
          smt_screen_printer: {
            position: { x: 20, y: 120 },
            node_type: 'machine',
            event_required: false,
            name: 'Screen Printer',
          },
          smt_pick_place: {
            position: { x: 240, y: 130 },
            node_type: 'machine',
            event_required: false,
            name: 'Pick and Place',
          },
          smt_reflow: {
            position: { x: 480, y: 140 },
            node_type: 'machine',
            event_required: false,
            name: 'Reflow Oven',
          },
          smt_aoi: {
            position: { x: 720, y: 150 },
            node_type: 'machine',
            event_required: false,
            name: 'AOI Inspection',
          },
        },
        route: {
          start: 'smt_screen_printer',
          end: 'smt_aoi',
          connections: {
            smt_screen_printer: {
              towards: {
                default: 'smt_pick_place',
                conditions: null,
                route_type: 'main',
              },
            },
            smt_pick_place: {
              towards: {
                default: 'smt_reflow',
                conditions: null,
                route_type: 'main',
              },
            },
            smt_reflow: {
              towards: {
                default: 'smt_aoi',
                conditions: null,
                route_type: 'main',
              },
            },
            smt_aoi: {
              towards: {
                default: 'end',
                end: true,
                conditions: null,
                route_type: 'main',
              },
            },
          },
        },
      },
    },
    products_details: [
      {
        id: 3,
        name: 'SMT Board Type C',
        code: 'SMT-C',
      },
    ],
    created_by: {
      id: 1,
      username: 'admin',
    },
    created_at: '2023-04-10T14:20:30.456Z',
  },
  {
    id: 3,
    name: 'Solder Paste Routing',
    code: 'sp-route',
    schema: {
      routing_schema: {
        id: 'r3',
        name: 'Solder Paste Routing',
        type: 'consumable-mt-route',
        components: {
          sp_packet: {
            position: { x: 0, y: 0 },
            node_type: 'scanner',
            event_required: false,
            name: 'SP Packet',
          },
          frige_i: {
            position: { x: 400, y: 0 },
            node_type: 'machine',
            event_required: true,
            name: 'SP Fridge In',
          },
          sp_normalizer: {
            position: { x: 800, y: 0 },
            node_type: 'machine',
            event_required: true,
            name: 'SP Normalizer',
          },
          sp_mixer: {
            position: { x: 1200, y: 0 },
            node_type: 'machine',
            event_required: true,
            name: 'SP Mixer',
          },
          sp_visc_meter: {
            position: { x: 1600, y: 0 },
            node_type: 'machine',
            event_required: true,
            name: 'SP Viscosity Meter',
          },
          spp: {
            position: { x: 2000, y: 0 },
            node_type: 'machine',
            event_required: false,
            name: 'SPP',
          },
        },
        route: {
          start: 'sp_packet',
          end: 'spp',
          connections: {
            sp_packet: {
              towards: {
                default: 'frige_i',
                conditions: null,
                route_type: 'main',
              },
            },
            frige_i: {
              towards: {
                default: 'sp_normalizer',
                conditions: null,
                route_type: 'main',
              },
            },
            sp_normalizer: {
              towards: {
                default: 'sp_mixer',
                conditions: null,
                route_type: 'main',
              },
            },
            sp_mixer: {
              towards: {
                default: 'sp_visc_meter',
                conditions: null,
                route_type: 'main',
              },
            },
            sp_visc_meter: {
              towards: {
                default: 'spp',
                conditions: null,
                route_type: 'main',
              },
            },
            spp: {
              towards: {
                default: 'end',
                end: true,
                conditions: null,
                route_type: 'main',
              },
            },
          },
        },
      },
    },
    products_details: [
      {
        id: 10,
        name: 'Solder Paste Make (OM-340)',
        code: 'SP_OM-340',
      },
    ],
    created_by: {
      id: 3,
      username: 'admin',
    },
    created_at: '2023-04-06T22:49:45.632Z',
  },
];

// Function to filter and sort routings
const filterAndSortRoutings = (
  routings: RoutingItem[],
  filters: RoutingFilter
): { filteredRoutings: RoutingItem[]; totalCount: number } => {
  let filteredRoutings = [...routings];

  // Apply search filter
  if (filters.search) {
    const searchTerm = filters.search.toLowerCase();
    filteredRoutings = filteredRoutings.filter(
      (routing) =>
        routing.name.toLowerCase().includes(searchTerm) ||
        routing.code.toLowerCase().includes(searchTerm)
    );
  }

  // Apply form_code filter
  if (filters.form_code) {
    const formCode = filters.form_code.toLowerCase();
    filteredRoutings = filteredRoutings.filter((routing) =>
      routing.code.toLowerCase().includes(formCode)
    );
  }

  // Apply product_code filter
  if (filters.product_code) {
    const productCode = filters.product_code.toLowerCase();
    filteredRoutings = filteredRoutings.filter((routing) =>
      routing.products_details?.some((product) =>
        product.code.toLowerCase().includes(productCode)
      )
    );
  }

  // Apply routing_code filter
  if (filters.routing_code) {
    const routingCode = filters.routing_code.toLowerCase();
    filteredRoutings = filteredRoutings.filter((routing) =>
      routing.code.toLowerCase().includes(routingCode)
    );
  }

  // Apply sorting
  if (filters.ordering) {
    const isDesc = filters.ordering.startsWith('-');
    const field = isDesc ? filters.ordering.substring(1) : filters.ordering;

    filteredRoutings.sort((a: any, b: any) => {
      let valueA = a[field];
      let valueB = b[field];

      // Handle nested fields
      if (field.includes('.')) {
        const parts = field.split('.');
        valueA = parts.reduce((obj, part) => obj?.[part], a);
        valueB = parts.reduce((obj, part) => obj?.[part], b);
      }

      // Handle string comparison
      if (typeof valueA === 'string' && typeof valueB === 'string') {
        return isDesc
          ? valueB.localeCompare(valueA)
          : valueA.localeCompare(valueB);
      }

      // Handle number comparison
      return isDesc ? valueB - valueA : valueA - valueB;
    });
  }

  const totalCount = filteredRoutings.length;

  // Apply pagination
  if (filters.page !== undefined && filters.page_size !== undefined) {
    const startIndex = (filters.page - 1) * filters.page_size;
    filteredRoutings = filteredRoutings.slice(
      startIndex,
      startIndex + filters.page_size
    );
  }

  return { filteredRoutings, totalCount };
};

// Mock function to fetch routings
export const mockFetchRoutings = (
  filters: RoutingFilter = {}
): Promise<RoutingResponse> => {
  return new Promise((resolve) => {
    // Simulate API delay
    setTimeout(() => {
      const { filteredRoutings, totalCount } = filterAndSortRoutings(
        mockRoutings,
        filters
      );

      const response: RoutingResponse = {
        count: totalCount,
        next: null,
        previous: null,
        results: filteredRoutings,
      };

      resolve(response);
    }, 500); // Simulate 500ms delay
  });
};

// Mock function to fetch a single routing by ID
export const mockFetchRoutingById = (id: number): Promise<RoutingItem> => {
  return new Promise((resolve, reject) => {
    // Simulate API delay
    setTimeout(() => {
      const routing = mockRoutings.find((r) => r.id === id);

      if (routing) {
        resolve(routing);
      } else {
        reject(new Error(`Routing with ID ${id} not found`));
      }
    }, 300); // Simulate 300ms delay
  });
};

// Mock function to delete a routing by ID
export const mockDeleteRouting = (id: number): Promise<{ success: boolean; message: string }> => {
  return new Promise((resolve, reject) => {
    // Simulate API delay
    setTimeout(() => {
      const index = mockRoutings.findIndex((r) => r.id === id);

      if (index !== -1) {
        // In a real implementation, we would make an API call to delete the routing
        // Here we're just removing it from our mock array
        mockRoutings.splice(index, 1);
        resolve({
          success: true,
          message: `Routing with ID ${id} has been successfully deleted`
        });
      } else {
        reject(new Error(`Routing with ID ${id} not found`));
      }
    }, 500); // Simulate 500ms delay
  });
};
