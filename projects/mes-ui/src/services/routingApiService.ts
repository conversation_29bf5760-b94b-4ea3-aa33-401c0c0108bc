// src/services/routingApiService.ts

// For development mode check
declare const process: { env: { NODE_ENV: string } };

// Define the NodeInfo interface
export interface NodeInfo {
  id: string;
  label: string;
  nodeType: string;
}

// Define the routing API service
export const routingApiService = {
  saveRoutingSchema: async (schema: any) => {
    try {
      console.log('Saving routing schema to API:', schema);
      // Simulate API call with a delay
      await new Promise(resolve => setTimeout(resolve, 800));
      return {
        success: true,
        message: 'Routing schema saved successfully',
        data: {
          id: schema.routing_schema.id,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      console.error('Error saving routing schema:', error);
      return {
        success: false,
        message: 'Failed to save routing schema',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  },

  getProcessBlocks: async () => {
    try {
      console.log('Fetching process blocks from API');
      // Simulate API call with a delay
      await new Promise(resolve => setTimeout(resolve, 600));

      // Return mock process blocks in the format expected by NodePalette
      return {
        success: true,
        data: [
          { id: 'machine_1', label: 'Machine 1', nodeType: 'machine' },
          { id: 'machine_2', label: 'Machine 2', nodeType: 'machine' },
          { id: 'machine_3', label: 'Machine 3', nodeType: 'machine' },
          { id: 'inspection_1', label: 'Inspection Station 1', nodeType: 'inspection' },
          { id: 'inspection_2', label: 'Inspection Station 2', nodeType: 'inspection' },
          { id: 'testing_1', label: 'Testing Station 1', nodeType: 'testing' },
          { id: 'testing_2', label: 'Testing Station 2', nodeType: 'testing' },
          { id: 'rework_1', label: 'Rework Station 1', nodeType: 'rework_station' },
          { id: 'rework_2', label: 'Rework Station 2', nodeType: 'rework_station' },
          { id: 'load_1', label: 'Load Station', nodeType: 'load' },
          { id: 'unload_1', label: 'Unload Station', nodeType: 'unload' },
          { id: 'storage_1', label: 'Storage Area 1', nodeType: 'storage' }
        ]
      };
    } catch (error) {
      console.error('Error fetching process blocks:', error);
      return {
        success: false,
        message: 'Failed to fetch process blocks',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
};
