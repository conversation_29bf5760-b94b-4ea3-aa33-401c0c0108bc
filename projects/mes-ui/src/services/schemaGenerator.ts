// src/services/schemaGenerator.ts
import { Node, Edge } from 'reactflow';
import { v4 as uuidv4 } from 'uuid';

interface SchemaGenerationOptions {
  configName: string;
  configCode: string;
  startNodeId: string;
  endNodeId: string;
}

/**
 * Validates if a JSON string has balanced brackets
 * @param jsonString The JSON string to validate
 * @returns True if brackets are balanced, false otherwise
 */
export const validateJsonBrackets = (jsonString: string): boolean => {
  const stack: string[] = [];
  const openBrackets = ['(', '[', '{'];
  const closeBrackets = [')', ']', '}'];
  const bracketPairs: Record<string, string> = {
    ')': '(',
    ']': '[',
    '}': '{'
  };

  for (let i = 0; i < jsonString.length; i++) {
    const char = jsonString[i];

    if (openBrackets.includes(char)) {
      stack.push(char);
    } else if (closeBrackets.includes(char)) {
      const lastOpenBracket = stack.pop();
      if (lastOpenBracket !== bracketPairs[char]) {
        return false;
      }
    }
  }

  return stack.length === 0;
};

/**
 * Generates a routing schema from nodes and edges
 * @param nodes The nodes in the flow
 * @param edges The edges in the flow
 * @param options Options for schema generation
 * @returns The generated schema
 */
export const generateSchema = (
  nodes: Node[],
  edges: Edge[],
  options: SchemaGenerationOptions
) => {
  const { configName, configCode, startNodeId, endNodeId } = options;

  // Create a unique ID for the schema
  const schemaId = `r${Date.now()}`;

  // Initialize the schema structure
  const schema = {
    name: configName,
    code: configCode || configName.toLowerCase().replace(/\\s+/g, '_'),
    schema: {
      routing_schema: {
        id: schemaId,
        name: configName,
        type: 'manufacturing',
        version: '1.1',
        route: {
          start: startNodeId,
          end: endNodeId,
          connections: {} as Record<string, any>
        },
        components: {} as Record<string, any>
      }
    }
  };

  // Process nodes to create components
  nodes.forEach(node => {
    const nodeId = node.id;
    const nodeData = node.data || {};

    schema.schema.routing_schema.components[nodeId] = {
      position: {
        x: node.position.x,
        y: node.position.y
      },
      node_type: nodeData.nodeType || 'process',
      event_required: nodeData.eventRequired || false,
      name: nodeData.label || nodeId,
      handles: {
        inputs: ['input-0', 'input-1', 'input-2', 'input-3'],
        outputs: ['output-0', 'output-1', 'output-2', 'output-3']
      }
    };
  });

  // Process edges to create connections
  const connectionsBySource: Record<string, any> = {};

  edges.forEach(edge => {
    const sourceId = edge.source;
    const targetId = edge.target;
    const edgeData = edge.data || {};

    // Skip if source or target is missing
    if (!sourceId || !targetId) return;

    // Initialize connection for this source if it doesn't exist
    if (!connectionsBySource[sourceId]) {
      connectionsBySource[sourceId] = {
        default: null,
        conditions: []
      };
    }

    // Handle different connection types
    if (edgeData.connectionType === 'default') {
      connectionsBySource[sourceId].default = targetId;
      connectionsBySource[sourceId].route_type = edgeData.routeType || 'main';
      connectionsBySource[sourceId].edge_type = edgeData.edgeType || 'bezier';
      connectionsBySource[sourceId].sourceHandle = edge.sourceHandle || 'output-0';
      connectionsBySource[sourceId].targetHandle = edge.targetHandle || 'input-0';
    } else if (edgeData.connectionType === 'conditional') {
      connectionsBySource[sourceId].conditions.push({
        left: {
          node: sourceId,
          path: edgeData.conditionPath || 'condition_path_placeholder',
          type: 'property'
        },
        right: {
          type: 'value',
          value: edgeData.conditionValue || 'condition_value_placeholder'
        },
        target: targetId,
        operator: edgeData.operator || 'equals',
        route_type: edgeData.routeType || 'main',
        edge_type: edgeData.edgeType || 'bezier',
        sourceHandle: edge.sourceHandle || 'output-1',
        targetHandle: edge.targetHandle || 'input-1',
        metadata: {
          label: `Conditional connection from ${sourceId} to ${targetId}`,
          description: `Condition: ${edgeData.conditionPath || 'condition_path_placeholder'} ${edgeData.operator || 'equals'} ${edgeData.conditionValue || 'condition_value_placeholder'}`
        }
      });
    }
  });

  // Add connections to the schema
  Object.entries(connectionsBySource).forEach(([sourceId, connection]) => {
    schema.schema.routing_schema.route.connections[sourceId] = {
      towards: {
        ...connection,
        metadata: {
          label: connection.default
            ? `Connection from ${sourceId} to ${connection.default}`
            : `Terminal connection from ${sourceId}`,
          description: connection.default
            ? `Default ${connection.route_type || 'main'} route with ${connection.edge_type || 'bezier'} style`
            : `Terminal connection with no specific target`
        }
      }
    };

    // If this is the end node, mark it as end
    if (sourceId === endNodeId) {
      schema.schema.routing_schema.route.connections[sourceId].towards.end = true;
      // Ensure the end node has a default connection to "end"
      schema.schema.routing_schema.route.connections[sourceId].towards.default = "end";
    }
  });

  // Ensure the end node is included in connections even if it has no outgoing edges
  if (endNodeId && !schema.schema.routing_schema.route.connections[endNodeId]) {
    schema.schema.routing_schema.route.connections[endNodeId] = {
      towards: {
        default: "end",
        end: true,
        conditions: [],
        route_type: "main",
        edge_type: "bezier",
        sourceHandle: "output-0",
        targetHandle: "input-0",
        metadata: {
          label: `Terminal connection from ${endNodeId}`,
          description: "Terminal connection to end"
        }
      }
    };
  }

  return schema;
};

/**
 * Parses a JSON schema string and validates it
 * @param jsonString The JSON string to parse
 * @returns The parsed schema or null if invalid
 */
export const parseJsonSchema = (jsonString: string): any => {
  try {
    // Validate brackets first for a quick check
    if (!validateJsonBrackets(jsonString)) {
      throw new Error('JSON has unbalanced brackets');
    }

    // Parse the JSON
    const schema = JSON.parse(jsonString);

    // Validate schema structure
    if (!schema || !schema.schema || !schema.schema.routing_schema) {
      throw new Error('Invalid schema structure: missing schema.routing_schema');
    }

    // Validate required fields
    const routingSchema = schema.schema.routing_schema;
    if (!routingSchema.route || !routingSchema.components) {
      throw new Error('Invalid schema structure: missing route or components');
    }

    return schema;
  } catch (error) {
    console.error('Error parsing JSON schema:', error);
    throw error;
  }
};

export default {
  generateSchema,
  validateJsonBrackets,
  parseJsonSchema
};
