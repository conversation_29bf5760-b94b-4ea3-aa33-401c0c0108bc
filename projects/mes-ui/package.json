{"name": "admiin-dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@mui/icons-material": "^6.1.5", "@mui/lab": "^6.0.0-beta.25", "@mui/material": "^6.4.2", "@mui/x-date-pickers": "^7.25.0", "@tanstack/react-query": "^5.61.5", "axios": "^1.7.8", "date-fns": "^2.29.3", "elkjs": "^0.9.3", "react": "^18.3.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.5", "react-icons": "^5.3.0", "react-router-dom": "^6.27.0", "reactflow": "^11.11.4", "sass": "^1.80.4", "uuid": "^11.1.0", "yup": "^1.4.0"}, "devDependencies": {"@eslint/js": "^9.13.0", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^9.13.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.13", "globals": "^15.11.0", "typescript": "~5.6.2", "typescript-eslint": "^8.10.0", "vite": "^5.4.9"}}