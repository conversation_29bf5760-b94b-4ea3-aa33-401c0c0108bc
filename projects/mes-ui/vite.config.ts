import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";

export default defineConfig({
  plugins: [react()],
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@use "./src/styles/mixins.scss" as *;`,
      },
    },
  },
  server: {
    proxy: {
      "/mes_trace": {
        target: "https://backend.smartmfg.in/", // Backend base URL
        changeOrigin: true, // Ensures the Host header matches the target
      },
      "/api/analytics/dashboard/": {
        target: "http://localhost:3001", // Backend base URL
        changeOrigin: true, // Ensures the Host header matches the target
      },
    },
  },
});
