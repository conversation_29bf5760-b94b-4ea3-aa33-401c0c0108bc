import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";

export default defineConfig({
  plugins: [react()],
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@use "./src/styles/mixins.scss" as *;`,
      },
    },
  },
  server: {
    proxy: {
      "/mes_trace": {
        target: "https://backend.smartmfg.in/", // Backend base URL
        changeOrigin: true, // Ensures the Host header matches the target
      },
      "/generateEmbedUrl": {
        target: "https://qol7pj7y4i.execute-api.ap-south-1.amazonaws.com", // New target URL
        changeOrigin: true, // Ensures the Host header matches the target
        rewrite: (path) => path.replace(/^\/generateEmbedUrl/, ""), // Removes the /generateEmbedUrl prefix in the path when forwarding
      },
    },
  },
});
