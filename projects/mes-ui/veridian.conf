server {
	listen 80;
	location / {
		root /usr/share/nginx/html;
		index index.html index.htm;
		try_files $uri $uri/ /index.html =404;
	}
	  # Proxy setup for "/generateEmbedUrl"
	  location /default/GenerateEmbedUrlForAnonymousUser {
		proxy_pass https://5owof4lxqd.execute-api.ap-south-1.amazonaws.com/default/GenerateEmbedUrlForAnonymousUser;
	}
	location /mes_trace {
		proxy_pass http://mes-backend:8000;
		proxy_set_header Host $host;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header X-Forwarded-Proto $scheme;
	}
}