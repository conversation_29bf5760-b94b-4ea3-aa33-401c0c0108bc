# Use the official Node.js image as the base image
FROM node:18-alpine AS builder

# Set the working directory inside the container
WORKDIR /app

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy the rest of the application files
COPY . .

# Build the Vite app
RUN npm run build

# Use a lightweight web server for serving the app
FROM nginx:alpine AS production

# Copy the build output to the nginx HTML directory
COPY --from=builder /app/dist /usr/share/nginx/html
COPY ./veridian.conf /etc/nginx/conf.d/default.conf

# Expose the port on which the app runs
EXPOSE 80

# Start the nginx server
CMD ["nginx", "-g", "daemon off;"]
